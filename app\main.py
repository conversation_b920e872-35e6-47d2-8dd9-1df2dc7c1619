"""
Selda AI Sales Autopilot - Main FastAPI Application
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import time
import logging
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.exceptions import setup_exception_handlers
from app.shared.middleware import (
    LoggingMiddleware,
    SecurityHeadersMiddleware,
    RateLimitMiddleware
)
from app.shared.auth_middleware import (
    AuthContextMiddleware,
    RLSEnforcementMiddleware
)

# Import routers
from app.modules.auth.router import router as auth_router
from app.modules.users.router import router as users_router
# from app.modules.campaigns.router import router as campaigns_router
from app.modules.campaigns.workflow_router import router as workflow_router
from app.modules.agents.chat_router import router as chat_router
from app.modules.agents.goal_workflow_router import router as goal_workflow_router
from app.modules.leads.router import apollo_router
from app.modules.leads.crm_router import router as leads_crm_router
from app.modules.agents.router import router as agents_router
from app.modules.payments.router import router as payments_router
# from app.modules.analytics.router import router as analytics_router

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Selda AI Sales Autopilot Backend")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    
    # Initialize services here
    # await initialize_database()
    # await initialize_redis()
    # await initialize_agents()
    
    yield
    
    # Shutdown
    logger.info("Shutting down Selda AI Sales Autopilot Backend")
    # Cleanup resources here
    # await cleanup_database()
    # await cleanup_redis()


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI-powered sales automation platform with multi-agent system",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.debug else ["yourdomain.com", "*.yourdomain.com"]
)

# Custom middleware (order matters - auth middleware should be early)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RLSEnforcementMiddleware)  # Enforce RLS requirements
app.add_middleware(AuthContextMiddleware)     # Set auth context
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitMiddleware)


# Exception handlers
setup_exception_handlers(app)

# Add rate limit exception handler
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


# Health check endpoints
@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.app_version,
        "environment": settings.environment
    }


@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with service status"""
    # TODO: Add actual service health checks
    services = {
        "database": "healthy",  # Check Supabase connection
        "redis": "healthy",     # Check Redis connection
        "openai": "healthy",    # Check OpenAI API
        "apollo": "healthy",    # Check Apollo.io API
        "stripe": "healthy",    # Check Stripe API
    }
    
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.app_version,
        "environment": settings.environment,
        "services": services
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to Selda AI Sales Autopilot",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "Documentation not available in production"
    }


# Include routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
# app.include_router(campaigns_router, prefix="/api/v1/campaigns", tags=["Campaigns"])
app.include_router(workflow_router, prefix="/api/v1", tags=["Campaign Workflow"])
app.include_router(chat_router, prefix="/api/v1", tags=["Chat Interface"])
app.include_router(goal_workflow_router, prefix="/api/v1", tags=["Goal Workflow"])
app.include_router(apollo_router, prefix="/api/v1/apollo", tags=["Apollo.io"])
app.include_router(leads_crm_router, prefix="/api/v1/leads", tags=["Leads CRM"])
app.include_router(agents_router, prefix="/api/v1/agents", tags=["Agents"])
app.include_router(payments_router, prefix="/api/v1/payments", tags=["Payments"])
# app.include_router(analytics_router, prefix="/api/v1/analytics", tags=["Analytics"])


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
