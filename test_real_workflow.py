#!/usr/bin/env python3
"""
Test the real workflow with proper database setup
"""

import sys
import asyncio

# Add the app directory to the path
sys.path.append('.')

async def test_real_goal_understanding():
    """Test goal understanding with real database"""
    try:
        print("🧪 Testing Real Goal Understanding Agent...")
        
        # Use the fixed demo IDs
        org_id = "00000000-0000-4000-8000-000000000001"
        user_id = "00000000-0000-4000-8000-000000000002"
        
        print(f"Organization ID: {org_id}")
        print(f"User ID: {user_id}")
        
        # Import goal understanding agent
        from app.modules.agents.goal_understanding import GoalUnderstandingAgent
        
        # Create agent
        agent = GoalUnderstandingAgent(org_id)
        print(f"✅ Agent created: {agent.name}")
        
        # Test goal
        goal = "I want 2 restaurant clients in London"
        print(f"\n🎯 Processing goal: '{goal}'")
        
        # Execute agent
        result = await agent.execute({
            "goal_description": goal,
            "user_id": user_id,
            "organization_id": org_id,
            "conversation_history": [],
            "is_follow_up": False,
            "context": None,
            "target_audience": None,
            "timeline": None,
            "budget": None,
            "answered_questions": {}
        })
        
        print(f"\n✅ Goal processing successful!")
        print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
        print(f"   Complete: {result.get('is_complete', False)}")
        
        parsed_goal = result.get('parsed_goal', {})
        print(f"   Objective: {parsed_goal.get('objective', 'Not specified')}")
        
        if parsed_goal.get('target_audience'):
            ta = parsed_goal['target_audience']
            print(f"   Industry: {ta.get('industry', 'Not specified')}")
            print(f"   Geography: {ta.get('geography', 'Not specified')}")
        
        # Check if we have next agent input
        next_input = result.get('next_agent_input')
        if next_input:
            print(f"✅ Ready for next agent!")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_simple_workflow():
    """Test the simple workflow manager"""
    try:
        print("\n🧪 Testing Simple Workflow Manager...")
        
        from app.modules.agents.simple_workflow import simple_workflow_manager
        
        # Set the organization ID
        simple_workflow_manager.organization_id = "00000000-0000-4000-8000-000000000001"
        
        goal = "I want 3 restaurant clients in London"
        print(f"Testing workflow with goal: '{goal}'")
        
        result = await simple_workflow_manager.start_from_goal(goal, "00000000-0000-4000-8000-000000000002")
        
        if result["success"]:
            print(f"✅ Workflow started successfully!")
            
            workflow_info = result.get("workflow", {})
            if workflow_info:
                print(f"   Workflow ID: {workflow_info.get('workflow_id', 'Not found')}")
                print(f"   Campaign ID: {workflow_info.get('campaign_id', 'Not found')}")
            
            goal_result = result.get("goal_understanding", {})
            if goal_result:
                print(f"   Goal Confidence: {goal_result.get('confidence_score', 0):.1%}")
            
            return result
        else:
            print(f"❌ Workflow failed: {result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Main test function"""
    print("🚀 Testing Real Workflow with Database")
    print("=" * 50)
    
    # Test 1: Goal Understanding
    goal_result = await test_real_goal_understanding()
    
    # Test 2: Simple Workflow (only if goal understanding works)
    if goal_result:
        workflow_result = await test_simple_workflow()
        
        if workflow_result:
            print(f"\n🎉 All tests passed! The real workflow is working.")
        else:
            print(f"\n⚠️  Goal understanding works, but workflow failed.")
    else:
        print(f"\n❌ Goal understanding failed.")


if __name__ == "__main__":
    asyncio.run(main())
