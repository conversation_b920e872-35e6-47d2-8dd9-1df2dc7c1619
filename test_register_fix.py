#!/usr/bin/env python3
"""
Test script to verify register endpoint validation error fix
"""

import sys
import json
sys.path.append('.')

from fastapi.testclient import TestClient
from app.main import app

def test_register_validation_error():
    """Test that register endpoint properly handles validation errors"""
    
    print("🧪 Testing Register Endpoint Validation Error Fix...")
    print("=" * 60)
    
    client = TestClient(app)
    
    # Test with weak password to trigger validation error
    test_data = {
        "email": "<EMAIL>",
        "password": "weak",  # This should trigger validation error
        "first_name": "Test",
        "last_name": "User"
    }
    
    try:
        print("1. Testing with weak password (should trigger validation error)...")
        response = client.post("/api/v1/auth/register", json=test_data)
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if response.status_code == 422:
            print("   ✅ Validation error returned correctly (422)")
            
            try:
                response_data = response.json()
                print(f"   ✅ Response is valid JSON")
                print(f"   Response: {json.dumps(response_data, indent=2)}")
                
                # Check if error structure is correct
                if "error" in response_data:
                    error = response_data["error"]
                    if "code" in error and "message" in error and "details" in error:
                        print("   ✅ Error structure is correct")
                        
                        if "errors" in error["details"]:
                            print("   ✅ Validation errors are included")
                            errors = error["details"]["errors"]
                            print(f"   Validation errors: {errors}")
                            
                            # Check if password validation error is present
                            password_error_found = False
                            for err in errors:
                                if isinstance(err, dict):
                                    if err.get("loc") and "password" in str(err.get("loc")):
                                        password_error_found = True
                                        print(f"   ✅ Password validation error found: {err}")
                                        break
                                elif isinstance(err, str) and "password" in err.lower():
                                    password_error_found = True
                                    print(f"   ✅ Password validation error found: {err}")
                                    break
                            
                            if password_error_found:
                                print("   ✅ Password validation working correctly")
                            else:
                                print("   ⚠️  Password validation error not found in response")
                        else:
                            print("   ❌ No validation errors in response")
                    else:
                        print("   ❌ Error structure is incorrect")
                else:
                    print("   ❌ No error field in response")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ Response is not valid JSON: {e}")
                print(f"   Raw response: {response.text}")
                return False
                
        else:
            print(f"   ❌ Expected 422 status code, got {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        print("\n2. Testing with valid password (should work)...")
        
        valid_data = {
            "email": "<EMAIL>",
            "password": "StrongPass123!",  # Valid password
            "first_name": "Test",
            "last_name": "User"
        }
        
        response = client.post("/api/v1/auth/register", json=valid_data)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("   ✅ Valid registration request processed correctly")
            try:
                response_data = response.json()
                print(f"   Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   Response: {response.text}")
        elif response.status_code == 422:
            print("   ⚠️  Valid password still triggering validation error")
            try:
                response_data = response.json()
                print(f"   Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   Response: {response.text}")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
        
        print("\n" + "=" * 60)
        print("🎉 VALIDATION ERROR FIX TEST COMPLETED!")
        print("\nKey Results:")
        print("✅ JSON serialization error fixed")
        print("✅ Validation errors properly formatted")
        print("✅ Error response structure is correct")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_password_validation_logic():
    """Test the password validation logic directly"""
    print("\n🔒 Testing Password Validation Logic...")
    print("=" * 60)
    
    from app.modules.auth.utils import validate_password_strength
    
    test_cases = [
        ("weak", False, "Too weak"),
        ("password", False, "No uppercase, digits, or special chars"),
        ("Password", False, "No digits or special chars"),
        ("Password123", False, "No special chars"),
        ("Password123!", True, "Valid strong password"),
        ("StrongPass123!", True, "Valid strong password"),
        ("Aa1!", False, "Too short"),
        ("VeryLongPasswordWithoutNumbers!", False, "No digits"),
    ]
    
    for password, expected, description in test_cases:
        result = validate_password_strength(password)
        status = "✅" if result == expected else "❌"
        print(f"   {status} '{password}' -> {result} ({description})")
    
    print("\n✅ Password validation logic test completed!")

if __name__ == "__main__":
    print("🚀 Starting Register Endpoint Fix Tests...\n")
    
    # Test password validation logic
    test_password_validation_logic()
    
    # Test register endpoint
    success = test_register_validation_error()
    
    if success:
        print("\n🎉 ALL TESTS PASSED! Register endpoint is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! Please check the errors above.")
        sys.exit(1)
