#!/usr/bin/env python3
"""
Interactive Chat Interface for Selda AI Goal Workflow
Simulates real user experience with terminal-based conversation
"""

import asyncio
import sys
import os
import json
from typing import Dict, Any, Optional

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent
from app.core.database import get_database

class SeldaChatInterface:
    """Interactive chat interface for Selda AI"""
    
    def __init__(self):
        self.db = None
        self.organization_id = "00000000-0000-4000-8000-000000000001"  # Test org ID
        
    async def initialize(self):
        """Initialize database connection"""
        self.db = await get_database()
        
    def print_banner(self):
        """Print welcome banner"""
        print("\n" + "="*70)
        print("🤖 SELDA AI - Interactive Lead Sourcing Assistant")
        print("="*70)
        print("Welcome! I'm your AI assistant for sourcing high-quality sales leads.")
        print("I'll help you:")
        print("  • Understand your sales goals")
        print("  • Ask clarifying questions if needed")
        print("  • Source targeted leads from Apollo.io")
        print("  • Provide quality scoring and filtering")
        print("\n💡 Example goals:")
        print("  • 'I need 10 meetings with restaurant owners in London'")
        print("  • 'Find me tech startup CEOs in San Francisco for 5 meetings'")
        print("  • 'Source leads for SaaS companies in New York'")
        print("\nType 'quit', 'exit', or 'help' for assistance.")
        print("-"*70)
        
    def print_separator(self, title: str = ""):
        """Print section separator"""
        if title:
            print(f"\n{'='*20} {title} {'='*20}")
        else:
            print("\n" + "-"*60)
            
    def get_user_input(self, prompt: str) -> str:
        """Get user input with nice formatting"""
        print(f"\n💬 {prompt}")
        print("👤 ", end="", flush=True)
        user_input = input().strip()

        # Handle help command
        if user_input.lower() == 'help':
            self.show_help()
            return self.get_user_input(prompt)

        return user_input

    def show_help(self):
        """Show help information"""
        print("\n" + "="*50)
        print("📚 HELP - How to use Selda AI")
        print("="*50)
        print("🎯 Goal Examples:")
        print("  • 'I need 5 meetings with restaurant owners in London'")
        print("  • 'Find 10 tech CEOs in San Francisco'")
        print("  • 'Source leads for healthcare companies in Boston'")
        print("\n📝 What I need to know:")
        print("  • Industry (restaurants, technology, healthcare, etc.)")
        print("  • Location (city, country, or region)")
        print("  • Number of meetings you want")
        print("  • Company size (optional: small, medium, large)")
        print("\n⌨️ Commands:")
        print("  • 'help' - Show this help")
        print("  • 'quit' or 'exit' - End session")
        print("  • Just type naturally - I'll understand!")
        print("-"*50)
        
    def print_ai_response(self, message: str):
        """Print AI response with nice formatting"""
        print(f"🤖 {message}")
        
    async def handle_goal_understanding(self, initial_goal: str) -> Optional[Dict[str, Any]]:
        """Handle the goal understanding phase"""
        self.print_separator("GOAL UNDERSTANDING")
        
        goal_agent = GoalUnderstandingAgent(
            organization_id=self.organization_id,
            db=self.db
        )
        
        # Process initial goal
        self.print_ai_response("Let me understand your goal...")
        
        goal_input = {
            "goal_description": initial_goal,
            "answers": {}
        }
        
        result = await goal_agent.execute(goal_input)
        
        # Check if goal is complete
        if result.get("complete", False):
            self.print_ai_response("Perfect! I have all the information I need:")
            print(f"   🎯 Industry: {result.get('industry', 'N/A')}")
            print(f"   📍 Location: {result.get('location', 'N/A')}")
            print(f"   🔢 Target: {result.get('target_meetings', 'N/A')} meetings")
            print(f"   ✅ Confidence: {result.get('confidence', 0):.1f}%")
            return result
        
        # Handle follow-up questions
        follow_up_questions = result.get("follow_up_questions", [])
        if follow_up_questions:
            self.print_ai_response("I need some additional information to help you better:")
            
            answers = {}
            for i, question in enumerate(follow_up_questions, 1):
                answer = self.get_user_input(f"({i}/{len(follow_up_questions)}) {question}")
                
                # Simple mapping of questions to answer keys
                if "industry" in question.lower():
                    answers["industry"] = answer
                elif "location" in question.lower():
                    answers["location"] = answer
                elif "meeting" in question.lower() or "target" in question.lower():
                    answers["target_meetings"] = answer
                elif "company size" in question.lower():
                    answers["company_size"] = answer
                else:
                    # Generic answer storage
                    answers[f"question_{i}"] = answer
            
            # Process follow-up answers
            self.print_ai_response("Thank you! Let me process your answers...")
            
            follow_up_input = {
                "goal_description": initial_goal,
                "answers": answers
            }
            
            final_result = await goal_agent.execute(follow_up_input)
            
            if final_result.get("complete", False):
                self.print_ai_response("Excellent! Now I have everything I need:")
                print(f"   🎯 Industry: {final_result.get('industry', 'N/A')}")
                print(f"   📍 Location: {final_result.get('location', 'N/A')}")
                print(f"   🔢 Target: {final_result.get('target_meetings', 'N/A')} meetings")
                print(f"   ✅ Confidence: {final_result.get('confidence', 0):.1f}%")
                return final_result
            else:
                self.print_ai_response("I still need more information. Let me ask a few more questions...")
                return await self.handle_goal_understanding(initial_goal)  # Recursive for additional rounds
        
        return None
        
    async def handle_lead_sourcing(self, goal_result: Dict[str, Any]) -> Dict[str, Any]:
        """Handle the lead sourcing phase"""
        self.print_separator("LEAD SOURCING")
        
        self.print_ai_response("Now I'll source leads for your goal. This may take a moment...")
        
        lead_agent = LeadSourcingAgent(
            organization_id=self.organization_id,
            db=self.db
        )
        
        # Calculate lead target (10 leads per meeting target)
        target_meetings = goal_result.get("target_meetings", 5)
        lead_count_target = target_meetings * 1  # Reduced for testing
        
        # Prepare lead sourcing input
        sourcing_input = {
            "industry": goal_result.get("industry", ""),
            "location": goal_result.get("location", ""),
            "lead_count_target": lead_count_target,
            "quality_threshold": 50,
            "sources": ["apollo"],
            "company_size": goal_result.get("company_size", "any")
        }
        
        print(f"\n🔍 Sourcing {lead_count_target} leads for {target_meetings} meetings...")
        print(f"   🎯 Industry: {sourcing_input['industry']}")
        print(f"   📍 Location: {sourcing_input['location']}")
        print(f"   🏢 Company Size: {sourcing_input['company_size']}")
        print(f"   📊 Quality Threshold: {sourcing_input['quality_threshold']}%")

        # Show progress
        print(f"\n⏳ Processing...")
        print("   🔍 Searching Apollo.io database...")
        print("   📧 Enriching contact emails...")
        print("   🎯 Filtering for quality...")
        print("   💾 Storing leads...")

        # Execute lead sourcing
        sourcing_result = await lead_agent.execute(sourcing_input)
        
        # Display results
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        duplicates = sourcing_result.get("duplicates_filtered", 0)
        cooldown = sourcing_result.get("cooldown_filtered", 0)
        
        self.print_ai_response("Lead sourcing completed! Here are the results:")
        print(f"   ✅ Leads sourced: {leads_sourced}")
        print(f"   🔄 Duplicates filtered: {duplicates}")
        print(f"   ⏰ Cooldown filtered: {cooldown}")
        
        # Show quality distribution
        quality_dist = sourcing_result.get("quality_distribution", {})
        if quality_dist:
            print(f"   📊 Quality distribution:")
            print(f"      🟢 High quality: {quality_dist.get('high', 0)} leads")
            print(f"      🟡 Medium quality: {quality_dist.get('medium', 0)} leads")
            print(f"      🔴 Low quality: {quality_dist.get('low', 0)} leads")
        
        # Show source breakdown
        sources = sourcing_result.get("leads_by_source", {})
        if sources:
            print(f"   🔗 Sources:")
            for source, count in sources.items():
                print(f"      📡 {source.title()}: {count} leads")
        
        return sourcing_result
        
    def display_final_summary(self, goal_result: Dict[str, Any], sourcing_result: Dict[str, Any]):
        """Display final summary"""
        self.print_separator("CAMPAIGN SUMMARY")
        
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        target_meetings = goal_result.get("target_meetings", 0)
        
        self.print_ai_response("🎉 Your lead sourcing campaign is complete!")
        print(f"\n📋 Campaign Details:")
        print(f"   🎯 Goal: {goal_result.get('goal_description', 'N/A')}")
        print(f"   🏭 Industry: {goal_result.get('industry', 'N/A')}")
        print(f"   🌍 Location: {goal_result.get('location', 'N/A')}")
        print(f"   🎯 Target Meetings: {target_meetings}")
        print(f"   📊 Leads Sourced: {leads_sourced}")
        
        if leads_sourced > 0:
            ratio = leads_sourced / target_meetings if target_meetings > 0 else 0
            print(f"   📈 Lead-to-Meeting Ratio: {ratio:.1f}:1")
            
            self.print_ai_response("✅ Success! Your leads are ready for outreach.")
            print("   💡 Next steps:")
            print("      1. Review the sourced leads in your dashboard")
            print("      2. Customize your outreach messages")
            print("      3. Start your email campaign")
            print("      4. Track responses and book meetings")
        else:
            self.print_ai_response("⚠️ No leads were sourced. This could be due to:")
            print("   • Very specific targeting criteria")
            print("   • API limitations or credits")
            print("   • Temporary service issues")
            print("   💡 Try adjusting your criteria or contact support.")
            
    async def run_chat_session(self):
        """Run the main chat session"""
        try:
            await self.initialize()
            self.print_banner()
            
            while True:
                # Get initial goal from user
                goal = self.get_user_input("What's your sales goal? (e.g., 'I need 5 meetings with restaurant owners in London')")
                
                if goal.lower() in ['quit', 'exit', 'bye']:
                    self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
                    break
                
                if not goal.strip():
                    self.print_ai_response("Please enter a valid goal description.")
                    continue
                
                try:
                    # Phase 1: Goal Understanding
                    goal_result = await self.handle_goal_understanding(goal)
                    
                    if not goal_result:
                        self.print_ai_response("❌ I couldn't understand your goal completely. Let's try again.")
                        continue
                    
                    # Phase 2: Lead Sourcing
                    sourcing_result = await self.handle_lead_sourcing(goal_result)
                    
                    # Phase 3: Final Summary
                    self.display_final_summary(goal_result, sourcing_result)
                    
                    # Ask if user wants to continue
                    self.print_separator()
                    continue_choice = self.get_user_input("Would you like to create another campaign? (yes/no)")
                    
                    if continue_choice.lower() not in ['yes', 'y', 'yeah', 'sure']:
                        self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
                        break
                        
                except Exception as e:
                    self.print_ai_response(f"❌ An error occurred: {str(e)}")
                    print("Let's try again with a different goal.")
                    continue
                    
        except KeyboardInterrupt:
            self.print_ai_response("\n👋 Session interrupted. Goodbye!")
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")
        finally:
            if self.db:
                await self.db.close()

async def main():
    """Main entry point"""
    chat = SeldaChatInterface()
    await chat.run_chat_session()

if __name__ == "__main__":
    print("🚀 Starting Selda AI Chat Interface...")
    asyncio.run(main())
