"""
Goal Understanding Agent - Agent 1
Interprets and clarifies sales goals from natural language input
"""

import logging
import json
from typing import Dict, Any, List
from datetime import datetime

from app.modules.agents.base import SeldaAgent, agent_registry
from app.modules.agents.models import (
    AgentType,
    GoalUnderstandingInput,
    GoalUnderstandingOutput
)

logger = logging.getLogger(__name__)


@agent_registry.register(AgentType.GOAL_UNDERSTANDING)
class GoalUnderstandingAgent(SeldaAgent):
    """Agent for interpreting and clarifying sales goals"""
    
    @property
    def agent_type(self) -> AgentType:
        return AgentType.GOAL_UNDERSTANDING
    
    @property
    def name(self) -> str:
        return "Goal Understanding Specialist"
    
    @property
    def description(self) -> str:
        return "Interprets natural language sales goals and extracts actionable parameters"
    
    def _get_agent_goal(self) -> str:
        return """Analyze natural language sales goals from chat interface, extract key parameters,
        ask intelligent follow-up questions for missing information, and ensure complete understanding
        before proceeding to next agent. Act as a conversational assistant that guides users through
        goal clarification."""
    
    def _get_agent_backstory(self) -> str:
        return """You are a conversational sales strategy consultant who specializes in understanding
        natural language sales goals through chat interactions. You excel at asking intelligent follow-up
        questions to clarify ambiguous goals, extract missing details, and ensure complete understanding
        before passing information to the next agent. You communicate in a friendly, professional manner
        and guide users through the goal clarification process step by step."""
    
    def get_capabilities(self) -> List[Dict[str, Any]]:
        return [
            {
                "name": "goal_parsing",
                "description": "Parse natural language goals into structured data",
                "parameters": {
                    "input": "Natural language goal description",
                    "output": "Structured goal parameters"
                }
            },
            {
                "name": "clarification_questions",
                "description": "Generate questions to clarify ambiguous goals",
                "parameters": {
                    "input": "Parsed goal data",
                    "output": "List of clarification questions"
                }
            },
            {
                "name": "metrics_suggestion",
                "description": "Suggest relevant success metrics",
                "parameters": {
                    "input": "Goal context and parameters",
                    "output": "List of suggested metrics"
                }
            }
        ]
    
    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute goal understanding logic: parse goals and prepare for next agent."""
        try:
            # Handle simple goal input (just a string) or complex input
            if isinstance(input_data, str):
                goal_description = input_data
                goal_input_data = {
                    "goal_description": goal_description,
                    "user_id": None,
                    "conversation_history": [],
                    "context": None,
                    "target_audience": None,
                    "timeline": None,
                    "budget": None,
                    "is_follow_up": False,
                    "answered_questions": {}
                }
            else:
                goal_input_data = input_data
                goal_description = goal_input_data.get("goal_description", "")

            goal_input = GoalUnderstandingInput(**goal_input_data)

            # Parse the goal using enhanced parsing
            parsed_goal = await self._parse_goal_enhanced(goal_input)

            # Calculate confidence score based on critical information
            confidence_score = self._calculate_confidence_score_enhanced(parsed_goal)

            # Determine what critical information is missing
            missing_critical_info = self._identify_missing_critical_information(parsed_goal)

            # Goal is complete only if we have industry and location (critical for lead sourcing)
            is_complete = len(missing_critical_info) == 0

            # Generate follow-up questions for missing critical information
            clarification_questions = []
            if not is_complete:
                clarification_questions = await self._generate_follow_up_questions(
                    parsed_goal, missing_critical_info
                )

            # Suggest success metrics
            suggested_metrics = await self._suggest_metrics(parsed_goal)

            # Generate next steps
            next_steps = await self._generate_next_steps(parsed_goal, confidence_score, is_complete)

            # Prepare input for next agent (only if complete)
            next_agent_input = None
            if is_complete:
                next_agent_input = {
                    "goal": parsed_goal,
                    "target_audience": parsed_goal.get("target_audience", {}),
                    "budget_constraints": parsed_goal.get("budget_constraints"),
                    "timeline_constraints": parsed_goal.get("timeline_constraints"),
                    "success_metrics": suggested_metrics,
                    "campaign_id": goal_input_data.get("campaign_id") if isinstance(goal_input_data, dict) else None,
                    "organization_id": goal_input_data.get("organization_id") if isinstance(goal_input_data, dict) else self.organization_id
                }

            # Create output
            output = GoalUnderstandingOutput(
                parsed_goal=parsed_goal,
                clarification_questions=clarification_questions,
                suggested_metrics=suggested_metrics,
                confidence_score=confidence_score,
                next_steps=next_steps,
                is_complete=is_complete,
                missing_information=missing_critical_info,
                next_agent_input=next_agent_input
            )

            return output.model_dump()

        except Exception as e:
            logger.error(f"Goal understanding execution failed: {e}")
            raise

    async def _parse_goal_enhanced(self, goal_input: GoalUnderstandingInput) -> Dict[str, Any]:
        """Enhanced goal parsing with AI-powered extraction of critical information"""
        try:
            # Use AI parsing for better extraction
            return await self._parse_goal(goal_input)
        except Exception as e:
            logger.error(f"Enhanced goal parsing failed: {e}")
            return self._fallback_goal_parsing_enhanced(goal_input)

    def _calculate_confidence_score_enhanced(self, parsed_goal: Dict[str, Any]) -> float:
        """Calculate confidence score based on presence of critical information"""
        score = 0.0

        # Critical information (required for lead sourcing)
        target_audience = parsed_goal.get("target_audience", {})
        if target_audience.get("industry"):
            score += 0.4  # Industry is critical
        if target_audience.get("geography"):
            score += 0.4  # Location is critical

        # Important but not critical information
        if parsed_goal.get("metrics", {}).get("target_value"):
            score += 0.1  # Target count
        if parsed_goal.get("timeline"):
            score += 0.05  # Timeline
        if parsed_goal.get("budget"):
            score += 0.05  # Budget

        return min(score, 1.0)

    def _identify_missing_critical_information(self, parsed_goal: Dict[str, Any]) -> List[str]:
        """Identify missing critical information needed for lead sourcing and email personalization"""
        missing = []

        target_audience = parsed_goal.get("target_audience", {})

        # Check if industry/target market is missing or unclear
        industry = target_audience.get("industry")
        if not industry or industry.lower() in ["not specified", "not clear", "unclear", "", "not provided"]:
            missing.append("industry")

        # Check if location is missing or unclear
        geography = target_audience.get("geography")
        if not geography or geography.lower() in ["not specified", "not clear", "unclear", "", "not provided"]:
            missing.append("location")

        # Enhanced purpose/service detection
        product_or_service = parsed_goal.get("product_or_service", "")
        missing_info = parsed_goal.get("missing_information", [])

        # Check if purpose/service is missing using multiple criteria
        purpose_missing = (
            "purpose" in missing_info or
            not product_or_service or
            product_or_service.lower() in [
                "not specified", "not clear", "unclear", "", "not provided",
                "not mentioned", "unknown", "n/a", "na"
            ]
        )

        if purpose_missing:
            missing.append("purpose")

        # Check for email personalization information
        value_proposition = parsed_goal.get("value_proposition", "")
        if not value_proposition or value_proposition.lower() in ["not specified", "not clear", "unclear", "", "not provided"]:
            missing.append("value_proposition")

        pain_points = parsed_goal.get("pain_points", [])
        if not pain_points or len(pain_points) == 0:
            missing.append("pain_points")

        company_info = parsed_goal.get("company_info", {})
        if not company_info.get("name") or not company_info.get("description"):
            missing.append("company_info")

        return missing

    async def _generate_follow_up_questions(
        self,
        parsed_goal: Dict[str, Any],
        missing_critical_info: List[str]
    ) -> List[str]:
        """Generate specific follow-up questions for missing critical information and email personalization"""
        questions = []

        if "industry" in missing_critical_info:
            questions.append(
                "What industry are you targeting? (e.g., restaurants, technology, healthcare, retail)"
            )

        if "location" in missing_critical_info:
            questions.append(
                "What geographic location should we focus on? (e.g., London, New York, California, UK)"
            )

        if "purpose" in missing_critical_info:
            questions.append(
                "What product or service are you looking to sell to these clients? (e.g., POS system, marketing services, software solution)"
            )

        if "value_proposition" in missing_critical_info:
            questions.append(
                "What's your main value proposition? What key benefit does your product/service provide? (e.g., 'Save 30% on operational costs', 'Increase efficiency by 50%')"
            )

        if "pain_points" in missing_critical_info:
            questions.append(
                "What are the main pain points your target customers face that your solution addresses? (e.g., 'Manual processes taking too much time', 'High operational costs')"
            )

        if "company_info" in missing_critical_info:
            questions.append(
                "Please provide your company name and a brief description of what your company does (this will be used in email personalization)"
            )

        return questions

    async def _parse_goal(self, goal_input: GoalUnderstandingInput) -> Dict[str, Any]:
        """Parse the goal using AI to extract structured information"""
        try:
            # Enable AI parsing with enhanced prompt for purpose/service detection
            logger.info("Using AI-powered goal parsing with enhanced purpose detection")

            # Build the prompt using string concatenation to avoid f-string issues
            goal_desc = goal_input.goal_description
            context = goal_input.context or 'Not provided'
            target_aud = goal_input.target_audience or 'Not specified'
            timeline = goal_input.timeline or 'Not specified'
            budget = goal_input.budget or 'Not specified'
            follow_up_answers = goal_input.answered_questions if goal_input.is_follow_up else 'None'

            prompt = f"""
            Analyze the following sales goal and extract structured information. Use AI reasoning to determine what information is missing and generate effective keywords for lead sourcing.

            Goal Description: {goal_desc}
            Context: {context}
            Target Audience: {target_aud}
            Timeline: {timeline}
            Budget: {budget}
            Follow-up Answers: {follow_up_answers}

            Extract and return ONLY a valid JSON object with the following structure (no additional text):

            """ + """{
                "objective": "Main sales objective (what the person wants to achieve)",
                "product_or_service": "What product/service is being offered (analyze from context)",
                "target_audience": {
                    "industry": "Target industry (be specific, e.g., restaurants, technology, healthcare)",
                    "company_size": "Company size range if specified",
                    "job_titles": ["List of target job titles like CEO, Owner, Manager"],
                    "geography": "Geographic focus (city, region, country)"
                },
                "metrics": {
                    "primary_metric": "Main success metric (usually meetings or clients)",
                    "target_value": "Target number (e.g., 5, 10, 20)",
                    "secondary_metrics": ["Additional metrics if any"]
                },
                "timeline": {
                    "duration": "Campaign duration if specified",
                    "start_date": "Preferred start date if mentioned",
                    "urgency": "How urgent this is"
                },
                "budget": {
                    "total_budget": "Total budget if specified",
                    "budget_allocation": "How budget should be allocated"
                },
                "constraints": ["Any constraints or limitations mentioned"],
                "success_criteria": ["What defines success for this goal"],
                "value_proposition": "Main value proposition or key benefit offered (extract from context)",
                "pain_points": ["List of customer pain points that the solution addresses"],
                "company_info": {
                    "name": "Company name if mentioned",
                    "description": "Brief company description if provided"
                },
                "apollo_keywords": {
                    "primary_keywords": "Best keywords for Apollo.io search based on goal context",
                    "related_keywords": ["Alternative keyword variations for broader search"],
                    "reasoning": "Why these keywords are most suitable for finding the right prospects"
                },
                "missing_information": ["Use AI reasoning to determine what critical info is truly missing"]
            }

            CRITICAL ANALYSIS RULES:
            1. Return ONLY valid JSON - no explanatory text before or after
            2. Use double quotes for all strings
            3. For "missing_information", be thorough in your analysis:
               - Include "industry" if you cannot determine the target market from context
               - Include "location" if geographic focus is completely unclear
               - Include "purpose" if the product/service being sold is not clear or not mentioned
               - Include "target_value" if no numeric target can be extracted
               - Include "value_proposition" if no clear value/benefit is mentioned
               - Include "pain_points" if no customer problems are identified
               - Include "company_info" if company name/description is missing
               - Be conservative - if something is unclear or vague, mark it as missing
            4. For "product_or_service", be specific:
               - If the goal mentions what they're selling, extract it clearly
               - If it's vague or not mentioned, set it to "Not specified"
               - Examples: "POS system", "AI diagnostic tool", "marketing services", "software solution"
            5. For "value_proposition", extract the main benefit:
               - Look for phrases like "save money", "increase efficiency", "reduce costs"
               - If not clear, set to "Not specified"
            6. For "pain_points", identify customer problems:
               - Look for problems the solution solves
               - Examples: ["Manual processes", "High costs", "Inefficient workflows"]
            7. For "apollo_keywords", prioritize broad, generic terms for better lead volume:
               - Use simple, generic industry terms (e.g., "hospital", "restaurant", "technology")
               - Avoid overly specific or compound phrases
               - Focus on single words or simple 2-word combinations
               - Prioritize terms that will return high volume of relevant prospects
            8. If this is a follow-up, incorporate the answered_questions into your analysis
            """

            # Use the LLM to parse the goal
            response = await self.llm.ainvoke(prompt)

            # Parse the JSON response
            try:
                # Clean the response content to ensure it's valid JSON
                response_content = response.content.strip()
                if response_content.startswith('```json'):
                    response_content = response_content.replace('```json', '').replace('```', '').strip()
                elif response_content.startswith('```'):
                    response_content = response_content.replace('```', '').strip()

                parsed_goal = json.loads(response_content)
                logger.info(f"AI parsing successful: {parsed_goal}")
                return parsed_goal
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed: {e}")
                logger.error(f"Raw response content: {response.content}")
                logger.info("Falling back to enhanced parsing")
                # Fallback parsing if JSON is malformed
                return self._fallback_goal_parsing_enhanced(goal_input)

            # Original AI parsing code (commented out due to timeout issues)
            # prompt = f"""
            # Analyze the following sales goal and extract structured information:
            #
            # Goal Description: {goal_input.goal_description}
            # Context: {goal_input.context or 'Not provided'}
            # Target Audience: {goal_input.target_audience or 'Not specified'}
            # Timeline: {goal_input.timeline or 'Not specified'}
            # Budget: {goal_input.budget or 'Not specified'}
            #
            # Extract and return a JSON object with the following structure:
            # {{
            #     "objective": "Main sales objective",
            #     "target_audience": {{
            #         "industry": "Target industry",
            #         "company_size": "Company size range",
            #         "job_titles": ["List of target job titles"],
            #         "geography": "Geographic focus"
            #     }},
            #     "metrics": {{
            #         "primary_metric": "Main success metric",
            #         "target_value": "Target value if specified",
            #         "secondary_metrics": ["Additional metrics"]
            #     }},
            #     "timeline": {{
            #         "duration": "Campaign duration",
            #         "start_date": "Preferred start date",
            #         "milestones": ["Key milestones"]
            #     }},
            #     "budget": {{
            #         "total_budget": "Total budget if specified",
            #         "budget_allocation": "How budget should be allocated"
            #     }},
            #     "constraints": ["Any constraints or limitations"],
            #     "success_criteria": ["What defines success"],
            #     "missing_information": ["Key information that's missing"]
            # }}
            #
            # Be thorough and extract as much relevant information as possible.
            # """
            #
            # # Use the LLM to parse the goal
            # response = await self.llm.ainvoke(prompt)
            #
            # # Parse the JSON response
            # try:
            #     parsed_goal = json.loads(response.content)
            # except json.JSONDecodeError:
            #     # Fallback parsing if JSON is malformed
            #     parsed_goal = self._fallback_goal_parsing(goal_input)
            #
            # return parsed_goal

        except Exception as e:
            logger.error(f"Goal parsing failed: {e}")
            return self._fallback_goal_parsing_enhanced(goal_input)

    async def _update_goal_with_followup(self, parsed_goal: Dict[str, Any], goal_input: GoalUnderstandingInput) -> Dict[str, Any]:
        """Update parsed goal with information from follow-up responses"""
        try:
            # Merge answered questions into parsed goal
            for question_key, answer in goal_input.answered_questions.items():
                if question_key == "target_audience" and answer:
                    parsed_goal["target_audience"] = answer
                elif question_key == "timeline" and answer:
                    parsed_goal["timeline"] = answer
                elif question_key == "budget" and answer:
                    parsed_goal["budget"] = answer
                elif question_key == "success_metrics" and answer:
                    parsed_goal["success_criteria"] = answer
                elif question_key == "industry" and answer:
                    if "target_audience" not in parsed_goal:
                        parsed_goal["target_audience"] = {}
                    parsed_goal["target_audience"]["industry"] = answer
                elif question_key == "location" and answer:
                    if "target_audience" not in parsed_goal:
                        parsed_goal["target_audience"] = {}
                    parsed_goal["target_audience"]["location"] = answer
                elif question_key == "company_size" and answer:
                    if "target_audience" not in parsed_goal:
                        parsed_goal["target_audience"] = {}
                    parsed_goal["target_audience"]["company_size"] = answer

            return parsed_goal

        except Exception as e:
            logger.error(f"Failed to update goal with follow-up: {e}")
            return parsed_goal

    def _identify_missing_information(self, parsed_goal: Dict[str, Any]) -> List[str]:
        """Identify what information is still missing from the goal"""
        missing = []

        # Check for essential information
        if not parsed_goal.get("objective"):
            missing.append("objective")

        if not parsed_goal.get("target_audience"):
            missing.append("target_audience")
        elif isinstance(parsed_goal["target_audience"], dict):
            if not parsed_goal["target_audience"].get("industry"):
                missing.append("industry")
            if not parsed_goal["target_audience"].get("location"):
                missing.append("location")

        if not parsed_goal.get("timeline"):
            missing.append("timeline")

        if not parsed_goal.get("budget"):
            missing.append("budget")

        if not parsed_goal.get("success_criteria"):
            missing.append("success_metrics")

        return missing
    
    def _fallback_goal_parsing_enhanced(self, goal_input: GoalUnderstandingInput) -> Dict[str, Any]:
        """Enhanced fallback goal parsing with basic NLP and follow-up handling"""
        import re

        goal_text = goal_input.goal_description.lower()
        answered_questions = goal_input.answered_questions or {}

        # Extract numbers (potential targets)
        numbers = re.findall(r'\d+', goal_input.goal_description)
        target_value = int(numbers[0]) if numbers else 10

        # Detect industry - prioritize answered questions
        detected_industry = answered_questions.get("industry", "")
        if not detected_industry:
            industry_keywords = {
                "restaurant": "restaurants",
                "food": "restaurants",
                "dining": "restaurants",
                "tech": "technology",
                "software": "technology",
                "saas": "technology",
                "healthcare": "healthcare",
                "finance": "finance",
                "retail": "retail",
                "ecommerce": "retail",
                "consulting": "consulting",
                "marketing": "marketing"
            }

            for keyword, industry in industry_keywords.items():
                if keyword in goal_text:
                    detected_industry = industry
                    break

        # Detect location - prioritize answered questions
        detected_location = answered_questions.get("location", "")
        if not detected_location:
            location_keywords = {
                "london": "London, UK",
                "uk": "United Kingdom",
                "us": "United States",
                "usa": "United States",
                "europe": "Europe",
                "new york": "New York, USA",
                "california": "California, USA",
                "toronto": "Toronto, Canada",
                "sydney": "Sydney, Australia"
            }

            for keyword, location in location_keywords.items():
                if keyword in goal_text:
                    detected_location = location
                    break

        # Detect purpose/service - prioritize answered questions
        detected_purpose = answered_questions.get("purpose", "")
        if not detected_purpose:
            purpose_keywords = {
                "pos": "POS system",
                "point of sale": "POS system",
                "software": "software solution",
                "system": "system",
                "service": "service",
                "consulting": "consulting services",
                "marketing": "marketing services",
                "crm": "CRM system",
                "website": "website development",
                "app": "mobile app",
                "platform": "platform solution"
            }

            for keyword, purpose in purpose_keywords.items():
                if keyword in goal_text:
                    detected_purpose = purpose
                    break

        # Detect timeline
        timeline_keywords = {
            "month": f"{target_value} months" if "month" in goal_text else "3 months",
            "week": f"{target_value} weeks" if "week" in goal_text else "4 weeks",
            "year": f"{target_value} years" if "year" in goal_text else "1 year"
        }

        detected_timeline = "3 months"  # default
        for keyword, timeline in timeline_keywords.items():
            if keyword in goal_text:
                detected_timeline = timeline
                break

        # Determine missing information (be conservative)
        missing_info = []
        if not detected_industry:
            missing_info.append("industry")
        if not detected_location:
            missing_info.append("location")
        # Be more conservative about purpose detection - if it's vague or generic, consider it missing
        if not detected_purpose or detected_purpose in ["service", "system", "solution"]:
            missing_info.append("purpose")

        # Generate Apollo keywords based on detected information
        apollo_keywords = self._generate_apollo_keywords_fallback(
            detected_industry, detected_purpose, goal_text
        )

        return {
            "objective": f"Acquire {target_value} new clients in {detected_industry}" + (f" for {detected_purpose}" if detected_purpose else ""),
            "product_or_service": detected_purpose or "Not specified",
            "target_audience": {
                "industry": detected_industry,
                "company_size": "Small to Medium Business",
                "job_titles": ["Owner", "Manager", "Decision Maker"],
                "geography": detected_location
            },
            "metrics": {
                "primary_metric": "Number of clients acquired",
                "target_value": target_value,
                "secondary_metrics": ["Revenue generated", "Conversion rate"]
            },
            "timeline": {
                "duration": detected_timeline,
                "start_date": "Immediate",
                "milestones": ["Lead generation", "Initial contact", "Client acquisition"]
            },
            "budget": {
                "total_budget": goal_input.budget or "Not specified",
                "budget_allocation": "Lead generation and outreach"
            },
            "constraints": [],
            "success_criteria": [f"Acquire {target_value} new clients", "Positive ROI"],
            "apollo_keywords": apollo_keywords,
            "missing_information": missing_info
        }

    def _generate_apollo_keywords_fallback(self, industry: str, purpose: str, goal_text: str) -> Dict[str, Any]:
        """Generate Apollo keywords for fallback parsing with focus on generic, high-volume terms"""
        # Base keywords from industry - prioritize simple, generic terms
        industry_keyword_map = {
            "restaurants": "restaurant",
            "restaurant": "restaurant",
            "technology": "technology",
            "healthcare": "hospital",
            "hospital": "hospital",
            "retail": "retail",
            "finance": "finance",
            "consulting": "consulting",
            "marketing": "marketing"
        }

        # Start with the most generic term
        primary_keywords = industry_keyword_map.get(industry.lower(), industry) if industry else ""

        # Generate related keywords - keep them simple and generic
        related_keywords = []
        if industry:
            if industry.lower() in ["restaurants", "restaurant"]:
                related_keywords = ["restaurant", "food", "dining", "hospitality"]
            elif industry.lower() in ["healthcare", "hospital"]:
                related_keywords = ["hospital", "healthcare", "medical", "clinic"]
            elif industry.lower() == "technology":
                related_keywords = ["technology", "software", "tech", "IT"]
            elif industry.lower() == "retail":
                related_keywords = ["retail", "store", "shop", "commerce"]
            elif industry.lower() == "finance":
                related_keywords = ["finance", "bank", "financial", "investment"]
            else:
                # For any other industry, use generic variations
                base_term = industry.lower()
                related_keywords = [base_term, f"{base_term}s", "business", "company"]

        return {
            "primary_keywords": primary_keywords,
            "related_keywords": related_keywords,
            "reasoning": f"Generic keywords optimized for high lead volume in {industry} industry"
        }

    def _fallback_goal_parsing(self, goal_input: GoalUnderstandingInput) -> Dict[str, Any]:
        """Fallback goal parsing when AI parsing fails"""
        return {
            "objective": goal_input.goal_description,
            "target_audience": {
                "industry": goal_input.target_audience or "Not specified",
                "company_size": "Not specified",
                "job_titles": [],
                "geography": "Not specified"
            },
            "metrics": {
                "primary_metric": "Not specified",
                "target_value": "Not specified",
                "secondary_metrics": []
            },
            "timeline": {
                "duration": goal_input.timeline or "Not specified",
                "start_date": "Not specified",
                "milestones": []
            },
            "budget": {
                "total_budget": goal_input.budget or "Not specified",
                "budget_allocation": "Not specified"
            },
            "constraints": [],
            "success_criteria": [],
            "missing_information": [
                "Target audience details",
                "Success metrics",
                "Timeline specifics",
                "Budget allocation"
            ]
        }
    
    async def _generate_clarification_questions(
        self,
        parsed_goal: Dict[str, Any],
        missing_information: List[str]
    ) -> List[str]:
        """Generate clarification questions based on missing information"""
        questions = []
        
        # Check for missing target audience information
        if not parsed_goal.get("target_audience", {}).get("industry"):
            questions.append("What industry or industries are you targeting?")
        
        if not parsed_goal.get("target_audience", {}).get("job_titles"):
            questions.append("What specific job titles or roles are you trying to reach?")
        
        # Check for missing metrics
        if not parsed_goal.get("metrics", {}).get("primary_metric"):
            questions.append("What is your primary success metric (e.g., leads generated, meetings booked, revenue)?")
        
        if not parsed_goal.get("metrics", {}).get("target_value"):
            questions.append("What is your target value for the primary metric?")
        
        # Check for missing timeline
        if not parsed_goal.get("timeline", {}).get("duration"):
            questions.append("What is your desired timeline for this campaign?")
        
        # Check for missing budget
        if not parsed_goal.get("budget", {}).get("total_budget"):
            questions.append("What is your budget for this sales campaign?")
        
        # Add custom questions based on missing information
        missing_info = parsed_goal.get("missing_information", [])
        for info in missing_info:
            if "geographic" in info.lower():
                questions.append("What geographic regions are you targeting?")
            elif "company size" in info.lower():
                questions.append("What company sizes are you targeting (startup, SMB, enterprise)?")
        
        return questions[:5]  # Limit to 5 most important questions
    
    async def _suggest_metrics(self, parsed_goal: Dict[str, Any]) -> List[str]:
        """Suggest relevant success metrics based on the goal"""
        metrics = []
        
        objective = parsed_goal.get("objective", "").lower()
        
        # Common sales metrics
        if "lead" in objective:
            metrics.extend([
                "Number of qualified leads generated",
                "Lead conversion rate",
                "Cost per lead"
            ])
        
        if "meeting" in objective or "demo" in objective:
            metrics.extend([
                "Number of meetings booked",
                "Meeting show-up rate",
                "Meeting to opportunity conversion rate"
            ])
        
        if "revenue" in objective or "sales" in objective:
            metrics.extend([
                "Revenue generated",
                "Average deal size",
                "Sales cycle length"
            ])
        
        # Email-specific metrics
        metrics.extend([
            "Email open rate",
            "Email response rate",
            "Click-through rate"
        ])
        
        # General engagement metrics
        metrics.extend([
            "Engagement rate",
            "Pipeline value created",
            "Return on investment (ROI)"
        ])
        
        return list(set(metrics))[:8]  # Remove duplicates and limit to 8
    
    def _calculate_confidence_score(self, parsed_goal: Dict[str, Any]) -> float:
        """Calculate confidence score based on completeness of parsed goal"""
        total_fields = 0
        completed_fields = 0
        
        # Check objective
        total_fields += 1
        if parsed_goal.get("objective") and parsed_goal["objective"] != "Not specified":
            completed_fields += 1
        
        # Check target audience
        audience = parsed_goal.get("target_audience", {})
        for field in ["industry", "company_size", "job_titles", "geography"]:
            total_fields += 1
            if audience.get(field) and audience[field] not in ["Not specified", []]:
                completed_fields += 1
        
        # Check metrics
        metrics = parsed_goal.get("metrics", {})
        for field in ["primary_metric", "target_value"]:
            total_fields += 1
            if metrics.get(field) and metrics[field] != "Not specified":
                completed_fields += 1
        
        # Check timeline
        timeline = parsed_goal.get("timeline", {})
        for field in ["duration", "start_date"]:
            total_fields += 1
            if timeline.get(field) and timeline[field] != "Not specified":
                completed_fields += 1
        
        # Check budget
        budget = parsed_goal.get("budget", {})
        total_fields += 1
        if budget.get("total_budget") and budget["total_budget"] != "Not specified":
            completed_fields += 1
        
        return round(completed_fields / total_fields, 2) if total_fields > 0 else 0.0
    
    async def _generate_next_steps(
        self,
        parsed_goal: Dict[str, Any],
        confidence_score: float,
        is_complete: bool = False
    ) -> List[str]:
        """Generate recommended next steps based on goal analysis"""
        next_steps = []
        
        if confidence_score < 0.7:
            next_steps.append("Gather additional information by answering clarification questions")
        
        if confidence_score >= 0.7:
            next_steps.append("Proceed to strategic planning phase")
            next_steps.append("Begin target audience research and lead generation")
        
        # Always include these steps
        next_steps.extend([
            "Review and validate the parsed goal information",
            "Set up tracking and measurement systems",
            "Define campaign timeline and milestones"
        ])
        
        return next_steps


