"""
Simple Workflow Manager for Selda AI
Provides a simplified interface to start workflows from just a goal string
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.strategic_planning import StrategicPlanningAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent
from app.modules.agents.workflow_manager import workflow_manager, WorkflowInput
from app.core.database import supabase_service

logger = logging.getLogger(__name__)


class SimpleWorkflowManager:
    """Simplified workflow manager that starts from a goal string"""

    def __init__(self, organization_id: str = None):
        # Use fixed demo organization ID if none provided
        self.organization_id = organization_id or "00000000-0000-4000-8000-000000000001"
        
    async def start_from_goal(self, goal: str, user_id: str = "00000000-0000-4000-8000-000000000002") -> Dict[str, Any]:
        """
        Start a complete workflow from just a goal string
        
        Args:
            goal: Natural language sales goal
            user_id: User ID (optional, defaults to demo user)
            
        Returns:
            Dict containing workflow results and status
        """
        try:
            logger.info(f"Starting workflow from goal: {goal}")
            
            # Step 1: Goal Understanding
            goal_result = await self._understand_goal(goal, user_id)
            
            # Step 2: Strategic Planning
            planning_result = await self._create_strategic_plan(goal_result)
            
            # Step 3: Lead Sourcing
            lead_result = await self._source_leads(goal_result, planning_result)
            
            # Step 4: Start Full Workflow
            workflow_result = await self._start_full_workflow(goal_result, planning_result, lead_result)
            
            return {
                "success": True,
                "goal_understanding": goal_result,
                "strategic_planning": planning_result,
                "lead_sourcing": lead_result,
                "workflow": workflow_result,
                "message": "Workflow started successfully from goal"
            }
            
        except Exception as e:
            logger.error(f"Failed to start workflow from goal: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to start workflow"
            }
    
    async def _understand_goal(self, goal: str, user_id: str) -> Dict[str, Any]:
        """Execute goal understanding agent"""
        try:
            goal_agent = GoalUnderstandingAgent(self.organization_id)
            
            goal_input = {
                "goal_description": goal,
                "user_id": user_id,
                "organization_id": self.organization_id,
                "conversation_history": [],
                "is_follow_up": False,
                "context": None,
                "target_audience": None,
                "timeline": None,
                "budget": None,
                "answered_questions": {}
            }
            
            result = await goal_agent.execute(goal_input)
            logger.info(f"Goal understanding completed with confidence: {result.get('confidence_score', 0)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Goal understanding failed: {e}")
            raise
    
    async def _create_strategic_plan(self, goal_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute strategic planning agent"""
        try:
            planning_agent = StrategicPlanningAgent(self.organization_id)
            
            planning_input = goal_result.get('next_agent_input', {})
            if not planning_input:
                # Fallback input if next_agent_input is not available
                parsed_goal = goal_result.get('parsed_goal', {})
                planning_input = {
                    "goal": parsed_goal,
                    "target_audience": parsed_goal.get('target_audience', {}),
                    "budget_constraints": parsed_goal.get('budget_constraints'),
                    "timeline_constraints": parsed_goal.get('timeline_constraints'),
                    "organization_id": self.organization_id
                }
            
            result = await planning_agent.execute(planning_input)
            logger.info("Strategic planning completed")
            
            return result
            
        except Exception as e:
            logger.error(f"Strategic planning failed: {e}")
            raise
    
    async def _source_leads(self, goal_result: Dict[str, Any], planning_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute lead sourcing agent"""
        try:
            lead_agent = LeadSourcingAgent(self.organization_id)
            
            parsed_goal = goal_result.get('parsed_goal', {})
            target_audience = parsed_goal.get('target_audience', {})
            
            # Extract target count from goal or use default
            target_count = 100
            if parsed_goal.get('metrics', {}).get('target_value'):
                try:
                    target_count = int(parsed_goal['metrics']['target_value']) * 10  # 10x leads for meetings
                except (ValueError, TypeError):
                    target_count = 100
            
            lead_input = {
                "campaign_id": f"campaign_{uuid.uuid4().hex[:8]}",
                "target_audience": target_audience,
                "lead_count_target": target_count,
                "sources": ["apollo"],
                "quality_threshold": 70,
                "organization_id": self.organization_id,
                "strategic_plan": planning_result
            }
            
            result = await lead_agent.execute(lead_input)
            logger.info(f"Lead sourcing completed, found {result.get('leads_sourced', 0)} leads")
            
            return result
            
        except Exception as e:
            logger.error(f"Lead sourcing failed: {e}")
            raise
    
    async def _start_full_workflow(
        self, 
        goal_result: Dict[str, Any], 
        planning_result: Dict[str, Any], 
        lead_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Start the complete sequential workflow"""
        try:
            parsed_goal = goal_result.get('parsed_goal', {})
            campaign_id = f"campaign_{uuid.uuid4().hex[:8]}"
            
            # Extract target meetings from goal
            target_meetings = 10
            if parsed_goal.get('metrics', {}).get('target_value'):
                try:
                    target_meetings = int(parsed_goal['metrics']['target_value'])
                except (ValueError, TypeError):
                    target_meetings = 10
            
            # Prepare workflow input
            workflow_input = WorkflowInput(
                campaign_id=campaign_id,
                organization_id=self.organization_id,
                initial_input={
                    "goal": parsed_goal,
                    "strategic_plan": planning_result,
                    "initial_leads": lead_result,
                    "target_audience": parsed_goal.get('target_audience', {}),
                    "goal_understanding_result": goal_result
                },
                workflow_type="sales_autopilot",
                target_meetings=target_meetings,
                target_contacts=target_meetings * 50  # 50 contacts per meeting target
            )
            
            # Start workflow
            workflow_id = await workflow_manager.start_workflow(workflow_input)
            
            logger.info(f"Full workflow started with ID: {workflow_id}")
            
            return {
                "workflow_id": workflow_id,
                "campaign_id": campaign_id,
                "target_meetings": target_meetings,
                "target_contacts": target_meetings * 50,
                "status": "started"
            }
            
        except Exception as e:
            logger.error(f"Failed to start full workflow: {e}")
            raise
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow status"""
        try:
            status = await workflow_manager.get_workflow_status(workflow_id)
            if status:
                return {
                    "workflow_id": workflow_id,
                    "status": status.status,
                    "current_stage": status.current_stage,
                    "stages_completed": status.stages_completed,
                    "started_at": status.started_at,
                    "total_execution_time_ms": status.total_execution_time_ms
                }
            return None
        except Exception as e:
            logger.error(f"Failed to get workflow status: {e}")
            return None
    
    async def get_workflow_results(self, workflow_id: str) -> Dict[str, Any]:
        """Get complete workflow results"""
        try:
            status = await self.get_workflow_status(workflow_id)
            results = await workflow_manager.get_stage_results(workflow_id)
            
            return {
                "workflow_status": status,
                "stage_results": [result.model_dump() for result in results],
                "total_stages": len(results)
            }
        except Exception as e:
            logger.error(f"Failed to get workflow results: {e}")
            return {"error": str(e)}


# Global instance
simple_workflow_manager = SimpleWorkflowManager()
