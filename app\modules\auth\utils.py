"""
Authentication utilities
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import Crypt<PERSON>ontext
from jose import JW<PERSON>rror, jwt
import secrets
import string
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging

from app.core.config import settings
from app.core.exceptions import AuthenticationError

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def generate_password_reset_token() -> str:
    """Generate a secure password reset token"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))


def generate_email_verification_token() -> str:
    """Generate a secure email verification token"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.jwt_access_token_expire_minutes)
    
    to_encode.update({"exp": expire, "type": "access"})
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.jwt_secret_key, 
            algorithm=settings.jwt_algorithm
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"Failed to create access token: {e}")
        raise AuthenticationError("Failed to create access token")


def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create a JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.jwt_refresh_token_expire_days)
    to_encode.update({"exp": expire, "type": "refresh"})
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.jwt_secret_key, 
            algorithm=settings.jwt_algorithm
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"Failed to create refresh token: {e}")
        raise AuthenticationError("Failed to create refresh token")


def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(
            token, 
            settings.jwt_secret_key, 
            algorithms=[settings.jwt_algorithm]
        )
        
        # Check token type
        if payload.get("type") != token_type:
            raise AuthenticationError(f"Invalid token type. Expected {token_type}")
        
        # Check expiration
        exp = payload.get("exp")
        if exp is None:
            raise AuthenticationError("Token missing expiration")
        
        if datetime.utcfromtimestamp(exp) < datetime.utcnow():
            raise AuthenticationError("Token has expired")
        
        return payload
        
    except JWTError as e:
        logger.warning(f"JWT verification failed: {e}")
        raise AuthenticationError(f"Invalid token: {str(e)}")
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise AuthenticationError("Token verification failed")


def validate_password_strength(password: str) -> bool:
    """Validate password strength"""
    if len(password) < 8:
        return False
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    return has_upper and has_lower and has_digit and has_special


def generate_secure_password(length: int = 12) -> str:
    """Generate a secure random password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    
    # Ensure password meets strength requirements
    if not validate_password_strength(password):
        return generate_secure_password(length)
    
    return password


class TokenBlacklist:
    """Simple in-memory token blacklist (should use Redis in production)"""
    
    def __init__(self):
        self._blacklisted_tokens = set()
    
    def add_token(self, token: str) -> None:
        """Add token to blacklist"""
        self._blacklisted_tokens.add(token)
        logger.debug(f"Token added to blacklist")
    
    def is_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted"""
        return token in self._blacklisted_tokens
    
    def clear_expired_tokens(self) -> None:
        """Clear expired tokens from blacklist (should be called periodically)"""
        # In a real implementation, this would check token expiration
        # For now, we'll keep it simple
        pass


# Global token blacklist instance
token_blacklist = TokenBlacklist()


def create_email_verification_email(email: str, token: str) -> Dict[str, str]:
    """Create email verification email content"""
    verification_url = f"{settings.cors_origins[0]}/verify-email?token={token}"
    
    subject = f"Verify your email for {settings.app_name}"
    
    html_body = f"""
    <html>
        <body>
            <h2>Welcome to {settings.app_name}!</h2>
            <p>Please click the link below to verify your email address:</p>
            <p><a href="{verification_url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{verification_url}</p>
            <p>This link will expire in {settings.email_verification_expire_hours} hours.</p>
            <p>If you didn't create an account, please ignore this email.</p>
        </body>
    </html>
    """
    
    text_body = f"""
    Welcome to {settings.app_name}!
    
    Please visit the following link to verify your email address:
    {verification_url}
    
    This link will expire in {settings.email_verification_expire_hours} hours.
    
    If you didn't create an account, please ignore this email.
    """
    
    return {
        "subject": subject,
        "html_body": html_body,
        "text_body": text_body
    }


def create_password_reset_email(email: str, token: str) -> Dict[str, str]:
    """Create password reset email content"""
    reset_url = f"{settings.cors_origins[0]}/reset-password?token={token}"
    
    subject = f"Reset your password for {settings.app_name}"
    
    html_body = f"""
    <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>You requested to reset your password for {settings.app_name}.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="{reset_url}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{reset_url}</p>
            <p>This link will expire in {settings.password_reset_expire_hours} hour(s).</p>
            <p>If you didn't request a password reset, please ignore this email.</p>
        </body>
    </html>
    """
    
    text_body = f"""
    Password Reset Request
    
    You requested to reset your password for {settings.app_name}.
    
    Please visit the following link to reset your password:
    {reset_url}
    
    This link will expire in {settings.password_reset_expire_hours} hour(s).
    
    If you didn't request a password reset, please ignore this email.
    """
    
    return {
        "subject": subject,
        "html_body": html_body,
        "text_body": text_body
    }
