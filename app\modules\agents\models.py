"""
Agent models and schemas
"""

from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.shared.models import BaseResponse, TimestampMixin


class AgentType(str, Enum):
    """Agent type enumeration"""
    GOAL_UNDERSTANDING = "goal_understanding"
    STRATEGIC_PLANNING = "strategic_planning"
    CAMPAIGN_PLANNING = "campaign_planning"
    LEAD_SOURCING = "lead_sourcing"
    LEAD_DISTRIBUTION = "lead_distribution"
    CONTENT_CREATION = "content_creation"
    EMAIL_AUTOMATION = "email_automation"
    REPLY_ANALYSIS = "reply_analysis"
    MEETING_BOOKING = "meeting_booking"
    PERFORMANCE_MONITORING = "performance_monitoring"
    LINKEDIN_OUTREACH = "linkedin_outreach"


class AgentStatus(str, Enum):
    """Agent status enumeration"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    COMPLETED = "completed"


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """Task priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class AgentConfiguration(BaseModel):
    """Agent configuration model"""
    id: str
    organization_id: str
    agent_type: AgentType
    name: str
    description: str
    is_active: bool = True
    settings: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class AgentConfigurationUpdate(BaseModel):
    """Agent configuration update model"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    settings: Optional[Dict[str, Any]] = None


class AgentTask(BaseModel):
    """Agent task model"""
    id: str
    organization_id: str
    agent_type: AgentType
    task_type: str
    status: TaskStatus
    priority: TaskPriority = TaskPriority.MEDIUM
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    progress: float = Field(default=0.0, ge=0.0, le=100.0)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CreateAgentTaskRequest(BaseModel):
    """Create agent task request"""
    agent_type: AgentType
    task_type: str
    priority: TaskPriority = TaskPriority.MEDIUM
    input_data: Dict[str, Any] = Field(default_factory=dict)


class AgentTaskResponse(BaseResponse):
    """Agent task response"""
    task: AgentTask


class AgentExecution(BaseModel):
    """Agent execution model"""
    id: str
    organization_id: str
    agent_type: AgentType
    task_id: str
    status: AgentStatus
    execution_data: Dict[str, Any] = Field(default_factory=dict)
    logs: List[str] = Field(default_factory=list)
    metrics: Dict[str, Any] = Field(default_factory=dict)
    started_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class AgentMetrics(BaseModel):
    """Agent metrics model"""
    agent_type: AgentType
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    average_execution_time: float = 0.0
    success_rate: float = 0.0
    last_execution: Optional[datetime] = None


class AgentCapability(BaseModel):
    """Agent capability model"""
    name: str
    description: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    is_available: bool = True


class AgentInfo(BaseModel):
    """Agent information model"""
    agent_type: AgentType
    name: str
    description: str
    capabilities: List[AgentCapability]
    status: AgentStatus
    configuration: Optional[AgentConfiguration] = None
    metrics: Optional[AgentMetrics] = None


class CrewConfiguration(BaseModel):
    """Crew configuration for multi-agent tasks"""
    id: str
    organization_id: str
    name: str
    description: str
    agents: List[AgentType]
    workflow: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = True
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CrewExecution(BaseModel):
    """Crew execution model"""
    id: str
    organization_id: str
    crew_id: str
    status: AgentStatus
    current_agent: Optional[AgentType] = None
    execution_data: Dict[str, Any] = Field(default_factory=dict)
    agent_results: Dict[str, Any] = Field(default_factory=dict)
    started_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class GoalUnderstandingInput(BaseModel):
    """Goal understanding agent input - supports natural language chat interface"""
    goal_description: str = Field(..., min_length=10, max_length=2000, description="Natural language sales goal from user")
    user_id: Optional[str] = Field(None, description="User ID for conversation tracking")
    conversation_history: List[Dict[str, str]] = Field(default_factory=list, description="Previous chat messages")
    context: Optional[str] = Field(None, max_length=2000, description="Additional business context")
    target_audience: Optional[str] = Field(None, max_length=500, description="Target audience if provided")
    timeline: Optional[str] = Field(None, max_length=200, description="Timeline if specified")
    budget: Optional[str] = Field(None, max_length=200, description="Budget if mentioned")
    is_follow_up: bool = Field(default=False, description="Whether this is answering follow-up questions")
    answered_questions: Dict[str, str] = Field(default_factory=dict, description="Previously answered questions")


class GoalUnderstandingOutput(BaseModel):
    """Goal understanding agent output - supports chat interface flow"""
    parsed_goal: Dict[str, Any] = Field(..., description="Extracted goal information")
    clarification_questions: List[str] = Field(default_factory=list, description="Follow-up questions to ask user")
    suggested_metrics: List[str] = Field(default_factory=list, description="Suggested success metrics")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in goal understanding")
    next_steps: List[str] = Field(default_factory=list, description="Recommended next actions")
    is_complete: bool = Field(default=False, description="Whether goal understanding is complete")
    missing_information: List[str] = Field(default_factory=list, description="Information still needed")
    next_agent_input: Optional[Dict[str, Any]] = Field(None, description="Input for next agent if complete")


class StrategicPlanningInput(BaseModel):
    """Strategic planning agent input"""
    goal: Dict[str, Any]
    target_audience: Dict[str, Any]
    budget_constraints: Optional[Dict[str, Any]] = None
    timeline_constraints: Optional[Dict[str, Any]] = None
    existing_data: Optional[Dict[str, Any]] = None


class StrategicPlanningOutput(BaseModel):
    """Strategic planning agent output"""
    campaign_strategy: Dict[str, Any]
    lead_generation_plan: Dict[str, Any]
    content_strategy: Dict[str, Any]
    timeline: Dict[str, Any]
    success_metrics: List[str]
    risk_assessment: Dict[str, Any]


class ContentCreationInput(BaseModel):
    """Content creation agent input"""
    campaign_context: Dict[str, Any]
    target_audience: Dict[str, Any]
    content_type: str = Field(..., description="email, linkedin_message, etc.")
    personalization_data: Optional[Dict[str, Any]] = None
    brand_guidelines: Optional[Dict[str, Any]] = None


class ContentCreationOutput(BaseModel):
    """Content creation agent output"""
    content: str
    subject_line: Optional[str] = None
    personalization_variables: List[str]
    content_score: float = Field(..., ge=0.0, le=1.0)
    suggestions: List[str]


class EmailAutomationInput(BaseModel):
    """Email automation agent input"""
    campaign_id: str
    email_content: str
    recipient_list: List[Dict[str, Any]]
    schedule: Optional[Dict[str, Any]] = None
    tracking_settings: Optional[Dict[str, Any]] = None


class EmailAutomationOutput(BaseModel):
    """Email automation agent output"""
    emails_sent: int
    emails_scheduled: int
    failed_sends: int
    tracking_urls: Dict[str, str]
    delivery_report: Dict[str, Any]


class ReplyAnalysisInput(BaseModel):
    """Reply analysis agent input"""
    email_content: str
    sender_info: Dict[str, Any]
    conversation_history: Optional[List[Dict[str, Any]]] = None
    campaign_context: Optional[Dict[str, Any]] = None


class ReplyAnalysisOutput(BaseModel):
    """Reply analysis agent output"""
    sentiment: str = Field(..., description="positive, negative, neutral")
    intent: str = Field(..., description="interested, not_interested, request_info, etc.")
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    suggested_response: Optional[str] = None
    requires_human_attention: bool = False
    extracted_information: Dict[str, Any] = Field(default_factory=dict)


class AgentListResponse(BaseResponse):
    """Agent list response"""
    agents: List[AgentInfo]


class AgentTaskListResponse(BaseResponse):
    """Agent task list response"""
    tasks: List[AgentTask]
    total: int
    page: int
    limit: int


class AgentMetricsResponse(BaseResponse):
    """Agent metrics response"""
    metrics: Dict[AgentType, AgentMetrics]


class AgentConfigurationResponse(BaseResponse):
    """Agent configuration response"""
    configuration: AgentConfiguration


class CrewExecutionResponse(BaseResponse):
    """Crew execution response"""
    execution: CrewExecution
