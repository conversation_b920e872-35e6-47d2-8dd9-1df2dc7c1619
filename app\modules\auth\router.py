"""
Authentication router
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import logging

from app.modules.auth.models import (
    LoginRequest,
    LoginResponse,
    RegisterRequest,
    RegisterResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    PasswordResetRequest,
    PasswordResetResponse,
    PasswordResetConfirm,
    EmailVerificationRequest,
    EmailVerificationResponse,
    ChangePasswordRequest,
    ChangePasswordResponse,
    LogoutResponse
)
from app.modules.auth.service import auth_service
from app.core.exceptions import (
    AuthenticationError,
    ValidationError,
    ConflictError,
    NotFoundError
)
from app.shared.dependencies import get_current_active_user
from app.shared.models import BaseResponse, ErrorResponse

logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Security
security = HTTPBearer()

# Router
router = APIRouter()


@router.post("/register", response_model=RegisterResponse)
@limiter.limit("5/minute")
async def register(request: Request, user_data: RegisterRequest):
    """Register a new user"""
    try:
        result = await auth_service.register(user_data)
        return RegisterResponse(**result)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=LoginResponse)
@limiter.limit("10/minute")
async def login(request: Request, credentials: LoginRequest):
    """Authenticate user and return tokens"""
    try:
        result = await auth_service.login(credentials)
        return LoginResponse(**result)
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message,
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=RefreshTokenResponse)
@limiter.limit("20/minute")
async def refresh_token(request: Request, token_data: RefreshTokenRequest):
    """Refresh access token"""
    try:
        result = await auth_service.refresh_token(token_data)
        return RefreshTokenResponse(**result)
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message,
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout", response_model=LogoutResponse)
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    refresh_token: str = None
):
    """Logout user and invalidate tokens"""
    try:
        result = await auth_service.logout(credentials.credentials, refresh_token)
        return LogoutResponse(**result)
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/password-reset", response_model=PasswordResetResponse)
@limiter.limit("3/minute")
async def request_password_reset(request: Request, reset_data: PasswordResetRequest):
    """Request password reset"""
    try:
        result = await auth_service.request_password_reset(reset_data)
        return PasswordResetResponse(**result)
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        # Always return success to prevent email enumeration
        return PasswordResetResponse(
            message="If the email exists, a password reset link has been sent."
        )


@router.post("/password-reset/confirm", response_model=BaseResponse)
@limiter.limit("5/minute")
async def confirm_password_reset(request: Request, reset_data: PasswordResetConfirm):
    """Confirm password reset with token"""
    try:
        # TODO: Implement password reset confirmation
        return BaseResponse(
            success=True,
            message="Password reset successfully"
        )
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Password reset confirmation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.post("/verify-email", response_model=EmailVerificationResponse)
@limiter.limit("10/minute")
async def verify_email(request: Request, verification_data: EmailVerificationRequest):
    """Verify email address with token"""
    try:
        # TODO: Implement email verification
        return EmailVerificationResponse(
            message="Email verified successfully",
            email_verified=True
        )
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Email verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )


@router.post("/change-password", response_model=ChangePasswordResponse)
async def change_password(
    password_data: ChangePasswordRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Change user password (requires authentication)"""
    try:
        # TODO: Implement password change
        return ChangePasswordResponse(
            message="Password changed successfully"
        )
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message,
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.get("/me")
async def get_current_user_profile(current_user: dict = Depends(get_current_active_user)):
    """Get current user profile"""
    try:
        # Remove sensitive data
        user_profile = {
            "id": current_user["id"],
            "email": current_user["email"],
            "first_name": current_user["first_name"],
            "last_name": current_user["last_name"],
            "role": current_user["role"],
            "is_active": current_user["is_active"],
            "email_verified": current_user.get("email_verified", False),
            "organization_id": current_user.get("organization_id"),
            "created_at": current_user["created_at"],
            "updated_at": current_user.get("updated_at")
        }
        return user_profile
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message,
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Get current user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


# Note: Rate limit exception handler is added to the main app, not the router
