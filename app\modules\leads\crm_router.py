"""
Router for Lead CRM functionalities
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, Query, Header
from uuid import UUID
import logging
from typing import List

from app.modules.leads.models import (
    Lead, LeadCreate, LeadUpdate, LeadFilter, PaginatedLeads
)
from app.modules.leads.service import LeadService
from app.shared.dependencies import get_current_active_user, get_current_organization
from app.shared.models import PaginationParams
from app.core.exceptions import NotFoundError, ConflictError, ValidationError

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/leads", tags=["Leads CRM"])

# Dependency for LeadService
def get_lead_service() -> LeadService:
    return LeadService()

@router.post("/", response_model=Lead, status_code=status.HTTP_201_CREATED)
async def create_lead(
    lead_data: LeadCreate,
    service: LeadService = Depends(get_lead_service),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Create a new lead in the CRM."""
    try:
        lead_data.organization_id = organization['id']
        return await service.create_lead(lead_data)
    except ConflictError as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating lead: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create lead.")

@router.get("/", response_model=PaginatedLeads)
async def get_leads_list(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    location: str = Query(None),
    industry: str = Query(None),
    service: LeadService = Depends(get_lead_service),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get a list of leads for the organization."""
    try:
        pagination = PaginationParams(page=page, limit=limit)
        filters = LeadFilter(location=location, industry=industry)
        return await service.get_leads(organization['id'], pagination, filters)
    except Exception as e:
        logger.error(f"Error getting leads for organization {organization['id']}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve leads.")

@router.get("/{lead_id}", response_model=Lead)
async def get_lead_details(
    lead_id: UUID,
    service: LeadService = Depends(get_lead_service),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get details of a specific lead."""
    try:
        return await service.get_lead_by_id(lead_id, organization['id'])
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting lead {lead_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve lead details.")

@router.patch("/{lead_id}", response_model=Lead)
async def update_lead(
    lead_id: UUID,
    update_data: LeadUpdate,
    service: LeadService = Depends(get_lead_service),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Update a lead's information."""
    try:
        return await service.update_lead(lead_id, update_data, organization['id'])
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating lead {lead_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update lead.")

@router.post("/{lead_id}/blacklist", response_model=Lead)
async def blacklist_lead(
    lead_id: UUID,
    service: LeadService = Depends(get_lead_service),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Blacklist a lead to prevent future contact."""
    try:
        return await service.blacklist_lead(lead_id, organization['id'])
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Error blacklisting lead {lead_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to blacklist lead.")

@router.post("/source/apollo", response_model=List[Lead])
async def source_apollo_leads(
    filters: LeadFilter,
    page: int = Query(1, ge=1, description="Page number for Apollo.io search"),
    apollo_api_key: str = Header(..., description="Apollo.io API key"),
    service: LeadService = Depends(get_lead_service),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Source new leads from Apollo.io based on filters."""
    try:
        created_leads = await service.source_leads_from_apollo(
            filters=filters,
            organization_id=organization['id'],
            api_key=apollo_api_key,
            page=page
        )
        return created_leads
    except Exception as e:
        logger.error(f"Error sourcing leads from Apollo.io: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to source leads from Apollo.io.") 