#!/usr/bin/env python3
"""
Development server runner
"""

import uvicorn
import os
from pathlib import Path

# Ensure we're in the right directory
os.chdir(Path(__file__).parent)

if __name__ == "__main__":
    # Check if .env file exists
    if not Path(".env").exists():
        print("⚠️  .env file not found. Please copy .env.example to .env and configure your settings.")
        print("   cp .env.example .env")
        exit(1)
    
    print("🚀 Starting Selda AI Backend Development Server")
    print("📖 API Documentation will be available at: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
