"""
Application configuration using Pydantic Settings
"""

from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings
from functools import lru_cache
import os


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Application Settings
    app_name: str = "Selda AI Sales Autopilot"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Security
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    
    # Database - Supabase
    supabase_url: str
    supabase_anon_key: str
    supabase_service_key: str
    
    # AI Services
    openai_api_key: str
    openai_model: str = "gpt-4o-mini"
    openai_max_tokens: int = 2000
    
    # Lead Generation - Apollo.io
    apollo_api_key: str
    apollo_base_url: str = "https://api.apollo.io/v1"
    
    # Payment Processing - Stripe
    stripe_publishable_key: str
    stripe_secret_key: str
    stripe_webhook_secret: str
    stripe_basic_price_id: Optional[str] = None
    stripe_pro_price_id: Optional[str] = None
    stripe_enterprise_price_id: Optional[str] = None
    
    # Email Service Provider - SendGrid
    sendgrid_api_key: str
    sendgrid_from_email: str
    sendgrid_from_name: str = "Selda AI"
    
    # Alternative ESP - Mailgun
    mailgun_api_key: Optional[str] = None
    mailgun_domain: Optional[str] = None
    mailgun_base_url: Optional[str] = None
    
    # Google Services
    google_client_id: str
    google_client_secret: str
    google_redirect_uri: str = "http://localhost:8000/auth/google/callback"
    
    # LinkedIn Integration
    linkedin_client_id: str
    linkedin_client_secret: str
    linkedin_redirect_uri: str = "http://localhost:8000/auth/linkedin/callback"
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    redis_password: Optional[str] = None
    redis_db: int = 0
    
    # Celery Configuration
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    celery_task_serializer: str = "json"
    celery_result_serializer: str = "json"
    celery_accept_content: List[str] = ["json"]
    celery_timezone: str = "UTC"
    
    # Logging Configuration
    log_level: str = "INFO"
    log_format: str = "json"
    log_file: str = "logs/app.log"
    
    # Rate Limiting
    rate_limit_per_minute: int = 60
    rate_limit_burst: int = 10
    
    # CORS Settings
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    cors_allow_credentials: bool = True
    
    # File Upload Settings
    max_file_size_mb: int = 10
    allowed_file_types: List[str] = ["pdf", "doc", "docx", "txt", "csv"]
    
    # Email Settings
    email_verification_expire_hours: int = 24
    password_reset_expire_hours: int = 1
    
    # Webhook Settings
    webhook_secret: str
    
    # Monitoring & Analytics
    sentry_dsn: Optional[str] = None
    analytics_enabled: bool = True
    
    # Development Settings
    reload: bool = False
    workers: int = 1
    
    @validator("cors_origins", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("allowed_file_types", pre=True)
    def assemble_allowed_file_types(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("celery_accept_content", pre=True)
    def assemble_celery_accept_content(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @property
    def database_url(self) -> str:
        """Get the database URL for SQLAlchemy (if needed)"""
        return f"postgresql://postgres:[password]@{self.supabase_url.replace('https://', '')}/postgres"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.environment.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.environment.lower() == "production"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Global settings instance
settings = get_settings()
