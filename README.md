# Selda AI Sales Autopilot - Backend

AI-powered sales automation platform with multi-agent system built using CrewAI framework.

## 🏗️ Architecture

- **Framework**: FastAPI (Modular Monolith)
- **AI Framework**: CrewAI with OpenAI GPT-4
- **Database**: Supabase (PostgreSQL)
- **Lead Generation**: Apollo.io
- **Background Tasks**: Celery + Redis
- **Authentication**: JWT with Row Level Security
- **Payment Processing**: Stripe

## 📁 Project Structure

```
backend/
├── app/
│   ├── modules/           # Feature modules
│   │   ├── auth/         # Authentication
│   │   ├── users/        # User management
│   │   ├── campaigns/    # Campaign management
│   │   ├── leads/        # CRM functionality
│   │   ├── agents/       # CrewAI agents
│   │   ├── payments/     # Stripe integration
│   │   └── analytics/    # Analytics & reporting
│   ├── core/             # Core utilities
│   ├── shared/           # Shared components
│   └── main.py           # FastAPI application
├── tests/                # Test files
├── migrations/           # Database migrations
└── requirements.txt      # Dependencies
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Redis (for Celery)
- Supabase account
- Required API keys (see .env.example)

### Installation

1. **Clone and setup**:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and configuration
   ```

3. **Setup database**:
   - Create a Supabase project
   - Run the SQL migration: `migrations/001_initial_schema.sql`
   - Update .env with your Supabase credentials

4. **Start development server**:
   ```bash
   python run_dev.py
   ```

5. **Access the application**:
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

## 🔧 Configuration

All configuration is managed through environment variables. See `.env.example` for all available options.

### Required Environment Variables

```bash
# Database
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Security
JWT_SECRET_KEY=your-secret-key

# AI Services
OPENAI_API_KEY=your-openai-key

# Lead Generation
APOLLO_API_KEY=your-apollo-key

# Payments
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-webhook-secret

# Email Service
SENDGRID_API_KEY=your-sendgrid-key

# Redis
REDIS_URL=redis://localhost:6379/0
```

## 📊 Current Implementation Status

### ✅ Completed Features

- **Foundation & Infrastructure**
  - Modular monolith project structure
  - FastAPI application with middleware
  - Environment configuration system
  - Logging and error handling
  - Health check endpoints

- **Database & Authentication**
  - Complete database schema design
  - Row Level Security (RLS) policies
  - JWT-based authentication system
  - User registration and login
  - Password hashing and validation
  - Token refresh mechanism
  - Rate limiting for auth endpoints

### 🚧 In Progress

- User management module
- Stripe payment integration
- CrewAI framework integration

### 📋 Upcoming Features

- Apollo.io integration
- Email automation agents
- Campaign management
- Lead scoring and CRM
- Performance monitoring
- LinkedIn outreach automation

## 🧪 Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest

# Run with coverage
pytest --cov=app
```

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Authentication Endpoints

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/password-reset` - Request password reset
- `GET /api/v1/auth/me` - Get current user profile

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Row Level Security (RLS) in database
- Rate limiting on sensitive endpoints
- Input validation and sanitization
- CORS protection
- Security headers middleware

## 🚀 Deployment

Deployment configuration will be added as the project progresses. The application is designed to be containerized with Docker and deployed to cloud platforms.

## 📝 Development Notes

- Follow the modular monolith architecture
- All API keys stored in .env file
- Use type hints and Pydantic models
- Implement comprehensive error handling
- Add logging for all operations
- Write tests for new features

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Follow Python best practices
5. Use type hints consistently

## 📄 License

This project is proprietary software for Selda AI.
