#!/usr/bin/env python3
"""Test script for Goal Understanding Agent"""

import asyncio
import sys
import traceback

async def test_goal_agent():
    """Test the Goal Understanding Agent with purpose detection"""
    try:
        from app.modules.agents.goal_understanding import GoalUnderstandingAgent
        
        print("🧪 Testing Goal Understanding Agent...")
        
        # Initialize agent
        org_id = "test-org-123"
        agent = GoalUnderstandingAgent(org_id)
        
        print(f"✅ Agent created: {agent.name}")
        
        # Test case 1: Goal without purpose (should ask for it)
        print("\n📝 Test 1: Goal without clear purpose")
        goal_input = {
            "goal_description": "I need 5 restaurant clients in London",
            "context": "Test case",
            "user_id": "test-user",
            "organization_id": org_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result = await agent.execute(goal_input)
        
        print(f"📊 Result: {result}")
        print(f"🔍 Is complete: {result.get('is_complete', False)}")
        print(f"❓ Questions: {result.get('clarification_questions', [])}")
        print(f"📋 Missing info: {result.get('missing_information', [])}")
        
        # Test case 2: Goal with purpose (should be complete)
        print("\n📝 Test 2: Goal with clear purpose")
        goal_input_with_purpose = {
            "goal_description": "I need 5 restaurant clients in London to sell my POS system",
            "context": "Test case",
            "user_id": "test-user",
            "organization_id": org_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result2 = await agent.execute(goal_input_with_purpose)
        
        print(f"📊 Result: {result2}")
        print(f"🔍 Is complete: {result2.get('is_complete', False)}")
        print(f"❓ Questions: {result2.get('clarification_questions', [])}")
        print(f"📋 Missing info: {result2.get('missing_information', [])}")
        
        # Test case 3: Follow-up with purpose answer
        print("\n📝 Test 3: Follow-up with purpose answer")
        followup_input = {
            "goal_description": "I need 5 restaurant clients in London",
            "context": "Test case follow-up",
            "user_id": "test-user",
            "organization_id": org_id,
            "conversation_history": [],
            "is_follow_up": True,
            "answered_questions": {"purpose": "POS system"}
        }
        
        result3 = await agent.execute(followup_input)
        
        print(f"📊 Result: {result3}")
        print(f"🔍 Is complete: {result3.get('is_complete', False)}")
        print(f"❓ Questions: {result3.get('clarification_questions', [])}")
        print(f"📋 Missing info: {result3.get('missing_information', [])}")
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Goal Understanding Agent Tests")
    print("="*50)
    
    success = asyncio.run(test_goal_agent())
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
