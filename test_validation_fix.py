#!/usr/bin/env python3
"""
Test script to verify validation error JSON serialization fix
"""

import sys
import json
sys.path.append('.')

def test_password_validation():
    """Test password validation logic"""
    print("🔒 Testing Password Validation Logic...")
    print("=" * 60)
    
    try:
        from app.modules.auth.utils import validate_password_strength
        
        test_cases = [
            ("weak", False, "Too weak"),
            ("password", False, "No uppercase, digits, or special chars"),
            ("Password", False, "No digits or special chars"),
            ("Password123", False, "No special chars"),
            ("Password123!", True, "Valid strong password"),
            ("StrongPass123!", True, "Valid strong password"),
            ("Aa1!", False, "Too short"),
            ("VeryLongPasswordWithoutNumbers!", False, "No digits"),
        ]
        
        all_passed = True
        for password, expected, description in test_cases:
            result = validate_password_strength(password)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{password}' -> {result} ({description})")
            if result != expected:
                all_passed = False
        
        if all_passed:
            print("\n✅ Password validation logic working correctly!")
        else:
            print("\n❌ Some password validation tests failed!")
            
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing password validation: {e}")
        return False

def test_pydantic_validation():
    """Test Pydantic model validation"""
    print("\n📝 Testing Pydantic Model Validation...")
    print("=" * 60)
    
    try:
        from app.modules.auth.models import RegisterRequest
        from pydantic import ValidationError
        
        # Test with invalid password
        print("1. Testing with weak password...")
        try:
            RegisterRequest(
                email="<EMAIL>",
                password="weak",
                first_name="Test",
                last_name="User"
            )
            print("   ❌ Validation should have failed!")
            return False
        except ValidationError as e:
            print("   ✅ Validation error raised correctly")
            
            # Test JSON serialization of the error
            try:
                errors = e.errors()
                print(f"   Raw errors: {errors}")
                
                # Convert to JSON-serializable format (like our fix does)
                serializable_errors = []
                for error in errors:
                    if isinstance(error, dict):
                        serializable_error = {}
                        for key, value in error.items():
                            try:
                                json.dumps(value)
                                serializable_error[key] = value
                            except (TypeError, ValueError):
                                serializable_error[key] = str(value)
                        serializable_errors.append(serializable_error)
                    else:
                        serializable_errors.append(str(error))
                
                # Test JSON serialization
                json_str = json.dumps(serializable_errors)
                print("   ✅ Errors can be serialized to JSON")
                print(f"   Serialized: {json_str}")
                
            except Exception as json_error:
                print(f"   ❌ JSON serialization failed: {json_error}")
                return False
        
        # Test with valid password
        print("\n2. Testing with valid password...")
        try:
            valid_request = RegisterRequest(
                email="<EMAIL>",
                password="StrongPass123!",
                first_name="Test",
                last_name="User"
            )
            print("   ✅ Valid password accepted")
            print(f"   Model: {valid_request}")
        except ValidationError as e:
            print(f"   ❌ Valid password rejected: {e}")
            return False
        
        print("\n✅ Pydantic validation working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Pydantic validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exception_handler_logic():
    """Test the exception handler logic"""
    print("\n🛠️  Testing Exception Handler Logic...")
    print("=" * 60)
    
    try:
        from pydantic import ValidationError
        from app.modules.auth.models import RegisterRequest
        
        # Create a validation error
        try:
            RegisterRequest(
                email="invalid-email",
                password="weak",
                first_name="",
                last_name="User"
            )
        except ValidationError as exc:
            print("   ✅ Generated validation error for testing")
            
            # Simulate the exception handler logic
            errors = []
            for error in exc.errors():
                if isinstance(error, dict):
                    serializable_error = {}
                    for key, value in error.items():
                        try:
                            json.dumps(value)
                            serializable_error[key] = value
                        except (TypeError, ValueError):
                            serializable_error[key] = str(value)
                    errors.append(serializable_error)
                else:
                    errors.append(str(error))
            
            # Create the response structure
            response_content = {
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "Request validation failed",
                    "details": {
                        "errors": errors
                    }
                }
            }
            
            # Test JSON serialization
            try:
                json_response = json.dumps(response_content, indent=2)
                print("   ✅ Exception handler response can be serialized")
                print(f"   Response structure:\n{json_response}")
                return True
            except Exception as e:
                print(f"   ❌ JSON serialization failed: {e}")
                return False
    
    except Exception as e:
        print(f"❌ Error testing exception handler: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Validation Fix Tests...\n")
    
    results = []
    
    # Test password validation
    results.append(test_password_validation())
    
    # Test Pydantic validation
    results.append(test_pydantic_validation())
    
    # Test exception handler logic
    results.append(test_exception_handler_logic())
    
    print("\n" + "=" * 60)
    
    if all(results):
        print("🎉 ALL TESTS PASSED!")
        print("\nKey Fixes Verified:")
        print("✅ Password validation logic working")
        print("✅ Pydantic model validation working")
        print("✅ JSON serialization error fixed")
        print("✅ Exception handler response structure correct")
        print("\n✨ The register endpoint validation error should now work correctly!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        print("\nPlease check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
