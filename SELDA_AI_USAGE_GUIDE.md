# Selda AI Sales Autopilot - Complete Usage Guide

## 🚀 Quick Start

### Prerequisites

1. **Authentication**: You need a valid JWT token from the auth system
2. **Organization**: Must be part of an organization
3. **Campaign**: Create a campaign to run workflows against

### Base URL

```
http://localhost:8000/api/v1
```

## 🔐 Authentication

### 1. Register/Login to get JWT Token

```bash
# Register
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "StrongPass123!",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "organization_name": "My Company"
  }'

# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "StrongPass123!"
  }'
```

**Response:**

```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "organization_id": "org-uuid"
  }
}
```

## 📋 Campaign Management

### 1. Create a Campaign

```bash
curl -X POST "http://localhost:8000/api/v1/campaigns" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Q1 SaaS Outreach",
    "description": "Target SaaS companies in London for our new product",
    "target_audience": {
      "industry": "Technology",
      "location": "London",
      "company_size": "50-200"
    },
    "goals": {
      "meetings_target": 10,
      "contacts_target": 500,
      "conversion_rate_target": 2.0
    },
    "status": "active"
  }'
```

**Response:**

```json
{
  "id": "campaign-uuid",
  "name": "Q1 SaaS Outreach",
  "status": "active",
  "created_at": "2024-01-15T10:00:00Z"
}
```

## 🤖 Sequential Agent Workflow

### 1. Start Complete Workflow (Recommended)

```bash
curl -X POST "http://localhost:8000/api/v1/campaigns/workflow/start" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "campaign_id": "campaign-uuid",
    "target_meetings": 10,
    "target_contacts": 500,
    "target_audience": {
      "industry": "Technology",
      "location": "London",
      "company_size": "50-200",
      "job_titles": ["CEO", "CTO", "VP Engineering"]
    },
    "campaign_goals": {
      "objective": "Generate qualified leads for our SaaS platform",
      "value_proposition": "AI-powered sales automation",
      "pain_points": ["Manual lead generation", "Low conversion rates"]
    },
    "workflow_type": "sales_autopilot"
  }'
```

**Response:**

```json
{
  "workflow_id": "workflow_campaign-uuid_1705320000",
  "status": "started",
  "message": "Campaign workflow started successfully"
}
```

### 2. Monitor Workflow Progress

```bash
curl -X GET "http://localhost:8000/api/v1/campaigns/workflow/workflow_campaign-uuid_1705320000/status" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Response:**

```json
{
  "workflow_id": "workflow_campaign-uuid_1705320000",
  "status": "running",
  "current_stage": "lead_sourcing",
  "stages_completed": 2,
  "total_stages": 9,
  "execution_time_ms": 45000,
  "started_at": "2024-01-15T10:00:00Z",
  "completed_at": null,
  "progress_percentage": 22.2
}
```

### 3. Get Detailed Workflow Results

```bash
curl -X GET "http://localhost:8000/api/v1/campaigns/workflow/workflow_campaign-uuid_1705320000/details" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Response:**

```json
{
  "workflow": {
    "workflow_id": "workflow_campaign-uuid_1705320000",
    "status": "completed",
    "current_stage": "performance_monitoring",
    "stages_completed": 9,
    "total_stages": 9,
    "execution_time_ms": 180000,
    "progress_percentage": 100.0
  },
  "stages": [
    {
      "stage": "goal_understanding",
      "agent_type": "goal_understanding",
      "status": "success",
      "execution_time_ms": 5000,
      "created_at": "2024-01-15T10:00:00Z",
      "output_summary": {
        "goals_identified": 3,
        "target_audience_refined": true
      }
    },
    {
      "stage": "lead_sourcing",
      "agent_type": "lead_sourcing",
      "status": "success",
      "execution_time_ms": 25000,
      "created_at": "2024-01-15T10:02:00Z",
      "output_summary": {
        "leads_sourced": 487,
        "sourcing_summary": {
          "apollo": 487,
          "quality_distribution": {
            "high": 156,
            "medium": 231,
            "low": 100
          }
        }
      }
    },
    {
      "stage": "lead_distribution",
      "agent_type": "lead_distribution",
      "status": "success",
      "execution_time_ms": 8000,
      "created_at": "2024-01-15T10:02:30Z",
      "output_summary": {
        "leads_allocated": 500,
        "distribution_summary": {
          "fulfillment_rate": 100.0,
          "average_quality": 78.5
        }
      }
    }
  ],
  "final_output": {
    "campaign_id": "campaign-uuid",
    "total_leads_sourced": 487,
    "total_leads_allocated": 500,
    "emails_sent": 500,
    "meetings_booked": 12,
    "conversion_rate": 2.4
  }
}
```

## 🔍 Individual Agent Testing

### 1. Test Lead Sourcing Agent

```bash
curl -X POST "http://localhost:8000/api/v1/agents/execute" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "lead_sourcing",
    "input_data": {
      "campaign_id": "campaign-uuid",
      "target_audience": {
        "industry": "Technology",
        "location": "London"
      },
      "location": "London",
      "industry": "Technology",
      "lead_count_target": 100,
      "sources": ["apollo"],
      "quality_threshold": 70
    }
  }'
```

**Expected Output:**

```json
{
  "leads_sourced": 87,
  "leads_by_source": {
    "apollo": 87
  },
  "quality_distribution": {
    "high": 28,
    "medium": 41,
    "low": 18
  },
  "duplicates_found": 13,
  "cooldown_filtered": 5,
  "sourcing_summary": {
    "execution_time_ms": 15000,
    "target_met": true,
    "sources_used": ["apollo"]
  },
  "next_agent_input": {
    "campaign_id": "campaign-uuid",
    "leads_available": 87,
    "target_audience": {...},
    "quality_threshold": 70
  }
}
```

### 2. Test Lead Distribution Agent

```bash
curl -X POST "http://localhost:8000/api/v1/agents/execute" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "lead_distribution",
    "input_data": {
      "campaign_id": "campaign-uuid",
      "leads_requested": 50,
      "target_audience": {
        "industry": "Technology",
        "location": "London"
      },
      "quality_threshold": 70,
      "exclude_domains": ["example.com"],
      "priority_level": 3
    }
  }'
```

**Expected Output:**

```json
{
  "leads_allocated": 50,
  "lead_ids": ["lead-uuid-1", "lead-uuid-2", "..."],
  "quality_stats": {
    "average_score": 78.5,
    "min_score": 70,
    "max_score": 95,
    "score_distribution": {
      "high": 18,
      "medium": 25,
      "low": 7
    },
    "total_leads": 50
  },
  "distribution_summary": {
    "requested": 50,
    "allocated": 50,
    "fulfillment_rate": 100.0,
    "average_quality": 78.5,
    "priority_level": 3
  },
  "next_agent_input": {
    "campaign_id": "campaign-uuid",
    "allocated_leads": ["lead-uuid-1", "..."],
    "lead_count": 50
  }
}
```

### 3. Test Other Agents

```bash
# Strategic Planning Agent
curl -X POST "http://localhost:8000/api/v1/agents/execute" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "strategic_planning",
    "input_data": {
      "campaign_id": "campaign-uuid",
      "target_audience": {...},
      "campaign_goals": {...}
    }
  }'

# Content Creation Agent
curl -X POST "http://localhost:8000/api/v1/agents/execute" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "content_creation",
    "input_data": {
      "campaign_id": "campaign-uuid",
      "allocated_leads": ["lead-uuid-1", "..."],
      "campaign_strategy": {...}
    }
  }'
```

## 📊 Monitoring & Analytics

### 1. List All Organization Workflows

```bash
curl -X GET "http://localhost:8000/api/v1/campaigns/workflow/organization/workflows?limit=10&status_filter=completed" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Get Lead Analytics

```bash
curl -X GET "http://localhost:8000/api/v1/leads/analytics?campaign_id=campaign-uuid" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. Campaign Performance

```bash
curl -X GET "http://localhost:8000/api/v1/analytics/campaigns/campaign-uuid/performance" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🧪 Testing Scenarios

### Scenario 1: Complete Autonomous Workflow

```bash
#!/bin/bash
# Complete workflow test script

TOKEN="YOUR_ACCESS_TOKEN"
BASE_URL="http://localhost:8000/api/v1"

# 1. Create campaign
CAMPAIGN_RESPONSE=$(curl -s -X POST "$BASE_URL/campaigns" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Campaign",
    "description": "Automated test campaign",
    "target_audience": {
      "industry": "Technology",
      "location": "London"
    }
  }')

CAMPAIGN_ID=$(echo $CAMPAIGN_RESPONSE | jq -r '.id')
echo "Created campaign: $CAMPAIGN_ID"

# 2. Start workflow
WORKFLOW_RESPONSE=$(curl -s -X POST "$BASE_URL/campaigns/workflow/start" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"campaign_id\": \"$CAMPAIGN_ID\",
    \"target_meetings\": 5,
    \"target_contacts\": 100,
    \"target_audience\": {
      \"industry\": \"Technology\",
      \"location\": \"London\"
    },
    \"campaign_goals\": {
      \"objective\": \"Test lead generation\"
    }
  }")

WORKFLOW_ID=$(echo $WORKFLOW_RESPONSE | jq -r '.workflow_id')
echo "Started workflow: $WORKFLOW_ID"

# 3. Monitor progress
while true; do
  STATUS_RESPONSE=$(curl -s -X GET "$BASE_URL/campaigns/workflow/$WORKFLOW_ID/status" \
    -H "Authorization: Bearer $TOKEN")

  STATUS=$(echo $STATUS_RESPONSE | jq -r '.status')
  PROGRESS=$(echo $STATUS_RESPONSE | jq -r '.progress_percentage')

  echo "Workflow status: $STATUS ($PROGRESS%)"

  if [ "$STATUS" = "completed" ] || [ "$STATUS" = "failed" ]; then
    break
  fi

  sleep 10
done

# 4. Get final results
curl -s -X GET "$BASE_URL/campaigns/workflow/$WORKFLOW_ID/details" \
  -H "Authorization: Bearer $TOKEN" | jq '.'
```

### Scenario 2: Step-by-Step Agent Testing

```bash
#!/bin/bash
# Individual agent testing script

TOKEN="YOUR_ACCESS_TOKEN"
BASE_URL="http://localhost:8000/api/v1"
CAMPAIGN_ID="your-campaign-uuid"

# Test each agent individually
AGENTS=("goal_understanding" "strategic_planning" "lead_sourcing" "lead_distribution" "content_creation")

for AGENT in "${AGENTS[@]}"; do
  echo "Testing $AGENT agent..."

  RESPONSE=$(curl -s -X POST "$BASE_URL/agents/execute" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"agent_type\": \"$AGENT\",
      \"input_data\": {
        \"campaign_id\": \"$CAMPAIGN_ID\",
        \"target_audience\": {
          \"industry\": \"Technology\",
          \"location\": \"London\"
        }
      }
    }")

  echo "Response: $RESPONSE" | jq '.'
  echo "---"
done
```

## 🔧 Configuration

### Environment Variables Required

```env
# Apollo.io Integration
APOLLO_API_KEY=your_apollo_api_key

# Database
SUPABASE_URL=https://aqjctgvrvtogryxlvwor.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_KEY=your_service_key

# Authentication
JWT_SECRET_KEY=your_jwt_secret
```

### Apollo.io Setup

1. Get API key from Apollo.io dashboard
2. Set `APOLLO_API_KEY` environment variable
3. Test connection with lead sourcing agent

## 📈 Expected Results

### Lead Sourcing Agent Output

- **leads_sourced**: Number of leads successfully sourced
- **quality_distribution**: Breakdown of lead quality scores
- **duplicates_found**: Number of duplicates filtered out
- **cooldown_filtered**: Leads filtered due to 90-day rule

### Lead Distribution Agent Output

- **leads_allocated**: Number of leads assigned to campaign
- **quality_stats**: Quality metrics of allocated leads
- **fulfillment_rate**: Percentage of requested leads fulfilled

### Complete Workflow Output

- **9 sequential stages** executed in order
- **Real-time progress** tracking
- **Detailed stage results** with execution times
- **Final campaign metrics** (leads, emails, meetings)

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Error**: Ensure JWT token is valid and not expired
2. **Campaign Not Found**: Verify campaign ID exists and belongs to your organization
3. **Apollo API Error**: Check API key and rate limits
4. **Workflow Stuck**: Check logs for agent execution errors

### Debug Endpoints

```bash
# Check user info
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer $TOKEN"

# Health check
curl -X GET "http://localhost:8000/health"

# Agent capabilities
curl -X GET "http://localhost:8000/api/v1/agents/types" \
  -H "Authorization: Bearer $TOKEN"
```

## 🧪 Automated Testing

### Run Complete API Test Suite

```bash
# Install dependencies
pip install requests

# Run automated tests
python test_selda_api.py

# Or test against different server
python test_selda_api.py http://your-server.com
```

**Test Coverage:**

- ✅ Health check and system status
- ✅ User registration and authentication
- ✅ Campaign creation and management
- ✅ Individual agent execution
- ✅ Complete workflow execution
- ✅ Progress monitoring and results

### Expected Test Output

```
🚀 Starting Selda AI Sales Autopilot API Tests
============================================================

🏥 Testing Health Check...
==================================================
📡 GET /health -> 200
✅ Health check passed

🔐 Testing Authentication...
==================================================
📝 Registering new user...
📡 POST /api/v1/auth/register -> 201
✅ Registration successful
🔑 Logging in...
📡 POST /api/v1/auth/login -> 200
✅ Login successful
   User ID: user-uuid
   Organization ID: org-uuid

📋 Testing Campaign Creation...
==================================================
📡 POST /api/v1/campaigns -> 201
✅ Campaign created successfully
   Campaign ID: campaign-uuid
   Campaign Name: Test SaaS Outreach Campaign

🤖 Testing Individual Agents...
==================================================
🔍 Testing Lead Sourcing Agent...
📡 POST /api/v1/agents/execute -> 200
✅ Lead Sourcing Agent executed successfully
   Leads Sourced: 87
   Quality Distribution: {'high': 28, 'medium': 41, 'low': 18}

📊 Testing Lead Distribution Agent...
📡 POST /api/v1/agents/execute -> 200
✅ Lead Distribution Agent executed successfully
   Leads Allocated: 25
   Quality Stats: {'average_score': 78.5, 'total_leads': 25}

🔄 Testing Complete Workflow Execution...
==================================================
🚀 Starting workflow...
📡 POST /api/v1/campaigns/workflow/start -> 200
✅ Workflow started successfully
   Workflow ID: workflow_campaign-uuid_1705320000

📊 Monitoring workflow progress...
   Status: running | Progress: 11.1% | Stage: goal_understanding
   Status: running | Progress: 22.2% | Stage: strategic_planning
   Status: running | Progress: 33.3% | Stage: lead_sourcing
   Status: running | Progress: 44.4% | Stage: lead_distribution
   Status: running | Progress: 55.6% | Stage: content_creation
   Status: running | Progress: 66.7% | Stage: email_automation
   Status: running | Progress: 77.8% | Stage: reply_analysis
   Status: running | Progress: 88.9% | Stage: meeting_booking
   Status: completed | Progress: 100.0% | Stage: performance_monitoring

📋 Getting final workflow details...
📡 GET /api/v1/campaigns/workflow/workflow_campaign-uuid_1705320000/details -> 200
✅ Workflow completed successfully
   Final Status: completed
   Stages Completed: 9/9
   Total Execution Time: 180000ms

📊 Stage Results:
   ✅ goal_understanding: 5000ms
      goals_identified: 3
      target_audience_refined: true
   ✅ strategic_planning: 8000ms
      strategy_created: true
      market_research_completed: true
   ✅ lead_sourcing: 25000ms
      leads_sourced: 487
      sourcing_summary: {...}
   ✅ lead_distribution: 8000ms
      leads_allocated: 500
      distribution_summary: {...}
   ✅ content_creation: 12000ms
      content_created: true
      templates_generated: 5
   ✅ email_automation: 45000ms
      emails_sent: 500
      delivery_rate: 98.5
   ✅ reply_analysis: 15000ms
      replies_analyzed: 47
      positive_responses: 23
   ✅ meeting_booking: 20000ms
      meetings_booked: 12
      booking_rate: 2.4
   ✅ performance_monitoring: 5000ms
      metrics_calculated: true
      roi_analysis: completed

============================================================
🎉 ALL TESTS PASSED!

✨ Selda AI Sales Autopilot is working correctly!
   ✅ Health check passed
   ✅ Authentication working
   ✅ Campaign creation working
   ✅ Individual agents working
   ✅ Complete workflow execution working
```

## 📊 Real-World Usage Examples

### Example 1: Technology Company Lead Generation

```bash
# Target SaaS companies in London
curl -X POST "http://localhost:8000/api/v1/campaigns/workflow/start" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "campaign_id": "tech-campaign-001",
    "target_meetings": 15,
    "target_contacts": 750,
    "target_audience": {
      "industry": "Software",
      "location": "London, UK",
      "company_size": "50-500",
      "job_titles": ["CEO", "CTO", "VP Engineering", "Head of Product"],
      "technologies": ["SaaS", "Cloud", "API"]
    },
    "campaign_goals": {
      "objective": "Generate qualified leads for our API management platform",
      "value_proposition": "Reduce API development time by 60%",
      "pain_points": ["Complex API integration", "Poor API documentation", "Slow development cycles"]
    }
  }'
```

### Example 2: Healthcare Industry Outreach

```bash
# Target healthcare companies in multiple locations
curl -X POST "http://localhost:8000/api/v1/campaigns/workflow/start" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "campaign_id": "healthcare-campaign-001",
    "target_meetings": 20,
    "target_contacts": 1000,
    "target_audience": {
      "industry": "Healthcare",
      "location": "New York, Boston, Chicago",
      "company_size": "100-1000",
      "job_titles": ["Chief Medical Officer", "VP Operations", "IT Director"],
      "specialties": ["Digital Health", "Telemedicine", "EHR"]
    },
    "campaign_goals": {
      "objective": "Introduce our HIPAA-compliant communication platform",
      "value_proposition": "Improve patient communication while ensuring compliance",
      "pain_points": ["HIPAA compliance complexity", "Poor patient engagement", "Fragmented communication"]
    }
  }'
```

## 🔍 Advanced Monitoring

### Real-time Workflow Monitoring Script

```python
#!/usr/bin/env python3
import requests
import time
import json

def monitor_workflow(workflow_id, token, base_url="http://localhost:8000"):
    """Monitor workflow progress in real-time"""
    headers = {"Authorization": f"Bearer {token}"}

    while True:
        response = requests.get(
            f"{base_url}/api/v1/campaigns/workflow/{workflow_id}/status",
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            status = data["status"]
            progress = data["progress_percentage"]
            stage = data["current_stage"]

            print(f"[{time.strftime('%H:%M:%S')}] {status} | {progress:.1f}% | {stage}")

            if status in ["completed", "failed"]:
                # Get final details
                details_response = requests.get(
                    f"{base_url}/api/v1/campaigns/workflow/{workflow_id}/details",
                    headers=headers
                )

                if details_response.status_code == 200:
                    details = details_response.json()
                    print("\n📊 Final Results:")
                    print(json.dumps(details["final_output"], indent=2))

                break

        time.sleep(30)  # Check every 30 seconds

# Usage
# monitor_workflow("workflow_id", "your_token")
```

## 📈 Performance Metrics

### Key Metrics to Track

1. **Lead Sourcing Metrics**

   - Leads sourced per hour
   - Quality score distribution
   - Source effectiveness (Apollo vs others)
   - Duplicate rate

2. **Lead Distribution Metrics**

   - Allocation efficiency
   - Quality threshold compliance
   - Cooldown adherence
   - Campaign fulfillment rate

3. **Workflow Metrics**

   - End-to-end execution time
   - Stage success rates
   - Error frequency
   - Resource utilization

4. **Business Metrics**
   - Meeting booking rate
   - Email response rate
   - Conversion to qualified leads
   - ROI per campaign

### Metrics API Endpoints

```bash
# Workflow performance
curl -X GET "http://localhost:8000/api/v1/analytics/workflows/performance" \
  -H "Authorization: Bearer $TOKEN"

# Lead quality analytics
curl -X GET "http://localhost:8000/api/v1/analytics/leads/quality" \
  -H "Authorization: Bearer $TOKEN"

# Campaign ROI analysis
curl -X GET "http://localhost:8000/api/v1/analytics/campaigns/roi" \
  -H "Authorization: Bearer $TOKEN"
```

This comprehensive guide provides everything needed to test, use, and monitor the Selda AI Sales Autopilot system effectively!
