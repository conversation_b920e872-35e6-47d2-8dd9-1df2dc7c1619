#!/usr/bin/env python3
"""
Real Interactive Chat Interface for Selda AI
Users can actually type their goals and responses
"""

import asyncio
import sys
import os
import uuid
from typing import Dict, Any, Optional

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent

class SeldaInteractiveChat:
    """Real interactive chat interface for Selda AI"""
    
    def __init__(self):
        self.organization_id = "00000000-0000-4000-8000-000000000001"
        self.session_id = str(uuid.uuid4())
        
    def print_banner(self):
        """Print welcome banner"""
        print("\n" + "="*70)
        print("🤖 SELDA AI - Interactive Lead Sourcing Assistant")
        print("="*70)
        print("Welcome! I'm your AI assistant for sourcing high-quality sales leads.")
        print("\n💡 How it works:")
        print("  1. Tell me your sales goal in natural language")
        print("  2. I'll ask follow-up questions if needed")
        print("  3. I'll source targeted leads from Apollo.io")
        print("  4. You'll get a complete campaign summary")
        print("\n📝 Example goals:")
        print("  • 'I need 10 meetings with restaurant owners in London'")
        print("  • 'Find me tech startup CEOs in San Francisco for 5 meetings'")
        print("  • 'Source leads for SaaS companies in New York'")
        print("\n⌨️ Commands:")
        print("  • Type 'help' for assistance")
        print("  • Type 'quit' or 'exit' to end session")
        print("  • Just type naturally - I understand!")
        print("-"*70)
        
    def get_user_input(self, prompt: str) -> str:
        """Get real user input"""
        print(f"\n💬 {prompt}")
        print("👤 ", end="", flush=True)
        
        try:
            user_input = input().strip()
            return user_input
        except (KeyboardInterrupt, EOFError):
            print("\n🤖 Session interrupted. Goodbye! 👋")
            sys.exit(0)
            
    def print_ai_response(self, message: str):
        """Print AI response"""
        print(f"\n🤖 {message}")
        
    def show_help(self):
        """Show help information"""
        print("\n" + "="*50)
        print("📚 HELP - How to use Selda AI")
        print("="*50)
        print("🎯 Goal Examples:")
        print("  • 'I need 5 meetings with restaurant owners in London'")
        print("  • 'Find 10 tech CEOs in San Francisco'")
        print("  • 'Source leads for healthcare companies in Boston'")
        print("  • 'Get me 3 meetings with SaaS founders in New York'")
        print("\n📝 What I need to know:")
        print("  • Industry (restaurants, technology, healthcare, etc.)")
        print("  • Location (city, country, or region)")
        print("  • Number of meetings you want")
        print("  • Company size (optional: small, medium, large)")
        print("\n⌨️ Commands:")
        print("  • 'help' - Show this help")
        print("  • 'quit' or 'exit' - End session")
        print("  • Just type naturally - I'll understand!")
        print("-"*50)
        
    def print_separator(self, title: str = ""):
        """Print section separator"""
        if title:
            print(f"\n{'='*20} {title} {'='*20}")
        else:
            print("\n" + "-"*60)
            
    async def handle_goal_understanding(self, initial_goal: str) -> Optional[Dict[str, Any]]:
        """Handle the goal understanding phase with real user interaction"""
        self.print_separator("GOAL UNDERSTANDING")
        
        self.print_ai_response("Let me understand your goal...")
        
        goal_agent = GoalUnderstandingAgent(self.organization_id)
        
        # Process initial goal
        goal_input = {
            "goal_description": initial_goal,
            "context": "Interactive chat interface for lead sourcing",
            "user_id": f"chat-user-{self.session_id}",
            "organization_id": self.organization_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result = await goal_agent.execute(goal_input)
        
        # Check if goal is complete
        if result.get("is_complete", False):
            parsed_goal = result.get('parsed_goal', {})
            target_audience = parsed_goal.get('target_audience', {})
            
            self.print_ai_response("Perfect! I have all the information I need:")
            print(f"   🎯 Industry: {target_audience.get('industry', 'N/A')}")
            print(f"   📍 Location: {target_audience.get('geography', 'N/A')}")
            print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 'N/A')} meetings")
            print(f"   ✅ Confidence: {result.get('confidence_score', 0):.1%}")
            
            return {
                "industry": target_audience.get('industry', ''),
                "location": target_audience.get('geography', ''),
                "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 5),
                "goal_description": initial_goal,
                "complete": True
            }
        
        # Handle follow-up questions
        follow_up_questions = result.get("clarification_questions", [])
        if follow_up_questions:
            self.print_ai_response("I need some additional information to help you better:")
            
            answers = {}
            for i, question in enumerate(follow_up_questions, 1):
                while True:
                    answer = self.get_user_input(f"({i}/{len(follow_up_questions)}) {question}")
                    
                    if answer.lower() == 'help':
                        self.show_help()
                        continue
                    elif answer.lower() in ['quit', 'exit']:
                        self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
                        return None
                    elif answer.strip():
                        break
                    else:
                        self.print_ai_response("Please provide an answer to continue.")
                
                # Map answers to appropriate fields
                if "industry" in question.lower():
                    answers["industry"] = answer
                elif "location" in question.lower() or "where" in question.lower():
                    answers["location"] = answer
                elif "meeting" in question.lower() or "target" in question.lower() or "how many" in question.lower():
                    answers["target_meetings"] = answer
                elif "company size" in question.lower() or "size" in question.lower():
                    answers["company_size"] = answer
                else:
                    answers[f"question_{i}"] = answer
            
            # Process follow-up answers
            self.print_ai_response("Thank you! Let me process your answers...")
            
            follow_up_input = {
                "goal_description": initial_goal,
                "context": "Interactive chat interface follow-up",
                "user_id": f"chat-user-{self.session_id}",
                "organization_id": self.organization_id,
                "conversation_history": [],
                "is_follow_up": True,
                "answered_questions": answers
            }
            
            final_result = await goal_agent.execute(follow_up_input)
            
            if final_result.get("is_complete", False):
                parsed_goal = final_result.get('parsed_goal', {})
                target_audience = parsed_goal.get('target_audience', {})
                
                self.print_ai_response("Excellent! Now I have everything I need:")
                print(f"   🎯 Industry: {target_audience.get('industry', 'N/A')}")
                print(f"   📍 Location: {target_audience.get('geography', 'N/A')}")
                print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 'N/A')} meetings")
                print(f"   ✅ Confidence: {final_result.get('confidence_score', 0):.1%}")
                
                return {
                    "industry": target_audience.get('industry', ''),
                    "location": target_audience.get('geography', ''),
                    "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 5),
                    "goal_description": initial_goal,
                    "complete": True
                }
            else:
                self.print_ai_response("I still need more information. Let me ask a few more questions...")
                return await self.handle_goal_understanding(initial_goal)  # Recursive for additional rounds
        
        return None
        
    async def handle_lead_sourcing(self, goal_result: Dict[str, Any]) -> Dict[str, Any]:
        """Handle the lead sourcing phase"""
        self.print_separator("LEAD SOURCING")
        
        self.print_ai_response("Now I'll source leads for your goal. This may take a moment...")
        
        target_meetings = goal_result.get("target_meetings", 5)
        lead_count_target = target_meetings * 1  # 1 lead per meeting for testing
        
        print(f"\n🔍 Sourcing {lead_count_target} leads for {target_meetings} meetings...")
        print(f"   🎯 Industry: {goal_result.get('industry', 'N/A')}")
        print(f"   📍 Location: {goal_result.get('location', 'N/A')}")
        print(f"   📊 Quality Threshold: 50%")
        
        # Show progress
        print(f"\n⏳ Processing...")
        print("   🔍 Searching Apollo.io database...")
        print("   📧 Enriching contact emails...")
        print("   🎯 Filtering for quality...")
        print("   💾 Storing leads...")
        
        # Execute lead sourcing
        lead_agent = LeadSourcingAgent(self.organization_id)
        
        sourcing_input = {
            "campaign_id": f"chat-campaign-{self.session_id}",
            "target_audience": {
                "industry": goal_result.get("industry", ""),
                "geography": goal_result.get("location", ""),
                "company_size": "any",
                "titles": ["CEO", "Owner", "Manager", "Director"]
            },
            "industry": goal_result.get("industry", ""),
            "location": goal_result.get("location", ""),
            "lead_count_target": lead_count_target,
            "quality_threshold": 50,
            "sources": ["apollo"]
        }
        
        sourcing_result = await lead_agent.execute(sourcing_input)
        
        # Display results
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        duplicates = sourcing_result.get("duplicates_filtered", 0)
        cooldown = sourcing_result.get("cooldown_filtered", 0)
        
        self.print_ai_response("Lead sourcing completed! Here are the results:")
        print(f"   ✅ Leads sourced: {leads_sourced}")
        print(f"   🔄 Duplicates filtered: {duplicates}")
        print(f"   ⏰ Cooldown filtered: {cooldown}")
        
        # Show quality distribution
        quality_dist = sourcing_result.get("quality_distribution", {})
        if quality_dist:
            print(f"   📊 Quality distribution:")
            print(f"      🟢 High quality: {quality_dist.get('high', 0)} leads")
            print(f"      🟡 Medium quality: {quality_dist.get('medium', 0)} leads")
            print(f"      🔴 Low quality: {quality_dist.get('low', 0)} leads")
        
        # Show source breakdown
        sources = sourcing_result.get("leads_by_source", {})
        if sources:
            print(f"   🔗 Sources:")
            for source, count in sources.items():
                print(f"      📡 {source.title()}: {count} leads")
        
        return sourcing_result

    def display_final_summary(self, goal_result: Dict[str, Any], sourcing_result: Dict[str, Any]):
        """Display final summary"""
        self.print_separator("CAMPAIGN SUMMARY")

        leads_sourced = sourcing_result.get("leads_sourced", 0)
        target_meetings = goal_result.get("target_meetings", 0)

        self.print_ai_response("🎉 Your lead sourcing campaign is complete!")
        print(f"\n📋 Campaign Details:")
        print(f"   🎯 Goal: {goal_result.get('goal_description', 'N/A')}")
        print(f"   🏭 Industry: {goal_result.get('industry', 'N/A')}")
        print(f"   🌍 Location: {goal_result.get('location', 'N/A')}")
        print(f"   🎯 Target Meetings: {target_meetings}")
        print(f"   📊 Leads Sourced: {leads_sourced}")

        if leads_sourced > 0:
            ratio = leads_sourced / target_meetings if target_meetings > 0 else 0
            print(f"   📈 Lead-to-Meeting Ratio: {ratio:.1f}:1")

            self.print_ai_response("✅ Success! Your leads are ready for outreach.")
            print("   💡 Next steps:")
            print("      1. Review the sourced leads in your dashboard")
            print("      2. Customize your outreach messages")
            print("      3. Start your email campaign")
            print("      4. Track responses and book meetings")
        else:
            self.print_ai_response("⚠️ No leads were sourced. This could be due to:")
            print("   • Very specific targeting criteria")
            print("   • API limitations or credits")
            print("   • Temporary service issues")
            print("   💡 Try adjusting your criteria or contact support.")

    async def run_chat_session(self):
        """Run the main interactive chat session"""
        try:
            self.print_banner()

            while True:
                # Get initial goal from user
                while True:
                    goal = self.get_user_input("What's your sales goal? (e.g., 'I need 5 meetings with restaurant owners in London')")

                    if goal.lower() == 'help':
                        self.show_help()
                        continue
                    elif goal.lower() in ['quit', 'exit', 'bye']:
                        self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
                        return
                    elif goal.strip():
                        break
                    else:
                        self.print_ai_response("Please enter a valid goal description.")

                try:
                    # Phase 1: Goal Understanding
                    goal_result = await self.handle_goal_understanding(goal)

                    if not goal_result:
                        self.print_ai_response("❌ I couldn't understand your goal completely. Let's try again.")
                        continue

                    # Phase 2: Lead Sourcing
                    sourcing_result = await self.handle_lead_sourcing(goal_result)

                    # Phase 3: Final Summary
                    self.display_final_summary(goal_result, sourcing_result)

                    # Ask if user wants to continue
                    self.print_separator()
                    while True:
                        continue_choice = self.get_user_input("Would you like to create another campaign? (yes/no)")

                        if continue_choice.lower() == 'help':
                            self.show_help()
                            continue
                        elif continue_choice.lower() in ['quit', 'exit']:
                            self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
                            return
                        elif continue_choice.lower() in ['yes', 'y', 'yeah', 'sure', 'ok']:
                            break
                        elif continue_choice.lower() in ['no', 'n', 'nope', 'done']:
                            self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
                            return
                        else:
                            self.print_ai_response("Please answer 'yes' or 'no'.")

                except Exception as e:
                    self.print_ai_response(f"❌ An error occurred: {str(e)}")
                    print("Let's try again with a different goal.")
                    continue

        except KeyboardInterrupt:
            self.print_ai_response("\n👋 Session interrupted. Goodbye!")
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")

async def main():
    """Main entry point"""
    chat = SeldaInteractiveChat()
    await chat.run_chat_session()

if __name__ == "__main__":
    print("🚀 Starting Selda AI Interactive Chat...")
    asyncio.run(main())
