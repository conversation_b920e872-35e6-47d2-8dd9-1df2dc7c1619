#!/usr/bin/env python3
"""
Test script for the direct goal-to-Apollo.io workflow
Tests the simplified workflow that goes directly from goal understanding to Apollo.io lead sourcing
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "testpassword123"

class DirectApolloWorkflowTester:
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=BASE_URL)
        self.token = None
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def authenticate(self):
        """Authenticate and get access token"""
        try:
            # Try to register first, then login
            register_data = {
                "email": TEST_USER_EMAIL,
                "password": TEST_USER_PASSWORD,
                "first_name": "Test",
                "last_name": "User"
            }

            # Try to register (ignore if user already exists)
            register_response = await self.client.post("/api/v1/auth/register", json=register_data)

            # Now try to login with JSON format
            login_data = {
                "email": TEST_USER_EMAIL,
                "password": TEST_USER_PASSWORD
            }

            response = await self.client.post("/api/v1/auth/login", json=login_data)

            if response.status_code == 200:
                result = response.json()
                self.token = result["access_token"]
                print(f"✅ Authenticated successfully")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_headers(self):
        """Get headers with authentication"""
        return {"Authorization": f"Bearer {self.token}"}
    
    async def test_direct_apollo_workflow(self):
        """Test direct goal-to-Apollo.io workflow"""
        print("\n🧪 Testing direct goal-to-Apollo.io workflow...")
        
        goal_request = {
            "sales_goal": "I need 5 restaurant clients in London for our POS system",
            "context": "We provide modern point-of-sale systems for restaurants"
        }
        
        try:
            response = await self.client.post(
                "/api/v1/workflow/start-from-goal",
                json=goal_request,
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Goal workflow response: {result['message']}")
                print(f"   Complete: {result['is_complete']}")
                print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
                
                if result['is_complete']:
                    print(f"   Apollo Workflow ID: {result.get('workflow_id', 'Not found')}")
                    
                    # Check parsed goal details
                    parsed_goal = result.get('parsed_goal', {})
                    target_audience = parsed_goal.get('target_audience', {})
                    print(f"   Industry: {target_audience.get('industry', 'Not detected')}")
                    print(f"   Location: {target_audience.get('geography', 'Not detected')}")
                    print(f"   Target Count: {parsed_goal.get('metrics', {}).get('target_value', 'Not detected')}")
                    
                    return result
                else:
                    print(f"   Follow-up questions: {result.get('follow_up_questions', [])}")
                    return result
            else:
                print(f"❌ Goal workflow failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Goal workflow error: {e}")
            return None
    
    async def test_incomplete_goal_with_followup(self):
        """Test incomplete goal that requires follow-up for Apollo.io"""
        print("\n🧪 Testing incomplete goal with follow-up...")
        
        # Start with incomplete goal
        goal_request = {
            "sales_goal": "I want 3 new clients for my business",
            "context": "We need to grow our customer base"
        }
        
        try:
            response = await self.client.post(
                "/api/v1/workflow/start-from-goal",
                json=goal_request,
                headers=self.get_headers()
            )
            
            if response.status_code != 200:
                print(f"❌ Initial goal request failed: {response.status_code}")
                return None
            
            result = response.json()
            print(f"✅ Initial goal processed: {result['message']}")
            print(f"   Complete: {result['is_complete']}")
            
            if not result['is_complete']:
                conversation_id = result['conversation_id']
                questions = result.get('follow_up_questions', [])
                print(f"   Follow-up questions: {questions}")
                
                # Answer follow-up questions to enable Apollo.io sourcing
                answers = {
                    "industry": "restaurants",
                    "location": "London, UK"
                }
                
                followup_request = {
                    "conversation_id": conversation_id,
                    "answers": answers
                }
                
                print(f"   Answering follow-up questions: {answers}")
                
                response = await self.client.post(
                    "/api/v1/workflow/answer-follow-up",
                    json=followup_request,
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    final_result = response.json()
                    print(f"✅ Follow-up processed: {final_result['message']}")
                    print(f"   Complete: {final_result['is_complete']}")
                    
                    if final_result['is_complete']:
                        print(f"   Apollo Workflow ID: {final_result.get('workflow_id', 'Not found')}")
                        
                        # Check final parsed goal
                        parsed_goal = final_result.get('parsed_goal', {})
                        target_audience = parsed_goal.get('target_audience', {})
                        print(f"   Final Industry: {target_audience.get('industry', 'Not detected')}")
                        print(f"   Final Location: {target_audience.get('geography', 'Not detected')}")
                    
                    return final_result
                else:
                    print(f"❌ Follow-up failed: {response.status_code}")
                    return None
            
            return result
            
        except Exception as e:
            print(f"❌ Follow-up workflow error: {e}")
            return None
    
    async def test_apollo_api_connection(self):
        """Test Apollo.io API connection"""
        print("\n🧪 Testing Apollo.io API connection...")
        
        try:
            response = await self.client.get(
                "/api/v1/apollo/leads/auth-test",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                print("✅ Apollo.io API connection successful")
                return True
            else:
                print(f"❌ Apollo.io API connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Apollo.io API test error: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all direct Apollo workflow tests"""
        print("🚀 Starting Direct Apollo.io Workflow Tests")
        print("=" * 50)
        
        # Authenticate
        if not await self.authenticate():
            return False
        
        # Test Apollo.io API connection first
        apollo_connected = await self.test_apollo_api_connection()
        if not apollo_connected:
            print("⚠️  Apollo.io API not connected - workflow tests may fail")
        
        # Test 1: Direct goal-to-Apollo workflow
        complete_result = await self.test_direct_apollo_workflow()
        
        # Test 2: Incomplete goal with follow-up leading to Apollo
        followup_result = await self.test_incomplete_goal_with_followup()
        
        print("\n📊 Test Summary:")
        print(f"   Complete Goal Test: {'✅ Passed' if complete_result else '❌ Failed'}")
        print(f"   Follow-up Goal Test: {'✅ Passed' if followup_result else '❌ Failed'}")
        print(f"   Apollo.io Connection: {'✅ Connected' if apollo_connected else '❌ Not Connected'}")
        
        print("\n✅ All direct Apollo.io workflow tests completed!")
        return True


async def main():
    """Main test function"""
    async with DirectApolloWorkflowTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
