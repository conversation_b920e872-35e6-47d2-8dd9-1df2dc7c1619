-- Migration 002: Update leads table for enhanced CRM functionality

-- Add new values to the lead_status ENUM
-- Note: We are adding these without transactions because ALTER TYPE cannot be in a transaction block.
ALTER TYPE lead_status ADD VALUE IF NOT EXISTS 'unused';
ALTER TYPE lead_status ADD VALUE IF NOT EXISTS 'reserved';
ALTER TYPE lead_status ADD VALUE IF NOT EXISTS 'blacklisted';
ALTER TYPE lead_status ADD VALUE IF NOT EXISTS 'cooldown';

-- Alter the leads table to match new requirements
ALTER TABLE leads
    -- Rename existing columns for clarity
    RENAME COLUMN email TO contact_email,
    RENAME COLUMN score TO contact_quality_score,

    -- Add new columns
    ADD COLUMN IF NOT EXISTS company_id TEXT,
    ADD COLUMN IF NOT EXISTS industry VARCHAR(100),
    ADD COLUMN IF NOT EXISTS location VARCHAR(255),
    ADD COLUMN IF NOT EXISTS used_by_campaigns JSONB DEFAULT '[]'::jsonb,
    ADD COLUMN IF NOT EXISTS last_contacted_at TIMESTAMP WITH TIME ZONE;

-- Update the status column default value
-- First, we'll update existing 'new' leads to 'unused'
UPDATE leads SET status = 'unused' WHERE status = 'new';
-- Then, alter the column to set the new default
ALTER TABLE leads ALTER COLUMN status SET DEFAULT 'unused';

-- Add comments for new columns for better documentation
COMMENT ON COLUMN leads.company_id IS 'Unique identifier for the company from external sources like Apollo.io';
COMMENT ON COLUMN leads.industry IS 'Industry of the company (e.g., Restaurants, Software)';
COMMENT ON COLUMN leads.location IS 'Geographic location of the company (e.g., London)';
COMMENT ON COLUMN leads.used_by_campaigns IS 'Array of campaign IDs that have used this lead';
COMMENT ON COLUMN leads.last_contacted_at IS 'Timestamp of the last time this lead was contacted';
COMMENT ON COLUMN leads.contact_quality_score IS 'A score from 0-100 indicating the quality of the lead based on interactions';

-- Update the unique constraint to use the new column name
ALTER TABLE leads DROP CONSTRAINT IF EXISTS leads_organization_id_email_key;
ALTER TABLE leads ADD CONSTRAINT leads_organization_id_contact_email_key UNIQUE (organization_id, contact_email); 