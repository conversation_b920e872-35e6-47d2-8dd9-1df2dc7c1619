from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any
from app.modules.leads.apollo_service import apollo_service

apollo_router = APIRouter(prefix="/leads/apollo", tags=["Apollo.io"])

@apollo_router.get("/auth-test", summary="Test Apollo.io authentication")
async def test_apollo_auth():
    ok = await apollo_service.authenticate()
    if not ok:
        raise HTTPException(status_code=401, detail="Apollo.io authentication failed")
    return {"success": True, "message": "Authenticated with Apollo.io"}

@apollo_router.get("/leads/search", summary="Search leads on Apollo.io")
async def search_leads(q: Optional[str] = Query(None), page: int = 1, per_page: int = 10):
    query = {"q": q, "page": page, "per_page": per_page}
    try:
        leads = await apollo_service.search_leads(query)
        return {"success": True, "leads": leads}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Apollo.io lead search failed: {e}")

@apollo_router.get("/companies/search", summary="Search companies on Apollo.io")
async def search_companies(q: Optional[str] = Query(None), page: int = 1, per_page: int = 10):
    query = {"q": q, "page": page, "per_page": per_page}
    try:
        companies = await apollo_service.search_companies(query)
        return {"success": True, "companies": companies}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Apollo.io company search failed: {e}")

@apollo_router.get("/contacts/enrich", summary="Enrich contact by email on Apollo.io")
async def enrich_contact(email: str):
    try:
        person = await apollo_service.enrich_contact(email)
        if not person:
            raise HTTPException(status_code=404, detail="No enrichment found for this email")
        return {"success": True, "person": person}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Apollo.io contact enrichment failed: {e}") 