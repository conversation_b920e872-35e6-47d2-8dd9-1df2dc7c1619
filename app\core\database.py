"""
Database connection and utilities for Supabase
"""

from typing import Optional, Dict, Any, List
from supabase import create_client, Client
import logging
from app.core.config import settings
from app.core.exceptions import ExternalServiceError

logger = logging.getLogger(__name__)


class SupabaseService:
    """Service class for Supabase database operations"""
    
    def __init__(self):
        self._client: Optional[Client] = None
        self._admin_client: Optional[Client] = None
    
    @property
    def client(self) -> Client:
        """Get Supabase client with anon key (for RLS)"""
        if not self._client:
            try:
                self._client = create_client(
                    settings.supabase_url,
                    settings.supabase_anon_key
                )
                logger.info("Supabase client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                raise ExternalServiceError(
                    message="Failed to connect to database",
                    details={"error": str(e)}
                )
        return self._client
    
    @property
    def admin_client(self) -> Client:
        """Get Supabase admin client (bypasses RLS)"""
        if not self._admin_client:
            try:
                self._admin_client = create_client(
                    settings.supabase_url,
                    settings.supabase_service_key
                )
                logger.info("Supabase admin client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase admin client: {e}")
                raise ExternalServiceError(
                    message="Failed to connect to database with admin privileges",
                    details={"error": str(e)}
                )
        return self._admin_client
    
    async def health_check(self) -> bool:
        """Check if database connection is healthy"""
        try:
            # Simple query to test connection
            response = self.client.table("organizations").select("id").limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    def set_auth(self, access_token: str) -> None:
        """Set authentication token for RLS"""
        try:
            self.client.auth.set_session(access_token, "")
            logger.debug("Authentication token set for Supabase client")
        except Exception as e:
            logger.error(f"Failed to set auth token: {e}")
            raise ExternalServiceError(
                message="Failed to authenticate with database",
                details={"error": str(e)}
            )
    
    def clear_auth(self) -> None:
        """Clear authentication token"""
        try:
            self.client.auth.sign_out()
            logger.debug("Authentication cleared for Supabase client")
        except Exception as e:
            logger.warning(f"Failed to clear auth token: {e}")


class BaseRepository:
    """Base repository class for database operations"""

    def __init__(self, table_name: str, supabase_service: SupabaseService):
        self.table_name = table_name
        self.supabase = supabase_service

    def _get_table(self, use_admin: bool = False, client: Client = None):
        """Get table reference"""
        if client:
            # Use provided client (usually with auth context for RLS)
            return client.table(self.table_name)
        elif use_admin:
            # Use admin client (bypasses RLS)
            return self.supabase.admin_client.table(self.table_name)
        else:
            # Use regular client (respects RLS but needs auth context)
            return self.supabase.client.table(self.table_name)
    
    async def create(self, data: Dict[str, Any], use_admin: bool = False, client: Client = None) -> Dict[str, Any]:
        """Create a new record"""
        try:
            response = self._get_table(use_admin, client).insert(data).execute()
            if response.data:
                logger.debug(f"Created record in {self.table_name}: {response.data[0]['id']}")
                return response.data[0]
            else:
                raise ExternalServiceError(f"Failed to create record in {self.table_name}")
        except Exception as e:
            logger.error(f"Error creating record in {self.table_name}: {e}")
            raise ExternalServiceError(
                message=f"Failed to create {self.table_name} record",
                details={"error": str(e)}
            )
    
    async def get_by_id(self, record_id: str, use_admin: bool = False, client: Client = None) -> Optional[Dict[str, Any]]:
        """Get record by ID"""
        try:
            response = self._get_table(use_admin, client).select("*").eq("id", record_id).execute()
            if response.data:
                return response.data[0]
            return None
        except Exception as e:
            logger.error(f"Error getting record from {self.table_name}: {e}")
            raise ExternalServiceError(
                message=f"Failed to get {self.table_name} record",
                details={"error": str(e)}
            )

    async def update(self, record_id: str, data: Dict[str, Any], use_admin: bool = False, client: Client = None) -> Dict[str, Any]:
        """Update record by ID"""
        try:
            response = self._get_table(use_admin, client).update(data).eq("id", record_id).execute()
            if response.data:
                logger.debug(f"Updated record in {self.table_name}: {record_id}")
                return response.data[0]
            else:
                raise ExternalServiceError(f"Failed to update record in {self.table_name}")
        except Exception as e:
            logger.error(f"Error updating record in {self.table_name}: {e}")
            raise ExternalServiceError(
                message=f"Failed to update {self.table_name} record",
                details={"error": str(e)}
            )
    
    async def delete(self, record_id: str, use_admin: bool = False, client: Client = None) -> bool:
        """Delete record by ID"""
        try:
            response = self._get_table(use_admin, client).delete().eq("id", record_id).execute()
            logger.debug(f"Deleted record from {self.table_name}: {record_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting record from {self.table_name}: {e}")
            raise ExternalServiceError(
                message=f"Failed to delete {self.table_name} record",
                details={"error": str(e)}
            )
    
    async def list(
        self,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 20,
        offset: int = 0,
        order_by: str = "created_at",
        ascending: bool = False,
        use_admin: bool = False,
        client: Client = None
    ) -> List[Dict[str, Any]]:
        """List records with optional filters and pagination"""
        try:
            query = self._get_table(use_admin, client).select("*")

            # Apply filters
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            # Apply ordering
            query = query.order(order_by, desc=not ascending)

            # Apply pagination
            query = query.range(offset, offset + limit - 1)

            response = query.execute()
            return response.data or []
        except Exception as e:
            logger.error(f"Error listing records from {self.table_name}: {e}")
            raise ExternalServiceError(
                message=f"Failed to list {self.table_name} records",
                details={"error": str(e)}
            )
    
    async def count(
        self,
        filters: Optional[Dict[str, Any]] = None,
        use_admin: bool = False,
        client: Client = None
    ) -> int:
        """Count records with optional filters"""
        try:
            query = self._get_table(use_admin, client).select("id", count="exact")

            # Apply filters
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            response = query.execute()
            return response.count or 0
        except Exception as e:
            logger.error(f"Error counting records in {self.table_name}: {e}")
            raise ExternalServiceError(
                message=f"Failed to count {self.table_name} records",
                details={"error": str(e)}
            )


# Global instance
supabase_service = SupabaseService()
