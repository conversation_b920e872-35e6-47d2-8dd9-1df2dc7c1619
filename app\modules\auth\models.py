"""
Authentication models and schemas
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator
from app.modules.auth.utils import validate_password_strength


class LoginRequest(BaseModel):
    """Login request model"""
    email: EmailStr
    password: str = Field(..., min_length=1)


class LoginResponse(BaseModel):
    """Login response model"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds
    user: dict


class RegisterRequest(BaseModel):
    """User registration request model"""
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=100)
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    organization_name: Optional[str] = Field(None, min_length=1, max_length=100)
    
    @validator('password')
    def validate_password(cls, v):
        if not validate_password_strength(v):
            raise ValueError(
                'Password must contain at least 8 characters with uppercase, '
                'lowercase, digit, and special character'
            )
        return v


class RegisterResponse(BaseModel):
    """User registration response model"""
    message: str
    user_id: str
    email_verification_sent: bool = True


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """Refresh token response model"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds


class PasswordResetRequest(BaseModel):
    """Password reset request model"""
    email: EmailStr


class PasswordResetResponse(BaseModel):
    """Password reset response model"""
    message: str


class PasswordResetConfirm(BaseModel):
    """Password reset confirmation model"""
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_password(cls, v):
        if not validate_password_strength(v):
            raise ValueError(
                'Password must contain at least 8 characters with uppercase, '
                'lowercase, digit, and special character'
            )
        return v


class EmailVerificationRequest(BaseModel):
    """Email verification request model"""
    token: str


class EmailVerificationResponse(BaseModel):
    """Email verification response model"""
    message: str
    email_verified: bool = True


class ChangePasswordRequest(BaseModel):
    """Change password request model"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator('new_password')
    def validate_password(cls, v):
        if not validate_password_strength(v):
            raise ValueError(
                'Password must contain at least 8 characters with uppercase, '
                'lowercase, digit, and special character'
            )
        return v


class ChangePasswordResponse(BaseModel):
    """Change password response model"""
    message: str


class LogoutResponse(BaseModel):
    """Logout response model"""
    message: str


class TokenValidationResponse(BaseModel):
    """Token validation response model"""
    valid: bool
    user_id: Optional[str] = None
    expires_at: Optional[int] = None  # Unix timestamp


class UserProfile(BaseModel):
    """User profile model for auth responses"""
    id: str
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool
    email_verified: bool
    organization_id: Optional[str] = None
    created_at: str
    updated_at: Optional[str] = None


class AuthError(BaseModel):
    """Authentication error model"""
    code: str
    message: str
    details: Optional[dict] = None
