"""
Custom middleware for the application
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import redis
from app.core.config import settings

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        logger.info(
            "Request started",
            extra={
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent"),
            }
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            "Request completed",
            extra={
                "method": request.method,
                "url": str(request.url),
                "status_code": response.status_code,
                "process_time": round(process_time, 4),
                "client_ip": request.client.host if request.client else None,
            }
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware using Redis"""
    
    def __init__(self, app, calls_per_minute: int = None):
        super().__init__(app)
        self.calls_per_minute = calls_per_minute or settings.rate_limit_per_minute
        self.redis_client = None
        
        # Initialize Redis client
        try:
            self.redis_client = redis.from_url(settings.redis_url)
        except Exception as e:
            logger.warning(f"Failed to initialize Redis for rate limiting: {e}")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting if Redis is not available
        if not self.redis_client:
            return await call_next(request)
        
        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/health/detailed"]:
            return await call_next(request)
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Create rate limit key
        rate_limit_key = f"rate_limit:{client_ip}:{int(time.time() // 60)}"
        
        try:
            # Check current request count
            current_requests = self.redis_client.get(rate_limit_key)
            current_requests = int(current_requests) if current_requests else 0
            
            if current_requests >= self.calls_per_minute:
                logger.warning(
                    "Rate limit exceeded",
                    extra={
                        "client_ip": client_ip,
                        "requests": current_requests,
                        "limit": self.calls_per_minute
                    }
                )
                
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": {
                            "code": "RATE_LIMIT_EXCEEDED",
                            "message": "Too many requests. Please try again later.",
                            "details": {
                                "limit": self.calls_per_minute,
                                "window": "1 minute"
                            }
                        }
                    }
                )
            
            # Increment request count
            pipe = self.redis_client.pipeline()
            pipe.incr(rate_limit_key)
            pipe.expire(rate_limit_key, 60)  # Expire after 1 minute
            pipe.execute()
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # Continue without rate limiting if Redis fails
        
        return await call_next(request)


class CacheMiddleware(BaseHTTPMiddleware):
    """Simple caching middleware for GET requests"""
    
    def __init__(self, app, cache_ttl: int = 300):
        super().__init__(app)
        self.cache_ttl = cache_ttl
        self.redis_client = None
        
        # Initialize Redis client
        try:
            self.redis_client = redis.from_url(settings.redis_url)
        except Exception as e:
            logger.warning(f"Failed to initialize Redis for caching: {e}")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip caching if Redis is not available or not a GET request
        if not self.redis_client or request.method != "GET":
            return await call_next(request)
        
        # Skip caching for certain paths
        skip_paths = ["/health", "/health/detailed", "/docs", "/redoc", "/openapi.json"]
        if request.url.path in skip_paths:
            return await call_next(request)
        
        # Create cache key
        cache_key = f"cache:{request.method}:{request.url.path}:{str(request.query_params)}"
        
        try:
            # Try to get cached response
            cached_response = self.redis_client.get(cache_key)
            if cached_response:
                logger.debug(f"Cache hit for {cache_key}")
                return Response(
                    content=cached_response,
                    media_type="application/json",
                    headers={"X-Cache": "HIT"}
                )
            
            # Process request
            response = await call_next(request)
            
            # Cache successful responses
            if response.status_code == 200:
                # Get response body
                response_body = b""
                async for chunk in response.body_iterator:
                    response_body += chunk
                
                # Cache the response
                self.redis_client.setex(cache_key, self.cache_ttl, response_body)
                logger.debug(f"Cached response for {cache_key}")
                
                # Return response with cache miss header
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=dict(response.headers, **{"X-Cache": "MISS"}),
                    media_type=response.media_type
                )
            
        except Exception as e:
            logger.error(f"Caching error: {e}")
        
        return await call_next(request)
