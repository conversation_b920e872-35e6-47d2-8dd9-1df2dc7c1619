"""
Goal-to-Workflow Router - New endpoint for starting workflows from sales goals
Handles goal interpretation with follow-ups and sequential agent execution
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.shared.dependencies import (
    get_current_active_user,
    get_current_organization
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/workflow", tags=["Goal Workflow"])


class StartFromGoalRequest(BaseModel):
    """Request to start workflow from a sales goal"""
    sales_goal: str = Field(..., min_length=10, max_length=2000, description="Natural language sales goal")
    context: Optional[str] = Field(None, max_length=1000, description="Additional business context")


class FollowUpAnswerRequest(BaseModel):
    """Request to answer follow-up questions"""
    conversation_id: str = Field(..., description="Conversation ID from initial goal request")
    answers: Dict[str, str] = Field(..., description="Answers to follow-up questions")


class GoalWorkflowResponse(BaseModel):
    """Response for goal workflow requests"""
    success: bool
    conversation_id: str
    is_complete: bool
    message: str
    follow_up_questions: Optional[List[str]] = None
    workflow_id: Optional[str] = None
    parsed_goal: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None


# In-memory conversation storage (in production, use Redis or database)
conversations = {}


@router.post("/start-from-goal", response_model=GoalWorkflowResponse)
async def start_workflow_from_goal(
    request: StartFromGoalRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Start a workflow from a sales goal with follow-up questions if needed"""
    try:
        conversation_id = f"conv_{uuid.uuid4().hex[:8]}"
        
        # Initialize goal understanding agent
        goal_agent = GoalUnderstandingAgent(organization["id"])
        
        # Prepare input for goal understanding
        goal_input = {
            "goal_description": request.sales_goal,
            "context": request.context,
            "user_id": current_user["id"],
            "organization_id": organization["id"],
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        # Execute goal understanding
        result = await goal_agent.execute(goal_input)
        
        # Store conversation state
        conversations[conversation_id] = {
            "user_id": current_user["id"],
            "organization_id": organization["id"],
            "initial_goal": request.sales_goal,
            "context": request.context,
            "goal_result": result,
            "answered_questions": {}
        }
        
        # Check if goal understanding is complete
        if result.get("is_complete", False):
            # Goal is complete, start the workflow
            workflow_id = await _start_complete_workflow(
                result, conversation_id, current_user, organization
            )
            
            return GoalWorkflowResponse(
                success=True,
                conversation_id=conversation_id,
                is_complete=True,
                message="Goal understood! Your sales workflow has been started.",
                workflow_id=workflow_id,
                parsed_goal=result.get("parsed_goal"),
                confidence_score=result.get("confidence_score")
            )
        else:
            # Need follow-up questions
            questions = result.get("clarification_questions", [])
            
            return GoalWorkflowResponse(
                success=True,
                conversation_id=conversation_id,
                is_complete=False,
                message="I need a bit more information to start your sales campaign:",
                follow_up_questions=questions,
                parsed_goal=result.get("parsed_goal"),
                confidence_score=result.get("confidence_score")
            )
            
    except Exception as e:
        logger.error(f"Failed to start workflow from goal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process your sales goal"
        )


@router.post("/answer-follow-up", response_model=GoalWorkflowResponse)
async def answer_follow_up_questions(
    request: FollowUpAnswerRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Answer follow-up questions and continue goal understanding"""
    try:
        # Get conversation state
        conversation = conversations.get(request.conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Verify user access
        if conversation["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this conversation"
            )
        
        # Update answered questions
        conversation["answered_questions"].update(request.answers)
        
        # Re-run goal understanding with answers
        goal_agent = GoalUnderstandingAgent(organization["id"])
        
        goal_input = {
            "goal_description": conversation["initial_goal"],
            "context": conversation["context"],
            "user_id": current_user["id"],
            "organization_id": organization["id"],
            "conversation_history": [],
            "is_follow_up": True,
            "answered_questions": conversation["answered_questions"]
        }
        
        # Execute goal understanding with answers
        result = await goal_agent.execute(goal_input)
        conversation["goal_result"] = result
        
        # Check if goal understanding is now complete
        if result.get("is_complete", False):
            # Goal is complete, start the workflow
            workflow_id = await _start_complete_workflow(
                result, request.conversation_id, current_user, organization
            )
            
            return GoalWorkflowResponse(
                success=True,
                conversation_id=request.conversation_id,
                is_complete=True,
                message="Perfect! Your sales workflow has been started.",
                workflow_id=workflow_id,
                parsed_goal=result.get("parsed_goal"),
                confidence_score=result.get("confidence_score")
            )
        else:
            # Still need more information
            questions = result.get("clarification_questions", [])
            
            return GoalWorkflowResponse(
                success=True,
                conversation_id=request.conversation_id,
                is_complete=False,
                message="I need just a bit more information:",
                follow_up_questions=questions,
                parsed_goal=result.get("parsed_goal"),
                confidence_score=result.get("confidence_score")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to process follow-up answers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process your answers"
        )


async def _start_complete_workflow(
    goal_result: Dict[str, Any],
    conversation_id: str,
    current_user: dict,
    organization: dict
) -> str:
    """Start Apollo.io lead sourcing directly after goal understanding is complete"""
    try:
        parsed_goal = goal_result.get("parsed_goal", {})

        # Extract target meetings from goal
        target_meetings = 10  # Default
        metrics = parsed_goal.get("metrics", {})
        if metrics.get("target_value"):
            try:
                target_meetings = int(metrics["target_value"])
            except (ValueError, TypeError):
                target_meetings = 10

        # Calculate target contacts (10 leads per client as updated)
        target_contacts = target_meetings * 10

        # Create campaign ID
        campaign_id = f"campaign_{uuid.uuid4().hex[:8]}"

        # Start Apollo.io lead sourcing directly
        workflow_id = await _start_apollo_lead_sourcing(
            parsed_goal=parsed_goal,
            campaign_id=campaign_id,
            organization_id=organization["id"],
            target_contacts=target_contacts
        )

        logger.info(f"Started Apollo.io lead sourcing {workflow_id} from goal conversation {conversation_id}")

        return workflow_id

    except Exception as e:
        logger.error(f"Failed to start Apollo.io lead sourcing: {e}")
        raise


async def _start_apollo_lead_sourcing(
    parsed_goal: Dict[str, Any],
    campaign_id: str,
    organization_id: str,
    target_contacts: int
) -> str:
    """Start Apollo.io lead sourcing agent directly"""
    try:
        from app.modules.agents.lead_sourcing import LeadSourcingAgent
        from app.modules.agents.models import LeadSourcingInput

        # Initialize lead sourcing agent
        lead_agent = LeadSourcingAgent(organization_id)

        # Extract target audience information
        target_audience = parsed_goal.get("target_audience", {})

        # Prepare lead sourcing input
        sourcing_input = LeadSourcingInput(
            campaign_id=campaign_id,
            organization_id=organization_id,
            industry=target_audience.get("industry", ""),
            location=target_audience.get("geography", ""),
            company_size="any",  # Default to any size
            lead_count_target=target_contacts,
            quality_threshold=50,  # Minimum quality score
            sources=["apollo"]  # Only use Apollo.io
        )

        # Execute lead sourcing
        logger.info(f"Starting Apollo.io lead sourcing for {target_contacts} leads")
        result = await lead_agent.execute(sourcing_input.model_dump())

        # Generate workflow ID for tracking
        workflow_id = f"apollo_workflow_{uuid.uuid4().hex[:8]}"

        logger.info(f"Apollo.io lead sourcing completed: {result.get('leads_sourced', 0)} leads sourced")

        return workflow_id

    except Exception as e:
        logger.error(f"Failed to execute Apollo.io lead sourcing: {e}")
        raise


@router.get("/conversation/{conversation_id}/status")
async def get_conversation_status(
    conversation_id: str,
    current_user: dict = Depends(get_current_active_user)
):
    """Get the status of a goal conversation"""
    try:
        conversation = conversations.get(conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Verify user access
        if conversation["user_id"] != current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this conversation"
            )
        
        return {
            "conversation_id": conversation_id,
            "initial_goal": conversation["initial_goal"],
            "is_complete": conversation["goal_result"].get("is_complete", False),
            "answered_questions": conversation["answered_questions"],
            "parsed_goal": conversation["goal_result"].get("parsed_goal"),
            "confidence_score": conversation["goal_result"].get("confidence_score")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get conversation status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation status"
        )
