-- Campaign Planning Tables Migration
-- This migration adds tables for comprehensive campaign planning functionality

-- Campaign Plans table
CREATE TABLE IF NOT EXISTS campaign_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    campaign_id VARCHAR(255) NOT NULL,
    goal_description TEXT NOT NULL,
    target_meetings INTEGER NOT NULL,
    target_industry VARCHAR(255),
    target_location VARCHAR(255),
    
    -- Lead volume calculations
    total_leads_needed INTEGER NOT NULL,
    email_open_rate DECIMAL(5,2) DEFAULT 30.0,
    response_rate DECIMAL(5,2) DEFAULT 8.0,
    meeting_rate DECIMAL(5,2) DEFAULT 40.0,
    buffer_percentage DECIMAL(5,2) DEFAULT 20.0,
    
    -- Source distribution
    apollo_leads INTEGER DEFAULT 0,
    linkedin_leads INTEGER DEFAULT 0,
    local_directory_leads INTEGER DEFAULT 0,
    
    -- Campaign timeline
    total_duration_days INTEGER NOT NULL,
    sourcing_duration_days INTEGER NOT NULL,
    
    -- Status and metadata
    status VARCHAR(50) DEFAULT 'planned',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Campaign Timeline Phases table
CREATE TABLE IF NOT EXISTS campaign_timeline_phases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_plan_id UUID NOT NULL REFERENCES campaign_plans(id) ON DELETE CASCADE,
    phase_name VARCHAR(255) NOT NULL,
    phase_type VARCHAR(100) NOT NULL, -- 'sourcing', 'initial_outreach', 'follow_up', 'break_up'
    start_day INTEGER NOT NULL,
    end_day INTEGER NOT NULL,
    description TEXT,
    leads_per_day INTEGER DEFAULT 100,
    frequency_description VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Sequence Templates table
CREATE TABLE IF NOT EXISTS email_sequence_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_plan_id UUID NOT NULL REFERENCES campaign_plans(id) ON DELETE CASCADE,
    sequence_number INTEGER NOT NULL,
    phase_id UUID REFERENCES campaign_timeline_phases(id) ON DELETE CASCADE,
    subject_template VARCHAR(500),
    content_strategy TEXT,
    cta_template VARCHAR(500),
    day_offset INTEGER NOT NULL,
    -- AI-generated email content fields
    generated_content TEXT,
    personalization_variables JSONB DEFAULT '[]',
    content_score DECIMAL(3,2) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Sourcing Strategies table
CREATE TABLE IF NOT EXISTS lead_sourcing_strategies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    campaign_id VARCHAR(255) NOT NULL,
    industry VARCHAR(255) NOT NULL,
    goal_description TEXT,
    
    -- Keyword strategy
    primary_keywords TEXT NOT NULL,
    related_keywords JSONB DEFAULT '[]',
    keywords_used JSONB DEFAULT '[]',
    
    -- Sourcing attempts
    total_attempts INTEGER DEFAULT 0,
    successful_attempts INTEGER DEFAULT 0,
    leads_per_attempt JSONB DEFAULT '{}',
    
    -- Results
    total_leads_sourced INTEGER DEFAULT 0,
    target_leads INTEGER NOT NULL,
    success_rate DECIMAL(5,2) DEFAULT 0.0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Prioritization Scores table (enhanced lead scoring)
CREATE TABLE IF NOT EXISTS lead_prioritization_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    campaign_plan_id UUID REFERENCES campaign_plans(id) ON DELETE CASCADE,
    
    -- Scoring components
    quality_score INTEGER DEFAULT 0,
    title_relevance_score INTEGER DEFAULT 0,
    company_size_score INTEGER DEFAULT 0,
    contact_completeness_score INTEGER DEFAULT 0,
    
    -- Weighted final score
    final_priority_score DECIMAL(5,2) DEFAULT 0.0,
    priority_tier VARCHAR(50) DEFAULT 'medium', -- 'high', 'medium', 'low'
    
    -- Contact timing
    recommended_contact_day INTEGER,
    contact_sequence_position INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Campaign Execution Tracking table
CREATE TABLE IF NOT EXISTS campaign_execution_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_plan_id UUID NOT NULL REFERENCES campaign_plans(id) ON DELETE CASCADE,
    phase_id UUID REFERENCES campaign_timeline_phases(id) ON DELETE CASCADE,
    
    -- Execution status
    phase_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'paused'
    leads_contacted INTEGER DEFAULT 0,
    emails_sent INTEGER DEFAULT 0,
    responses_received INTEGER DEFAULT 0,
    meetings_booked INTEGER DEFAULT 0,
    
    -- Performance metrics
    open_rate DECIMAL(5,2) DEFAULT 0.0,
    response_rate DECIMAL(5,2) DEFAULT 0.0,
    meeting_conversion_rate DECIMAL(5,2) DEFAULT 0.0,
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_campaign_plans_org_id ON campaign_plans(organization_id);
CREATE INDEX IF NOT EXISTS idx_campaign_plans_campaign_id ON campaign_plans(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_timeline_phases_plan_id ON campaign_timeline_phases(campaign_plan_id);
CREATE INDEX IF NOT EXISTS idx_lead_sourcing_strategies_org_id ON lead_sourcing_strategies(organization_id);
CREATE INDEX IF NOT EXISTS idx_lead_sourcing_strategies_campaign_id ON lead_sourcing_strategies(campaign_id);
CREATE INDEX IF NOT EXISTS idx_lead_prioritization_scores_lead_id ON lead_prioritization_scores(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_prioritization_scores_campaign_id ON lead_prioritization_scores(campaign_plan_id);
CREATE INDEX IF NOT EXISTS idx_campaign_execution_tracking_plan_id ON campaign_execution_tracking(campaign_plan_id);

-- Add updated_at trigger for campaign_plans
CREATE OR REPLACE FUNCTION update_campaign_plans_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_campaign_plans_updated_at
    BEFORE UPDATE ON campaign_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_campaign_plans_updated_at();

-- Add updated_at trigger for lead_sourcing_strategies
CREATE TRIGGER update_lead_sourcing_strategies_updated_at
    BEFORE UPDATE ON lead_sourcing_strategies
    FOR EACH ROW
    EXECUTE FUNCTION update_campaign_plans_updated_at();

-- Add updated_at trigger for lead_prioritization_scores
CREATE TRIGGER update_lead_prioritization_scores_updated_at
    BEFORE UPDATE ON lead_prioritization_scores
    FOR EACH ROW
    EXECUTE FUNCTION update_campaign_plans_updated_at();

-- Add updated_at trigger for campaign_execution_tracking
CREATE TRIGGER update_campaign_execution_tracking_updated_at
    BEFORE UPDATE ON campaign_execution_tracking
    FOR EACH ROW
    EXECUTE FUNCTION update_campaign_plans_updated_at();
