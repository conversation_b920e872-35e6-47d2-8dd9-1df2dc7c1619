# <PERSON>lda AI HTTP Testing Guide

## 🚀 Quick Start

### Prerequisites
1. **VS Code** with **REST Client** extension installed
2. **Selda AI backend** running on `http://localhost:8000`
3. **Apollo.io API key** configured (for lead sourcing tests)

### Setup
1. Open `selda_ai_test_requests.http` in VS Code
2. Ensure the backend server is running
3. Start testing from the top of the file

## 📋 Testing Flow (Correct Selda AI Workflow)

### Step 1: Authentication
```http
### Register New User
POST {{baseUrl}}/api/v1/auth/register
# Creates new user and organization

### Login User  
POST {{baseUrl}}/api/v1/auth/login
# Returns access token for subsequent requests
```

### Step 2: Chat Interface (Natural Language Goal Input)
```http
### Start Goal Conversation
POST {{baseUrl}}/api/v1/chat/goal/start
{
  "goal_description": "I want to get 10 restaurant clients in London",
  "context": "We provide POS systems for restaurants"
}

### Handle Follow-up Response
POST {{baseUrl}}/api/v1/chat/goal/followup
{
  "conversation_id": "{{conversationId}}",
  "user_response": "My budget is $5000, timeline is 3 months"
}
```

### Step 3: Sequential Agent Testing
```http
### Test each agent in order using previous agent's output:
1. Goal Understanding Agent
2. Strategic Planning Agent  
3. Lead Sourcing Agent
4. Lead Distribution Agent
5. Content Creation Agent
6. Email Automation Agent
7. Reply Analysis Agent
8. Meeting Booking Agent
9. Performance Monitoring Agent
```

### Step 4: Complete Automated Workflow
```http
### Start Complete Workflow
POST {{baseUrl}}/api/v1/campaigns/workflow/start
# Runs all 9 agents sequentially

### Monitor Progress
GET {{baseUrl}}/api/v1/campaigns/workflow/{{workflowId}}/status
# Track real-time progress
```

## 🔧 How to Use the .http File

### Using VS Code REST Client

1. **Install Extension:**
   - Open VS Code
   - Install "REST Client" extension by Huachao Mao

2. **Open File:**
   - Open `selda_ai_test_requests.http`
   - You'll see "Send Request" links above each HTTP request

3. **Execute Requests:**
   - Click "Send Request" above any HTTP block
   - Results appear in a new tab
   - Variables are automatically populated from responses

### Variable System
The file uses variables that auto-populate from responses:
```http
@token = {{auth_response.response.body.access_token}}
@conversationId = {{goal_start_response.response.body.conversation_id}}
@workflowId = {{workflow_start_response.response.body.workflow_id}}
```

## 📊 Test Scenarios Included

### 1. **Complete Selda AI Flow**
- Natural language goal input
- Follow-up question handling
- Sequential agent execution
- Automated workflow monitoring

### 2. **Individual Agent Testing**
- Test each of the 9 agents separately
- Verify agent output chaining
- Check agent capabilities

### 3. **Industry-Specific Scenarios**
- **Restaurant POS Systems** (default scenario)
- **SaaS API Management** 
- **Healthcare Communication**
- **E-commerce Logistics**
- **Financial Services**

### 4. **Error Testing**
- Invalid input validation
- Authentication errors
- Missing data handling

### 5. **Performance Testing**
- Bulk lead sourcing
- High-volume agent execution
- Workflow stress testing

## 🎯 Key Test Sequences

### Sequence A: Chat Interface Flow
```
1. POST /chat/goal/start (natural language input)
2. POST /chat/goal/followup (answer questions)  
3. GET /conversation/{id}/status (check completion)
```

### Sequence B: Manual Agent Chain
```
1. POST /agents/execute (goal_understanding)
2. POST /agents/execute (strategic_planning) 
3. POST /agents/execute (lead_sourcing)
4. POST /agents/execute (lead_distribution)
5. ... continue with remaining agents
```

### Sequence C: Automated Workflow
```
1. POST /campaigns (create campaign)
2. POST /campaigns/workflow/start (start workflow)
3. GET /campaigns/workflow/{id}/status (monitor)
4. GET /campaigns/workflow/{id}/details (get results)
```

## 📈 Expected Results

### Chat Interface Response:
```json
{
  "conversation_id": "conv_abc123",
  "agent_response": "I understand you want to get 10 restaurant clients...",
  "questions": [
    "What's your timeline for achieving this goal?",
    "What's your budget for this sales campaign?"
  ],
  "is_complete": false,
  "confidence_score": 0.6
}
```

### Agent Execution Response:
```json
{
  "leads_sourced": 487,
  "quality_distribution": {"high": 156, "medium": 231, "low": 100},
  "next_agent_input": {
    "leads_available": 487,
    "quality_stats": {...}
  }
}
```

### Workflow Status Response:
```json
{
  "workflow_id": "workflow_123",
  "status": "running",
  "current_stage": "lead_sourcing", 
  "progress_percentage": 33.3,
  "stages_completed": 3
}
```

## 🚨 Troubleshooting

### Common Issues:

1. **401 Unauthorized**
   - Run authentication requests first
   - Check if token is properly set

2. **404 Not Found**
   - Ensure backend server is running
   - Check endpoint URLs

3. **422 Validation Error**
   - Check request body format
   - Ensure required fields are provided

4. **500 Internal Server Error**
   - Check server logs
   - Verify database connection
   - Ensure Apollo API key is configured

### Debug Steps:
```http
### Check Health
GET {{baseUrl}}/health

### Verify Auth
GET {{baseUrl}}/api/v1/auth/me
Authorization: Bearer {{token}}

### Check Agent Types
GET {{baseUrl}}/api/v1/agents/types
Authorization: Bearer {{token}}
```

## 🔄 Testing Best Practices

### 1. **Sequential Testing**
- Always start with authentication
- Follow the logical flow order
- Use previous responses as input

### 2. **Variable Management**
- Let variables auto-populate
- Update base URL if needed
- Replace placeholder UUIDs with real values

### 3. **Error Handling**
- Test both success and error cases
- Verify error messages are helpful
- Check status codes

### 4. **Performance**
- Test with realistic data volumes
- Monitor response times
- Check for memory leaks

## 📝 Customization

### Update Base URL:
```http
@baseUrl = http://your-server.com
```

### Add Custom Scenarios:
```http
### Your Custom Test
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "Your custom goal here",
  "context": "Your business context"
}
```

### Environment Variables:
Create `.env` file for sensitive data:
```env
APOLLO_API_KEY=your_apollo_key
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=TestPass123!
```

This HTTP testing file provides comprehensive coverage of the entire Selda AI Sales Autopilot system according to the proposal requirements!
