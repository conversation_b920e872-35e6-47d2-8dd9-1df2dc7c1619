"""
Lead Sourcing Agent - Sources leads from multiple channels according to campaign requirements
"""

import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field

from app.modules.agents.base import SeldaAgent, agent_registry
from app.modules.agents.models import AgentType
from app.modules.leads.apollo_service import ApolloService
from app.modules.leads.models import LeadCreate, LeadStatus, LeadFilter
from app.core.database import BaseRepository

logger = logging.getLogger(__name__)


class LeadSourcingInput(BaseModel):
    """Input model for lead sourcing agent"""
    campaign_id: str = Field(..., description="Campaign ID requesting leads")
    target_audience: Dict[str, Any] = Field(..., description="Target audience criteria")
    location: Optional[str] = Field(None, description="Geographic location filter")
    industry: Optional[str] = Field(None, description="Industry filter")
    company_size: Optional[str] = Field(None, description="Company size filter")
    lead_count_target: int = Field(500, description="Target number of leads to source")
    sources: List[str] = Field(default=["apollo"], description="Lead sources to use")
    quality_threshold: int = Field(70, description="Minimum quality score for leads")
    keyword_strategy: Optional[Dict[str, Any]] = Field(None, description="Keyword strategy from campaign planning")
    campaign_plan_id: Optional[str] = Field(None, description="Campaign plan ID for tracking")


class LeadSourcingOutput(BaseModel):
    """Output model for lead sourcing agent"""
    leads_sourced: int = Field(..., description="Number of leads successfully sourced")
    leads_by_source: Dict[str, int] = Field(..., description="Breakdown by source")
    quality_distribution: Dict[str, int] = Field(..., description="Quality score distribution")
    duplicates_found: int = Field(..., description="Number of duplicates filtered out")
    cooldown_filtered: int = Field(..., description="Number of leads filtered due to cooldown")
    sourcing_summary: Dict[str, Any] = Field(..., description="Summary of sourcing operation")
    next_agent_input: Dict[str, Any] = Field(..., description="Input for next agent in pipeline")


@agent_registry.register(AgentType.LEAD_SOURCING)
class LeadSourcingAgent(SeldaAgent):
    """Agent for sourcing leads from multiple channels"""

    @property
    def agent_type(self) -> AgentType:
        return AgentType.LEAD_SOURCING

    @property
    def name(self) -> str:
        return "Lead Sourcing Specialist"

    @property
    def description(self) -> str:
        return "Sources high-quality leads from Apollo.io, LinkedIn, and other channels while preventing duplicates and managing cooldowns"

    def _get_agent_goal(self) -> str:
        return """Source high-quality leads from multiple channels (Apollo.io, LinkedIn, local directories) 
        based on campaign requirements, filter duplicates, manage cooldowns, and ensure lead quality."""

    def _get_agent_backstory(self) -> str:
        return """You are a lead sourcing specialist with expertise in B2B data acquisition, 
        lead quality assessment, and multi-channel sourcing strategies. You ensure optimal 
        lead volume while maintaining quality and preventing over-contact."""

    def get_capabilities(self) -> List[Dict[str, Any]]:
        return [
            {
                "name": "apollo_sourcing",
                "description": "Source leads from Apollo.io API",
                "parameters": {"location": "string", "industry": "string", "company_size": "string"}
            },
            {
                "name": "duplicate_detection",
                "description": "Detect and filter duplicate leads",
                "parameters": {"email_domain": "string", "company_name": "string"}
            },
            {
                "name": "quality_scoring",
                "description": "Score lead quality based on multiple factors",
                "parameters": {"contact_info": "object", "company_info": "object"}
            },
            {
                "name": "cooldown_management",
                "description": "Manage lead cooldown periods to prevent over-contact",
                "parameters": {"domain": "string", "last_contact_days": "integer"}
            }
        ]

    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute lead sourcing logic"""
        try:
            sourcing_input = LeadSourcingInput(**input_data)
            
            # Initialize repositories
            leads_repo = BaseRepository("leads", self.db)
            sourcing_log_repo = BaseRepository("lead_sourcing_log", self.db)
            
            # Track sourcing metrics
            sourcing_start = datetime.utcnow()
            leads_by_source = {}
            quality_distribution = {"high": 0, "medium": 0, "low": 0}
            duplicates_found = 0
            cooldown_filtered = 0
            total_sourced = 0
            
            # 1. Source from Apollo.io with enhanced keyword strategy
            if "apollo" in sourcing_input.sources:
                apollo_result = await self._source_from_apollo_with_keyword_strategy(sourcing_input, leads_repo)
                apollo_leads = apollo_result["leads"]
                retry_duplicates = apollo_result["duplicates_filtered"]
                keywords_used = apollo_result["keywords_used"]

                processed_apollo = await self._process_and_filter_leads(
                    apollo_leads, sourcing_input, leads_repo
                )
                leads_by_source["apollo"] = processed_apollo["imported"]
                duplicates_found += processed_apollo["duplicates"] + retry_duplicates
                cooldown_filtered += processed_apollo["cooldown_filtered"]
                total_sourced += processed_apollo["imported"]

                # Update quality distribution
                for lead in processed_apollo["leads"]:
                    score = lead.get("contact_quality_score", 0)
                    if score >= 80:
                        quality_distribution["high"] += 1
                    elif score >= 60:
                        quality_distribution["medium"] += 1
                    else:
                        quality_distribution["low"] += 1

                # Update sourcing strategy in database
                if sourcing_input.campaign_plan_id:
                    await self._update_sourcing_strategy(
                        sourcing_input.campaign_plan_id, keywords_used, total_sourced
                    )
            
            # 2. Source from LinkedIn (placeholder for future implementation)
            if "linkedin" in sourcing_input.sources:
                # TODO: Implement LinkedIn sourcing
                leads_by_source["linkedin"] = 0
            
            # 3. Source from local directories (placeholder)
            if "local_directories" in sourcing_input.sources:
                # TODO: Implement local directory sourcing
                leads_by_source["local_directories"] = 0
            
            # Log sourcing operation
            execution_time = int((datetime.now() - sourcing_start).total_seconds() * 1000)
            await sourcing_log_repo.create({
                "organization_id": self.organization_id,
                "source": "multi_channel",
                "query_params": sourcing_input.model_dump(),
                "leads_found": total_sourced + duplicates_found + cooldown_filtered,
                "leads_imported": total_sourced,
                "leads_duplicates": duplicates_found,
                "execution_time_ms": execution_time
            }, use_admin=True)
            
            # Create output for next agent
            next_agent_input = {
                "campaign_id": sourcing_input.campaign_id,
                "leads_available": total_sourced,
                "target_audience": sourcing_input.target_audience,
                "quality_threshold": sourcing_input.quality_threshold,
                "sourcing_summary": {
                    "total_sourced": total_sourced,
                    "sources_used": list(leads_by_source.keys()),
                    "quality_distribution": quality_distribution
                }
            }
            
            output = LeadSourcingOutput(
                leads_sourced=total_sourced,
                leads_by_source=leads_by_source,
                quality_distribution=quality_distribution,
                duplicates_found=duplicates_found,
                cooldown_filtered=cooldown_filtered,
                sourcing_summary={
                    "execution_time_ms": execution_time,
                    "target_met": total_sourced >= sourcing_input.lead_count_target * 0.8,
                    "sources_used": list(leads_by_source.keys())
                },
                next_agent_input=next_agent_input
            )
            
            return output.model_dump()
            
        except Exception as e:
            logger.error(f"Lead sourcing execution failed: {e}")
            raise

    async def _source_from_apollo_with_retry(
        self,
        sourcing_input: LeadSourcingInput,
        leads_repo: BaseRepository
    ) -> List[Dict[str, Any]]:
        """Source from Apollo.io with retry logic to handle duplicates"""
        all_leads = []
        target_count = sourcing_input.lead_count_target
        max_attempts = 3
        attempt = 1

        logger.info(f"🎯 Starting Apollo sourcing with target: {target_count} leads")

        while len(all_leads) < target_count and attempt <= max_attempts:
            logger.info(f"🔄 Attempt {attempt}/{max_attempts} - Current leads: {len(all_leads)}, Need: {target_count - len(all_leads)}")

            # Calculate how many more leads we need
            remaining_needed = target_count - len(all_leads)

            # Source more leads than needed to account for potential duplicates
            # Increase the sourcing count by 50% to account for duplicates
            sourcing_count = max(remaining_needed, int(remaining_needed * 1.5))

            # Create a modified sourcing input for this attempt
            modified_input = LeadSourcingInput(
                campaign_id=sourcing_input.campaign_id,
                target_audience=sourcing_input.target_audience,
                industry=sourcing_input.industry,
                location=sourcing_input.location,
                lead_count_target=sourcing_count,
                quality_threshold=sourcing_input.quality_threshold,
                sources=sourcing_input.sources,
                company_size=sourcing_input.company_size
            )

            # Source leads for this attempt
            attempt_leads = await self._source_from_apollo(modified_input, start_page=attempt)

            if not attempt_leads:
                logger.warning(f"❌ Attempt {attempt}: No leads returned from Apollo")
                break

            # Filter out leads we already have
            new_leads = []
            for lead in attempt_leads:
                email = lead.get("contact_email", "")
                if email and not any(existing.get("contact_email") == email for existing in all_leads):
                    # Quick duplicate check against database
                    is_duplicate = await self._check_duplicate(email, leads_repo)
                    if not is_duplicate:
                        new_leads.append(lead)

            logger.info(f"✅ Attempt {attempt}: Found {len(new_leads)} new leads (filtered {len(attempt_leads) - len(new_leads)} duplicates)")

            all_leads.extend(new_leads)
            attempt += 1

            # If we got very few new leads, break to avoid infinite loops
            if len(new_leads) < 2 and attempt > 1:
                logger.info(f"⚠️ Breaking retry loop - only {len(new_leads)} new leads found")
                break

        logger.info(f"🏁 Apollo sourcing completed: {len(all_leads)} total leads after {attempt-1} attempts")
        return all_leads[:target_count]  # Return only what we need

    async def _source_from_apollo_with_keyword_strategy(
        self,
        sourcing_input: LeadSourcingInput,
        leads_repo: BaseRepository
    ) -> Dict[str, Any]:
        """Source from Apollo.io using enhanced keyword strategy"""
        all_leads = []
        total_duplicates_filtered = 0
        keywords_used = []
        target_count = sourcing_input.lead_count_target

        # Get keyword strategy from input or generate default
        keyword_strategy = sourcing_input.keyword_strategy or self._generate_default_keyword_strategy(
            sourcing_input.industry, ""
        )

        logger.info(f"🎯 Starting Apollo sourcing with keyword strategy")
        logger.info(f"🔑 Primary keywords: {keyword_strategy.get('primary_keywords', '')}")
        logger.info(f"🔗 Related keywords available: {len(keyword_strategy.get('related_keywords', []))}")

        print(f"\n🔍 Enhanced Keyword Sourcing Strategy:")
        print(f"   🎯 Primary keywords: {keyword_strategy.get('primary_keywords', '')}")
        print(f"   🔗 Related keywords available: {len(keyword_strategy.get('related_keywords', []))} variations")
        print(f"   📊 Target: {target_count} leads")

        # Try primary keywords first
        primary_keywords = keyword_strategy.get("primary_keywords", sourcing_input.industry)
        keywords_used.append(primary_keywords)

        logger.info(f"🔍 Attempt 1: Using primary keywords: '{primary_keywords}'")
        print(f"\n   🔍 Attempt 1: Using primary keywords: '{primary_keywords}'")

        # Create modified sourcing input with primary keywords
        modified_input = LeadSourcingInput(
            campaign_id=sourcing_input.campaign_id,
            target_audience=sourcing_input.target_audience,
            location=sourcing_input.location,
            industry=primary_keywords,  # Use enhanced keywords
            company_size=sourcing_input.company_size,
            lead_count_target=target_count,
            quality_threshold=sourcing_input.quality_threshold,
            sources=sourcing_input.sources
        )

        try:
            primary_leads = await self._source_from_apollo(modified_input)

            # Filter duplicates
            new_leads = []
            attempt_duplicates = 0
            for lead in primary_leads:
                email = lead.get("contact_email", "")
                if email:
                    is_duplicate = await self._check_duplicate(email, leads_repo)
                    if not is_duplicate:
                        new_leads.append(lead)
                    else:
                        attempt_duplicates += 1

            all_leads.extend(new_leads)
            total_duplicates_filtered += attempt_duplicates

            logger.info(f"✅ Primary keywords yielded: {len(new_leads)} leads (filtered {attempt_duplicates} duplicates)")
            print(f"   ✅ Primary keywords yielded: {len(new_leads)} leads (filtered {attempt_duplicates} duplicates)")

            # If we need more leads, try related keywords
            related_keywords = keyword_strategy.get("related_keywords", [])
            if len(all_leads) < target_count * 0.8 and related_keywords:  # If we got less than 80% of target
                logger.info(f"🔄 Need more leads. Trying related keywords...")
                print(f"\n   🔄 Need more leads ({len(all_leads)}/{target_count}). Trying related keywords...")

                for i, related_keyword in enumerate(related_keywords[:3]):  # Try up to 3 variations
                    if len(all_leads) >= target_count:
                        break

                    logger.info(f"🔍 Attempt {i+2}: Using related keywords: '{related_keyword}'")
                    print(f"   🔍 Attempt {i+2}: Using related keywords: '{related_keyword}'")
                    keywords_used.append(related_keyword)

                    # Update sourcing input with new keywords
                    modified_input.industry = related_keyword
                    modified_input.lead_count_target = target_count - len(all_leads)

                    additional_leads = await self._source_from_apollo(modified_input)

                    # Filter duplicates for this attempt
                    new_additional_leads = []
                    additional_duplicates = 0
                    for lead in additional_leads:
                        email = lead.get("contact_email", "")
                        if email and not any(existing.get("contact_email") == email for existing in all_leads):
                            is_duplicate = await self._check_duplicate(email, leads_repo)
                            if not is_duplicate:
                                new_additional_leads.append(lead)
                            else:
                                additional_duplicates += 1

                    all_leads.extend(new_additional_leads)
                    total_duplicates_filtered += additional_duplicates

                    logger.info(f"✅ Related keywords yielded: {len(new_additional_leads)} additional leads")
                    print(f"   ✅ Related keywords yielded: {len(new_additional_leads)} additional leads")
                    logger.info(f"📊 Total leads so far: {len(all_leads)}")
                    print(f"   📊 Total leads so far: {len(all_leads)}")

            logger.info(f"🏁 Keyword strategy sourcing completed: {len(all_leads)} total leads")
            print(f"\n   🏁 Keyword strategy sourcing completed: {len(all_leads)} total leads")
            logger.info(f"📊 Total duplicates filtered: {total_duplicates_filtered}")
            print(f"   📊 Total duplicates filtered: {total_duplicates_filtered}")

            return {
                "leads": all_leads[:target_count],
                "duplicates_filtered": total_duplicates_filtered,
                "keywords_used": keywords_used
            }

        except Exception as e:
            logger.error(f"Apollo keyword strategy sourcing failed: {e}")
            return {
                "leads": [],
                "duplicates_filtered": 0,
                "keywords_used": keywords_used
            }

    def _generate_default_keyword_strategy(self, industry: str, goal_description: str = "") -> Dict[str, Any]:
        """Generate default keyword strategy if not provided by campaign planning"""
        # Generic keyword mapping - prioritize simple terms for better lead volume
        keyword_mapping = {
            "restaurant": {
                "primary": "restaurant",
                "related": ["restaurant", "food", "dining", "hospitality"]
            },
            "restaurants": {
                "primary": "restaurant",
                "related": ["restaurant", "food", "dining", "hospitality"]
            },
            "healthcare": {
                "primary": "hospital",
                "related": ["hospital", "healthcare", "medical", "clinic"]
            },
            "hospital": {
                "primary": "hospital",
                "related": ["hospital", "healthcare", "medical", "clinic"]
            },
            "technology": {
                "primary": "technology",
                "related": ["technology", "software", "tech", "IT"]
            },
            "retail": {
                "primary": "retail",
                "related": ["retail", "store", "shop", "commerce"]
            }
        }

        base_industry = industry.lower() if industry else ""
        keywords_data = keyword_mapping.get(base_industry, {
            "primary": industry or "",
            "related": [industry, "business", "company"] if industry else []
        })

        return {
            "primary_keywords": keywords_data["primary"],
            "related_keywords": keywords_data["related"],
            "total_variations": len(keywords_data["related"]) + 1,
            "strategy": "Generic keywords for high lead volume"
        }

    async def _update_sourcing_strategy(
        self,
        campaign_plan_id: str,
        keywords_used: List[str],
        total_leads_sourced: int
    ) -> None:
        """Update sourcing strategy in database with results"""
        try:
            sourcing_strategies_repo = BaseRepository("lead_sourcing_strategies", self.db)

            # Find the strategy record for this campaign
            strategies = await sourcing_strategies_repo.list(
                filters={"campaign_id": campaign_plan_id}, use_admin=True
            )

            if strategies:
                strategy = strategies[0]

                # Update the strategy with results
                update_data = {
                    "keywords_used": keywords_used,
                    "total_attempts": len(keywords_used),
                    "successful_attempts": len([k for k in keywords_used if k]),
                    "total_leads_sourced": total_leads_sourced,
                    "success_rate": (total_leads_sourced / strategy.get("target_leads", 1)) * 100 if strategy.get("target_leads") else 0
                }

                await sourcing_strategies_repo.update(
                    strategy["id"], update_data, use_admin=True
                )

                logger.info(f"Updated sourcing strategy for campaign {campaign_plan_id}")

        except Exception as e:
            logger.error(f"Failed to update sourcing strategy: {e}")

    async def _source_from_apollo(self, sourcing_input: LeadSourcingInput, start_page: int = 1) -> List[Dict[str, Any]]:
        """Enhanced Apollo.io sourcing with people search and bulk enrichment"""
        try:
            apollo_service = ApolloService()

            # Build Apollo people search filters with enhanced keywords (matching official API format)
            search_filters = {
                "q_organization_locations": sourcing_input.location,
                "q_keywords": sourcing_input.industry,  # Use keywords for broader industry matching
                "person_titles": ["CEO", "Owner", "Manager", "Director", "VP"],
                "contact_email_status": ["verified"]  # Only verified emails (correct parameter name)
            }

            # Log the search strategy
            logger.info(f"🔍 Apollo search strategy: Using q_keywords='{sourcing_input.industry}' for broader industry matching")

            # Add company size filter if specified
            if sourcing_input.company_size and sourcing_input.company_size != "any":
                if "small" in sourcing_input.company_size.lower():
                    search_filters["organization_num_employees_ranges"] = ["1,10", "11,50"]
                elif "medium" in sourcing_input.company_size.lower():
                    search_filters["organization_num_employees_ranges"] = ["51,200", "201,500"]
                elif "large" in sourcing_input.company_size.lower():
                    search_filters["organization_num_employees_ranges"] = ["501,1000", "1001,5000"]

            # Search people with pagination to get target count
            logger.info(f"Searching Apollo.io for {sourcing_input.lead_count_target} leads (starting from page {start_page})")
            people_data = await apollo_service.search_people_with_pagination(
                filters=search_filters,
                target_count=sourcing_input.lead_count_target,
                start_page=start_page
            )

            if not people_data:
                logger.warning("No people found from Apollo.io search")
                return []

            # Extract person IDs for bulk enrichment
            person_ids = [person.get("id") for person in people_data if person.get("id")]

            # Perform bulk enrichment to get real email addresses
            enriched_people = []
            if person_ids:
                logger.info(f"Bulk enriching {len(person_ids)} contacts to unlock emails")
                enriched_people = await apollo_service.bulk_enrich_people(person_ids)

                if enriched_people:
                    # Create a mapping of person_id to enriched data
                    enriched_map = {person.get("id"): person for person in enriched_people if person.get("id")}

                    # Count real emails
                    real_emails_count = sum(1 for person in enriched_people
                                         if person.get("email") and "email_not_unlocked" not in person.get("email", ""))

                    logger.info(f"Bulk enrichment API succeeded: {len(enriched_people)} contacts returned")
                    logger.info(f"Real emails unlocked: {real_emails_count}")

                    # Replace original data with enriched data where available
                    for i, person in enumerate(people_data):
                        person_id = person.get("id")
                        if person_id in enriched_map:
                            people_data[i] = enriched_map[person_id]

                    # Log sample emails for debugging
                    sample_emails = [person.get("email", "N/A") for person in enriched_people[:3]]
                    logger.info(f"Sample emails after enrichment: {sample_emails}")

                else:
                    logger.error("Bulk enrichment API failed - no data returned")
                    # Continue with original data, filtering will happen later

            # Convert to lead format
            leads_data = []
            for person in people_data:
                lead_dict = self._convert_apollo_person_to_lead(person)
                lead_dict["source"] = "apollo"
                lead_dict["organization_id"] = self.organization_id
                leads_data.append(lead_dict)

            logger.info(f"Successfully sourced {len(leads_data)} leads from Apollo.io")

            # Now check for real emails AFTER enrichment
            real_emails_count = sum(1 for lead in leads_data
                                  if lead.get("contact_email") and "email_not_unlocked" not in lead.get("contact_email", "")
                                  and "@domain.com" not in lead.get("contact_email", ""))

            logger.info(f"📧 Email analysis: {real_emails_count} real emails out of {len(leads_data)} leads")

            return leads_data

        except Exception as e:
            logger.error(f"Apollo sourcing failed: {e}")
            return []

    def _convert_apollo_person_to_lead(self, person: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Apollo person data to lead format"""
        organization = person.get("organization", {})

        # Extract domain for cooldown tracking
        email = person.get("email", "")
        domain = ""
        if email and "@" in email:
            domain = email.split("@")[1]

        return {
            "email": email,  # Database expects 'email' field
            "contact_email": email,  # Keep both for compatibility
            "first_name": person.get("first_name", ""),
            "last_name": person.get("last_name", ""),
            "company": organization.get("name", ""),
            "title": person.get("title", ""),
            "phone": person.get("phone", ""),
            "linkedin_url": person.get("linkedin_url", ""),
            "company_id": organization.get("id", ""),
            "industry": organization.get("industry", ""),
            "location": f"{organization.get('city', '')}, {organization.get('country', '')}".strip(", "),
            "domain": domain,
            "contact_quality_score": self._calculate_lead_quality_score(person),
            "status": "unused",
            "used_by_campaigns": [],
            "last_contacted_at": None
        }

    def _calculate_lead_quality_score(self, person: Dict[str, Any]) -> int:
        """Calculate quality score for a lead based on available data"""
        score = 0

        # Email verification (40 points)
        if person.get("email") and person.get("email_status") == "verified":
            score += 40
        elif person.get("email"):
            score += 20

        # Phone number (20 points)
        if person.get("phone"):
            score += 20

        # LinkedIn profile (15 points)
        if person.get("linkedin_url"):
            score += 15

        # Job title relevance (15 points)
        title = person.get("title", "").lower()
        if any(keyword in title for keyword in ["ceo", "owner", "founder", "president"]):
            score += 15
        elif any(keyword in title for keyword in ["director", "manager", "vp", "head"]):
            score += 10
        elif any(keyword in title for keyword in ["coordinator", "specialist", "assistant"]):
            score += 5

        # Company information (10 points)
        organization = person.get("organization", {})
        if organization.get("name") and organization.get("industry"):
            score += 10
        elif organization.get("name"):
            score += 5

        return min(score, 100)

    async def _process_and_filter_leads(
        self, 
        raw_leads: List[Dict[str, Any]], 
        sourcing_input: LeadSourcingInput,
        leads_repo: BaseRepository
    ) -> Dict[str, Any]:
        """Process and filter leads for duplicates and cooldowns"""
        processed_leads = []
        duplicates = 0
        cooldown_filtered = 0
        
        # Check if we have any real emails AFTER enrichment (leads now have contact_email field)
        real_emails_exist = any(
            lead.get("contact_email") and "email_not_unlocked" not in lead.get("contact_email", "")
            and "@domain.com" not in lead.get("contact_email", "")
            for lead in raw_leads
        )

        # If we're in testing mode, allow placeholder emails
        testing_mode = os.environ.get("APOLLO_TESTING_MODE", "false").lower() == "true"

        logger.info(f"📧 Real email analysis: {sum(1 for lead in raw_leads if lead.get('contact_email') and 'email_not_unlocked' not in lead.get('contact_email', ''))} real emails found")

        if not real_emails_exist:
            if testing_mode:
                logger.warning("No real emails found, but APOLLO_TESTING_MODE is enabled. Using placeholder emails for testing.")
            else:
                logger.warning("No real emails found. This may be due to Apollo.io API limitations or missing credits.")
                logger.warning("Set APOLLO_TESTING_MODE=true to store leads with placeholder emails for testing.")

        for lead_data in raw_leads:
            # After conversion, leads use "contact_email" field
            email = lead_data.get("contact_email", "")

            # Skip invalid emails unless in testing mode
            if not email or not "@" in email:
                continue

            # Skip locked emails unless in testing mode
            if ("email_not_unlocked" in email or "@domain.com" in email) and not testing_mode:
                logger.debug(f"Skipping lead with locked email: {email}")
                continue

            # Check for duplicates
            existing_lead = await self._check_duplicate(email, leads_repo)
            if existing_lead:
                duplicates += 1
                continue

            # Check domain cooldown
            if await self._check_domain_cooldown(email, leads_repo):
                cooldown_filtered += 1
                continue
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(lead_data)
            if quality_score < sourcing_input.quality_threshold:
                continue
            
            # Add quality score and status
            lead_data["contact_quality_score"] = quality_score
            lead_data["status"] = LeadStatus.UNUSED.value
            lead_data["used_by_campaigns"] = []
            
            # Extract domain for future cooldown checks
            domain = email.split("@")[1] if "@" in email else ""
            lead_data["domain"] = domain
            
            # Create lead in database
            try:
                created_lead = await leads_repo.create(lead_data, use_admin=True)
                processed_leads.append(created_lead)
            except Exception as e:
                logger.warning(f"Failed to create lead {lead_data['contact_email']}: {e}")
        
        return {
            "leads": processed_leads,
            "imported": len(processed_leads),
            "duplicates": duplicates,
            "cooldown_filtered": cooldown_filtered
        }

    async def _check_duplicate(self, email: str, leads_repo: BaseRepository) -> bool:
        """Check if lead already exists"""
        try:
            existing = await leads_repo.list(
                filters={"contact_email": email, "organization_id": self.organization_id},
                limit=1,
                use_admin=True
            )
            return len(existing) > 0
        except:
            return False

    async def _check_domain_cooldown(self, email: str, leads_repo: BaseRepository) -> bool:
        """Check if domain is in cooldown period (90 days)"""
        try:
            domain = email.split("@")[1] if "@" in email else ""
            if not domain:
                return False
            
            cooldown_date = datetime.now(datetime.UTC) - timedelta(days=90)
            
            # Check if any lead from this domain was contacted recently
            recent_contacts = await leads_repo.list(
                filters={
                    "domain": domain,
                    "organization_id": self.organization_id
                },
                limit=1,
                use_admin=True
            )
            
            for contact in recent_contacts:
                if contact.get("last_contacted_at"):
                    last_contact = datetime.fromisoformat(contact["last_contacted_at"].replace("Z", "+00:00"))
                    if last_contact > cooldown_date:
                        return True
            
            return False
        except:
            return False

    def _calculate_quality_score(self, lead_data: Dict[str, Any]) -> int:
        """Calculate lead quality score based on available data"""
        score = 50  # Base score
        
        # Email quality
        email = lead_data.get("contact_email", "")
        if email and "@" in email:
            score += 10
            # Bonus for business domains (not gmail, yahoo, etc.)
            domain = email.split("@")[1].lower()
            if domain not in ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com"]:
                score += 15
        
        # Contact information completeness
        if lead_data.get("first_name"):
            score += 5
        if lead_data.get("last_name"):
            score += 5
        if lead_data.get("title"):
            score += 10
        if lead_data.get("phone"):
            score += 10
        if lead_data.get("linkedin_url"):
            score += 10
        
        # Company information
        if lead_data.get("company"):
            score += 10
        if lead_data.get("industry"):
            score += 5
        if lead_data.get("location"):
            score += 5
        
        return min(score, 100)  # Cap at 100
