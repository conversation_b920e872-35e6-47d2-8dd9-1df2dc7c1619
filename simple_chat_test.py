#!/usr/bin/env python3
"""
Simple Chat Interface Test for Selda AI
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent

async def simple_chat_test():
    """Simple chat interface test"""
    print("\n" + "="*60)
    print("🤖 SELDA AI - Simple Chat Test")
    print("="*60)
    print("Welcome! Let's test the goal workflow interactively.")
    print("Type 'quit' to exit.")
    print("-"*60)
    
    try:
        # Initialize
        print("\n🔄 Initializing...")
        organization_id = "00000000-0000-4000-8000-000000000001"
        
        while True:
            # Get user goal
            print("\n💬 What's your sales goal?")
            print("👤 ", end="", flush=True)
            goal = input().strip()
            
            if goal.lower() in ['quit', 'exit']:
                print("🤖 Goodbye! 👋")
                break
                
            if not goal:
                print("🤖 Please enter a valid goal.")
                continue
            
            try:
                # Test Goal Understanding
                print("\n🤖 Let me understand your goal...")
                
                goal_agent = GoalUnderstandingAgent(organization_id)
                
                goal_input = {
                    "goal_description": goal,
                    "answers": {}
                }
                
                result = await goal_agent.execute(goal_input)
                
                print(f"🤖 Goal Analysis:")
                print(f"   Complete: {result.get('complete', False)}")
                print(f"   Industry: {result.get('industry', 'N/A')}")
                print(f"   Location: {result.get('location', 'N/A')}")
                print(f"   Target: {result.get('target_meetings', 'N/A')}")
                print(f"   Confidence: {result.get('confidence', 0):.1f}%")
                
                # Handle follow-up questions
                follow_ups = result.get("follow_up_questions", [])
                if follow_ups:
                    print(f"\n🤖 I need more information:")
                    answers = {}
                    
                    for i, question in enumerate(follow_ups, 1):
                        print(f"\n💬 {question}")
                        print("👤 ", end="", flush=True)
                        answer = input().strip()
                        
                        # Simple mapping
                        if "industry" in question.lower():
                            answers["industry"] = answer
                        elif "location" in question.lower():
                            answers["location"] = answer
                        elif "meeting" in question.lower():
                            answers["target_meetings"] = answer
                    
                    # Process follow-up
                    follow_up_input = {
                        "goal_description": goal,
                        "answers": answers
                    }
                    
                    result = await goal_agent.execute(follow_up_input)
                    
                    print(f"\n🤖 Updated Goal Analysis:")
                    print(f"   Complete: {result.get('complete', False)}")
                    print(f"   Industry: {result.get('industry', 'N/A')}")
                    print(f"   Location: {result.get('location', 'N/A')}")
                    print(f"   Target: {result.get('target_meetings', 'N/A')}")
                    print(f"   Confidence: {result.get('confidence', 0):.1f}%")
                
                # If goal is complete, proceed to lead sourcing
                if result.get("complete", False):
                    print(f"\n🤖 Great! Now I'll source leads for you...")
                    
                    lead_agent = LeadSourcingAgent(organization_id)
                    
                    target_meetings = result.get("target_meetings", 5)
                    lead_count = target_meetings * 1  # 1 lead per meeting for testing
                    
                    sourcing_input = {
                        "industry": result.get("industry", ""),
                        "location": result.get("location", ""),
                        "lead_count_target": lead_count,
                        "quality_threshold": 50,
                        "sources": ["apollo"]
                    }
                    
                    print(f"🔍 Sourcing {lead_count} leads...")
                    
                    sourcing_result = await lead_agent.execute(sourcing_input)
                    
                    print(f"\n🤖 Lead Sourcing Results:")
                    print(f"   ✅ Leads sourced: {sourcing_result.get('leads_sourced', 0)}")
                    print(f"   🔄 Duplicates: {sourcing_result.get('duplicates_filtered', 0)}")
                    print(f"   ⏰ Cooldown: {sourcing_result.get('cooldown_filtered', 0)}")
                    
                    quality_dist = sourcing_result.get("quality_distribution", {})
                    if quality_dist:
                        print(f"   📊 Quality: High={quality_dist.get('high', 0)}, "
                              f"Medium={quality_dist.get('medium', 0)}, "
                              f"Low={quality_dist.get('low', 0)}")
                
                print(f"\n" + "-"*60)
                print("🤖 Ready for another goal!")
                
            except Exception as e:
                print(f"🤖 ❌ Error: {e}")
                print("Let's try again!")
                
    except Exception as e:
        print(f"❌ Fatal error: {e}")
    finally:
        pass  # No cleanup needed

if __name__ == "__main__":
    print("🚀 Starting Simple Chat Test...")
    asyncio.run(simple_chat_test())
