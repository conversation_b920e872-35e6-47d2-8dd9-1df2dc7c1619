"""
Supabase authentication utilities for RLS integration
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from jose import jwt
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


def create_supabase_compatible_jwt(user_data: Dict[str, Any]) -> str:
    """
    Create a JWT token that Supabase can understand for RLS
    
    This creates a token with the proper structure that Supabase expects
    for auth.uid() to work in RLS policies.
    """
    try:
        # Get the JWT secret from the anon key
        # We'll decode the anon key to extract the secret
        anon_key = settings.supabase_anon_key
        
        # Decode without verification to get the structure
        decoded_anon = jwt.get_unverified_header(anon_key)
        
        # The secret should be the same as our JWT secret for now
        # In production, you'd want to use Supabase's actual JWT secret
        jwt_secret = settings.jwt_secret_key
        
        # Create the payload in Supabase format
        import time
        now = time.time()
        exp = now + 3600  # 1 hour expiry (3600 seconds)

        payload = {
            "iss": "supabase",
            "ref": "aqjctgvrvtogryxlvwor",  # Your project ref
            "role": "authenticated",
            "aud": "authenticated",
            "sub": user_data.get("id"),
            "email": user_data.get("email"),
            "iat": int(now),
            "exp": int(exp),
            # Add custom claims
            "user_metadata": {
                "email": user_data.get("email"),
                "first_name": user_data.get("first_name"),
                "last_name": user_data.get("last_name")
            },
            "app_metadata": {
                "provider": "email",
                "providers": ["email"]
            }
        }
        
        # Create the token
        token = jwt.encode(
            payload,
            jwt_secret,
            algorithm="HS256"
        )
        
        logger.debug(f"Created Supabase-compatible JWT for user: {user_data.get('id')}")
        return token
        
    except Exception as e:
        logger.error(f"Failed to create Supabase-compatible JWT: {e}")
        raise


def verify_supabase_jwt(token: str) -> Dict[str, Any]:
    """
    Verify a Supabase-compatible JWT token
    """
    try:
        # Use audience parameter in jwt.decode for proper validation
        payload = jwt.decode(
            token,
            settings.jwt_secret_key,
            algorithms=["HS256"],
            audience="authenticated"
        )

        # Validate required Supabase fields (audience is already validated by jwt.decode)
        if payload.get("role") != "authenticated":
            raise ValueError("Invalid token role")

        if payload.get("iss") != "supabase":
            raise ValueError("Invalid token issuer")

        if not payload.get("sub"):
            raise ValueError("Token missing user ID (sub)")

        if not payload.get("email"):
            raise ValueError("Token missing email")
        
        return payload
        
    except Exception as e:
        logger.error(f"Failed to verify Supabase JWT: {e}")
        raise


def extract_user_from_supabase_jwt(token: str) -> Dict[str, Any]:
    """
    Extract user information from a Supabase JWT token
    """
    try:
        payload = verify_supabase_jwt(token)
        
        user_data = {
            "id": payload.get("sub"),
            "email": payload.get("email"),
            "first_name": payload.get("user_metadata", {}).get("first_name"),
            "last_name": payload.get("user_metadata", {}).get("last_name"),
            "role": payload.get("role", "authenticated"),
            "aud": payload.get("aud"),
            "iss": payload.get("iss"),
            "exp": payload.get("exp"),
            "iat": payload.get("iat")
        }
        
        return user_data
        
    except Exception as e:
        logger.error(f"Failed to extract user from Supabase JWT: {e}")
        raise


class SupabaseAuthManager:
    """
    Manager class for Supabase authentication operations
    """
    
    def __init__(self):
        self.jwt_secret = settings.jwt_secret_key
    
    def create_auth_session(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a complete auth session with Supabase-compatible tokens
        """
        try:
            # Create access token (Supabase-compatible)
            access_token = create_supabase_compatible_jwt(user_data)

            # Create refresh token (custom format for our app)
            import time
            now = time.time()
            refresh_payload = {
                "sub": user_data.get("id"),
                "email": user_data.get("email"),
                "type": "refresh",
                "exp": int(now + (7 * 24 * 3600)),  # 7 days
                "iat": int(now)
            }
            
            refresh_token = jwt.encode(
                refresh_payload,
                self.jwt_secret,
                algorithm="HS256"
            )
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": 3600,  # 1 hour
                "expires_at": int(now + 3600),
                "user": {
                    "id": user_data.get("id"),
                    "email": user_data.get("email"),
                    "first_name": user_data.get("first_name"),
                    "last_name": user_data.get("last_name"),
                    "role": user_data.get("role", "user"),
                    "organization_id": user_data.get("organization_id")
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to create auth session: {e}")
            raise
    
    def refresh_access_token(self, refresh_token: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Refresh the access token using a refresh token
        """
        try:
            # Verify refresh token
            payload = jwt.decode(
                refresh_token,
                self.jwt_secret,
                algorithms=["HS256"]
            )

            if payload.get("type") != "refresh":
                raise ValueError("Invalid token type")

            if payload.get("sub") != user_data.get("id"):
                raise ValueError("Token user mismatch")

            # Create new access token
            access_token = create_supabase_compatible_jwt(user_data)

            import time
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": int(time.time() + 3600)
            }
            
        except Exception as e:
            logger.error(f"Failed to refresh access token: {e}")
            raise


# Global instance
supabase_auth_manager = SupabaseAuthManager()
