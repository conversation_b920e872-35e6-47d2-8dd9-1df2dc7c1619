# Selda AI Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
APP_NAME=Selda AI Sales Autopilot
APP_VERSION=1.0.0
DEBUG=False
ENVIRONMENT=development

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Security
JWT_SECRET_KEY=your-super-secret-jwt-key-here-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Database - Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-role-key

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Lead Generation - Apollo.io
APOLLO_API_KEY=your-apollo-api-key
APOLLO_BASE_URL=https://api.apollo.io/v1

# Payment Processing - Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
STRIPE_BASIC_PRICE_ID=price_your-basic-plan-price-id
STRIPE_PRO_PRICE_ID=price_your-pro-plan-price-id
STRIPE_ENTERPRISE_PRICE_ID=price_your-enterprise-plan-price-id

# Email Service Provider - SendGrid
SENDGRID_API_KEY=SG.your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Selda AI

# Alternative ESP - Mailgun (if using instead of SendGrid)
# MAILGUN_API_KEY=your-mailgun-api-key
# MAILGUN_DOMAIN=your-mailgun-domain
# MAILGUN_BASE_URL=https://api.mailgun.net/v3

# Google Services - Calendar Integration
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# LinkedIn Integration
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
LINKEDIN_REDIRECT_URI=http://localhost:8000/auth/linkedin/callback

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
CORS_ALLOW_CREDENTIALS=True

# File Upload Settings
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=["pdf", "doc", "docx", "txt", "csv"]

# Email Settings
EMAIL_VERIFICATION_EXPIRE_HOURS=24
PASSWORD_RESET_EXPIRE_HOURS=1

# Webhook Settings
WEBHOOK_SECRET=your-webhook-secret-for-verification

# Monitoring & Analytics
SENTRY_DSN=your-sentry-dsn-for-error-tracking
ANALYTICS_ENABLED=True

# Development Settings (only for development)
RELOAD=True
WORKERS=1

# Production Settings (only for production)
# RELOAD=False
# WORKERS=4
# GUNICORN_BIND=0.0.0.0:8000
# GUNICORN_WORKERS=4
# GUNICORN_WORKER_CLASS=uvicorn.workers.UvicornWorker
