#!/usr/bin/env python3
"""
Direct test of Apollo.io people search + bulk enrichment workflow
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.leads.apollo_service import ApolloService

async def test_apollo_direct_workflow():
    """Test the complete Apollo.io workflow: search + enrichment"""
    print("🚀 Testing Apollo.io Direct Workflow")
    print("=" * 50)
    
    try:
        apollo_service = ApolloService()
        
        # Step 1: People search
        print("\n1️⃣ Searching for restaurant contacts in London...")
        search_filters = {
            "page": 1,
            "per_page": 15,  # Small batch for testing
            "q_organization_locations": "London",
            "organization_industries": "restaurants",
            "person_titles": ["CEO", "Owner", "Manager", "Director"],
            "email_status": ["verified"]
        }
        
        people_data = await apollo_service.search_people_with_pagination(
            filters=search_filters,
            target_count=15
        )
        
        print(f"   Found {len(people_data)} people")
        
        if not people_data:
            print("❌ No people found")
            return False
        
        # Show sample before enrichment
        sample = people_data[0]
        print(f"   Sample before enrichment:")
        print(f"     Name: {sample.get('first_name', '')} {sample.get('last_name', '')}")
        print(f"     Email: {sample.get('email', 'N/A')}")
        print(f"     Company: {sample.get('organization', {}).get('name', 'N/A')}")
        
        # Step 2: Extract person IDs
        person_ids = [person.get("id") for person in people_data if person.get("id")]
        print(f"\n2️⃣ Extracting {len(person_ids)} person IDs for enrichment...")
        
        # Step 3: Bulk enrichment
        print(f"\n3️⃣ Performing bulk enrichment (batches of 10)...")
        enriched_people = await apollo_service.bulk_enrich_people(person_ids)
        
        print(f"   Enriched {len(enriched_people)} people")
        
        # Step 4: Analyze results
        real_emails = []
        locked_emails = []
        
        for person in enriched_people:
            email = person.get("email", "")
            if email and "email_not_unlocked" not in email and "@domain.com" not in email:
                real_emails.append(email)
            else:
                locked_emails.append(email)
        
        print(f"\n4️⃣ Enrichment Results:")
        print(f"   Total enriched: {len(enriched_people)}")
        print(f"   Real emails: {len(real_emails)}")
        print(f"   Locked emails: {len(locked_emails)}")
        
        if real_emails:
            print(f"   ✅ Sample real emails:")
            for i, email in enumerate(real_emails[:3]):
                print(f"     {i+1}. {email}")
        
        # Show sample after enrichment
        if enriched_people:
            enriched_sample = enriched_people[0]
            print(f"\n   Sample after enrichment:")
            print(f"     Name: {enriched_sample.get('first_name', '')} {enriched_sample.get('last_name', '')}")
            print(f"     Email: {enriched_sample.get('email', 'N/A')}")
            print(f"     Phone: {enriched_sample.get('phone', 'N/A')}")
            print(f"     Company: {enriched_sample.get('organization', {}).get('name', 'N/A')}")
            print(f"     LinkedIn: {enriched_sample.get('linkedin_url', 'N/A')}")
        
        # Step 5: Success metrics
        success_rate = len(real_emails) / len(enriched_people) if enriched_people else 0
        print(f"\n📊 Success Metrics:")
        print(f"   Email unlock rate: {success_rate:.1%}")
        print(f"   Usable contacts: {len(real_emails)}")
        
        success = len(real_emails) > 0
        print(f"\n🎯 Overall Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
        
        if success:
            print(f"   🎉 Apollo.io workflow is working! Found {len(real_emails)} contacts with real emails.")
        else:
            print(f"   ⚠️ No real emails unlocked. May need Apollo.io credits or subscription.")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_apollo_direct_workflow()
    
    if success:
        print("\n🚀 Apollo.io integration is ready for production!")
        print("   You can now use the goal workflow to source real leads.")
    else:
        print("\n🔧 Apollo.io integration needs attention.")
        print("   Check API credits and subscription status.")

if __name__ == "__main__":
    asyncio.run(main())
