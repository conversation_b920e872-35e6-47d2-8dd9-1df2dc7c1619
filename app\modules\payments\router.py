"""
Payment router
"""

from fastapi import APIRouter, Depends, HTTPEx<PERSON>, status, Request, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import logging
import stripe
from typing import Optional, List

from app.modules.payments.models import (
    SubscriptionPlan,
    CreateCheckoutSessionRequest,
    CheckoutSessionResponse,
    SubscriptionResponse,
    BillingPortalSessionRequest,
    BillingPortalSessionResponse,
    SubscriptionPlansResponse,
    CancelSubscriptionRequest,
    UpdateSubscriptionRequest,
    WebhookEvent
)
from app.modules.payments.service import payment_service
from app.shared.models import BaseResponse
from app.shared.dependencies import (
    get_current_active_user,
    get_current_organization,
    require_role
)
from app.core.exceptions import (
    NotFoundError,
    ConflictError,
    ValidationError,
    ExternalServiceError
)
from app.core.config import settings

logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Security
security = HTTPBearer()

# Router
router = APIRouter()


@router.get("/plans", response_model=SubscriptionPlansResponse)
async def get_subscription_plans():
    """Get all available subscription plans"""
    try:
        plans = await payment_service.get_subscription_plans()
        return SubscriptionPlansResponse(
            success=True,
            message="Subscription plans retrieved successfully",
            plans=plans
        )
    except Exception as e:
        logger.error(f"Error getting subscription plans: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subscription plans"
        )


@router.post("/checkout", response_model=CheckoutSessionResponse)
@limiter.limit("5/minute")
async def create_checkout_session(
    request: Request,
    checkout_request: CreateCheckoutSessionRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    _: dict = Depends(require_role("owner"))  # Only owners can manage billing
):
    """Create Stripe checkout session for subscription"""
    try:
        return await payment_service.create_checkout_session(
            organization_id=organization['id'],
            request=checkout_request
        )
    except ExternalServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating checkout session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create checkout session"
        )


@router.get("/subscription", response_model=SubscriptionResponse)
async def get_current_subscription(
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get organization's current subscription"""
    try:
        subscription = await payment_service.get_organization_subscription(
            organization_id=organization['id']
        )
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found"
            )
        
        return subscription
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting subscription: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subscription"
        )


@router.post("/billing-portal", response_model=BillingPortalSessionResponse)
@limiter.limit("10/hour")
async def create_billing_portal_session(
    request: Request,
    portal_request: BillingPortalSessionRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    _: dict = Depends(require_role("owner"))  # Only owners can access billing portal
):
    """Create Stripe billing portal session"""
    try:
        return await payment_service.create_billing_portal_session(
            organization_id=organization['id'],
            request=portal_request
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ExternalServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating billing portal session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create billing portal session"
        )


@router.post("/webhooks/stripe")
async def handle_stripe_webhook(
    request: Request,
    stripe_signature: str = Header(None, alias="stripe-signature")
):
    """Handle Stripe webhook events"""
    try:
        # Get the raw body
        body = await request.body()
        
        # Verify webhook signature
        try:
            event = stripe.Webhook.construct_event(
                body, stripe_signature, settings.stripe_webhook_secret
            )
        except ValueError as e:
            logger.error(f"Invalid payload in webhook: {e}")
            raise HTTPException(status_code=400, detail="Invalid payload")
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature in webhook: {e}")
            raise HTTPException(status_code=400, detail="Invalid signature")
        
        # Handle the event
        await _handle_webhook_event(event)
        
        return {"status": "success"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling webhook: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Webhook processing failed"
        )


async def _handle_webhook_event(event: dict):
    """Handle specific webhook events"""
    try:
        event_type = event['type']
        data = event['data']['object']
        
        logger.info(f"Processing webhook event: {event_type}")
        
        if event_type == 'checkout.session.completed':
            await _handle_checkout_completed(data)
        elif event_type == 'invoice.payment_succeeded':
            await _handle_payment_succeeded(data)
        elif event_type == 'customer.subscription.updated':
            await _handle_subscription_updated(data)
        elif event_type == 'customer.subscription.deleted':
            await _handle_subscription_deleted(data)
        elif event_type == 'invoice.payment_failed':
            await _handle_payment_failed(data)
        else:
            logger.info(f"Unhandled webhook event type: {event_type}")
            
    except Exception as e:
        logger.error(f"Error handling webhook event {event.get('type', 'unknown')}: {e}")
        raise


async def _handle_checkout_completed(session_data: dict):
    """Handle successful checkout completion"""
    try:
        # Delegate to payment_service
        await payment_service.handle_checkout_completed(session_data)
    except Exception as e:
        logger.error(f"Error handling checkout completion: {e}")
        raise


async def _handle_payment_succeeded(invoice_data: dict):
    """Handle successful payment"""
    try:
        await payment_service.handle_payment_succeeded(invoice_data)
    except Exception as e:
        logger.error(f"Error handling payment success: {e}")
        raise


async def _handle_subscription_updated(subscription_data: dict):
    """Handle subscription update"""
    try:
        await payment_service.handle_subscription_updated(subscription_data)
    except Exception as e:
        logger.error(f"Error handling subscription update: {e}")
        raise


async def _handle_subscription_deleted(subscription_data: dict):
    """Handle subscription cancellation"""
    try:
        await payment_service.handle_subscription_deleted(subscription_data)
    except Exception as e:
        logger.error(f"Error handling subscription deletion: {e}")
        raise


async def _handle_payment_failed(invoice_data: dict):
    """Handle failed payment"""
    try:
        await payment_service.handle_payment_failed(invoice_data)
    except Exception as e:
        logger.error(f"Error handling payment failure: {e}")
        raise


# Note: Rate limit exception handler is added to the main app, not the router
