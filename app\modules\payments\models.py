"""
Payment models and schemas
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.shared.models import BaseResponse, SubscriptionTier, TimestampMixin


class PaymentStatus(str, Enum):
    """Payment status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELED = "canceled"
    REFUNDED = "refunded"


class SubscriptionStatus(str, Enum):
    """Subscription status enumeration"""
    ACTIVE = "active"
    PAST_DUE = "past_due"
    UNPAID = "unpaid"
    CANCELED = "canceled"
    INCOMPLETE = "incomplete"
    INCOMPLETE_EXPIRED = "incomplete_expired"
    TRIALING = "trialing"
    PAUSED = "paused"


class InvoiceStatus(str, Enum):
    """Invoice status enumeration"""
    DRAFT = "draft"
    OPEN = "open"
    PAID = "paid"
    UNCOLLECTIBLE = "uncollectible"
    VOID = "void"


class SubscriptionPlan(BaseModel):
    """Subscription plan model"""
    id: str
    name: str
    description: str
    tier: SubscriptionTier
    price: float = Field(..., description="Price in dollars")
    currency: str = Field(default="usd")
    interval: str = Field(..., description="billing interval: month, year")
    interval_count: int = Field(default=1, description="number of intervals between billings")
    features: List[str] = Field(default_factory=list)
    limits: Dict[str, Any] = Field(default_factory=dict)
    stripe_price_id: Optional[str] = None
    is_active: bool = True
    trial_period_days: Optional[int] = None

    class Config:
        from_attributes = True


class CreateCheckoutSessionRequest(BaseModel):
    """Create checkout session request"""
    price_id: str = Field(..., description="Stripe price ID")
    success_url: str = Field(..., description="URL to redirect after successful payment")
    cancel_url: str = Field(..., description="URL to redirect after canceled payment")
    trial_period_days: Optional[int] = Field(None, description="Trial period in days")
    coupon_code: Optional[str] = Field(None, description="Coupon code to apply")
    metadata: Optional[Dict[str, str]] = Field(default_factory=dict)


class CheckoutSessionResponse(BaseResponse):
    """Checkout session response"""
    session_id: str
    session_url: str
    expires_at: datetime


class SubscriptionResponse(BaseModel):
    """Subscription response model"""
    id: str
    organization_id: str
    stripe_subscription_id: str
    stripe_customer_id: str
    plan: SubscriptionPlan
    status: SubscriptionStatus
    current_period_start: datetime
    current_period_end: datetime
    trial_start: Optional[datetime] = None
    trial_end: Optional[datetime] = None
    canceled_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class PaymentMethodResponse(BaseModel):
    """Payment method response"""
    id: str
    type: str
    card: Optional[Dict[str, Any]] = None
    is_default: bool = False
    created_at: datetime


class InvoiceLineItem(BaseModel):
    """Invoice line item"""
    id: str
    description: str
    amount: float
    currency: str
    quantity: int = 1
    unit_amount: float
    period_start: datetime
    period_end: datetime


class InvoiceResponse(BaseModel):
    """Invoice response model"""
    id: str
    organization_id: str
    stripe_invoice_id: str
    number: str
    status: InvoiceStatus
    amount_due: float
    amount_paid: float
    amount_remaining: float
    currency: str
    description: Optional[str] = None
    invoice_pdf: Optional[str] = None
    hosted_invoice_url: Optional[str] = None
    payment_intent_id: Optional[str] = None
    subscription_id: Optional[str] = None
    line_items: List[InvoiceLineItem] = Field(default_factory=list)
    due_date: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True


class PaymentResponse(BaseModel):
    """Payment response model"""
    id: str
    organization_id: str
    stripe_payment_intent_id: str
    amount: float
    currency: str
    status: PaymentStatus
    description: Optional[str] = None
    invoice_id: Optional[str] = None
    subscription_id: Optional[str] = None
    payment_method_id: Optional[str] = None
    failure_reason: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class UsageRecord(BaseModel):
    """Usage record model"""
    id: str
    organization_id: str
    subscription_id: str
    metric_name: str
    quantity: int
    timestamp: datetime
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BillingPortalSessionRequest(BaseModel):
    """Billing portal session request"""
    return_url: str = Field(..., description="URL to return to after managing billing")


class BillingPortalSessionResponse(BaseResponse):
    """Billing portal session response"""
    session_url: str


class CancelSubscriptionRequest(BaseModel):
    """Cancel subscription request"""
    reason: Optional[str] = Field(None, max_length=500)
    cancel_at_period_end: bool = Field(default=True, description="Cancel at end of current period")
    prorate: bool = Field(default=False, description="Prorate the cancellation")


class UpdateSubscriptionRequest(BaseModel):
    """Update subscription request"""
    price_id: Optional[str] = Field(None, description="New price ID")
    quantity: Optional[int] = Field(None, ge=1, description="Subscription quantity")
    prorate: bool = Field(default=True, description="Prorate the change")
    trial_end: Optional[datetime] = Field(None, description="Trial end date")


class WebhookEvent(BaseModel):
    """Stripe webhook event model"""
    id: str
    type: str
    data: Dict[str, Any]
    created: datetime
    livemode: bool
    pending_webhooks: int
    request_id: Optional[str] = None


class SubscriptionUsageResponse(BaseModel):
    """Subscription usage response"""
    organization_id: str
    subscription_id: str
    current_period_start: datetime
    current_period_end: datetime
    usage_records: List[UsageRecord]
    limits: Dict[str, Any]
    usage_summary: Dict[str, int]


class BillingHistoryResponse(BaseModel):
    """Billing history response"""
    invoices: List[InvoiceResponse]
    payments: List[PaymentResponse]
    total_count: int
    has_more: bool


class SubscriptionPlansResponse(BaseResponse):
    """Subscription plans response"""
    plans: List[SubscriptionPlan]


class PaymentMethodsResponse(BaseResponse):
    """Payment methods response"""
    payment_methods: List[PaymentMethodResponse]
    default_payment_method: Optional[PaymentMethodResponse] = None


class SubscriptionMetrics(BaseModel):
    """Subscription metrics"""
    monthly_recurring_revenue: float
    annual_recurring_revenue: float
    active_subscriptions: int
    churned_subscriptions: int
    trial_subscriptions: int
    conversion_rate: float
    average_revenue_per_user: float


class CouponResponse(BaseModel):
    """Coupon response model"""
    id: str
    name: str
    percent_off: Optional[float] = None
    amount_off: Optional[float] = None
    currency: Optional[str] = None
    duration: str
    duration_in_months: Optional[int] = None
    max_redemptions: Optional[int] = None
    times_redeemed: int
    valid: bool
    created_at: datetime
    expires_at: Optional[datetime] = None


class ApplyCouponRequest(BaseModel):
    """Apply coupon request"""
    coupon_code: str = Field(..., min_length=1, max_length=50)


class CreateCustomerRequest(BaseModel):
    """Create customer request"""
    email: str
    name: str
    phone: Optional[str] = None
    address: Optional[Dict[str, str]] = None
    metadata: Optional[Dict[str, str]] = Field(default_factory=dict)


class CustomerResponse(BaseModel):
    """Customer response model"""
    id: str
    stripe_customer_id: str
    email: str
    name: str
    phone: Optional[str] = None
    address: Optional[Dict[str, str]] = None
    default_payment_method: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
