from uuid import UUID
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field
from enum import Enum

class LeadStatus(str, Enum):
    """Enum for lead statuses."""
    UNUSED = "unused"
    RESERVED = "reserved"
    CONTACTED = "contacted"
    BLACKLISTED = "blacklisted"
    COOLDOWN = "cooldown"
    
    # Existing statuses, can be deprecated over time
    NEW = "new"
    RESPONDED = "responded"
    QUALIFIED = "qualified"
    CONVERTED = "converted"
    UNQUALIFIED = "unqualified"


class LeadBase(BaseModel):
    """Base model for a lead."""
    contact_email: EmailStr = Field(..., description="The contact email of the lead.")
    first_name: Optional[str] = Field(None, description="The first name of the lead contact.")
    last_name: Optional[str] = Field(None, description="The last name of the lead contact.")
    company: Optional[str] = Field(None, description="The company name of the lead.")
    title: Optional[str] = Field(None, description="Job title of the lead contact.")
    phone: Optional[str] = Field(None, description="Phone number of the lead contact.")
    linkedin_url: Optional[str] = Field(None, description="LinkedIn profile URL of the lead.")
    source: Optional[str] = Field(None, description="The source from where the lead was obtained.")
    notes: Optional[str] = Field(None, description="Additional notes about the lead.")
    custom_fields: Optional[dict] = Field(default_factory=dict, description="Custom fields for the lead.")
    
    # New fields from requirements
    company_id: Optional[str] = Field(None, description="Unique identifier for the company from external sources.")
    industry: Optional[str] = Field(None, description="Industry of the company.")
    location: Optional[str] = Field(None, description="Geographic location of the company.")
    used_by_campaigns: List[UUID] = Field(default_factory=list, description="Array of campaign IDs that have used this lead.")
    last_contacted_at: Optional[datetime] = Field(None, description="Timestamp of the last time this lead was contacted.")
    contact_quality_score: Optional[int] = Field(None, description="A score indicating lead quality.", ge=0, le=100)


class LeadCreate(LeadBase):
    """Model for creating a new lead."""
    organization_id: UUID


class LeadUpdate(LeadBase):
    """Model for updating an existing lead."""
    contact_email: Optional[EmailStr] = None # Allow partial updates
    status: Optional[LeadStatus] = None


class Lead(LeadBase):
    """Model representing a lead in the database."""
    id: UUID
    organization_id: UUID
    status: LeadStatus = Field(..., description="The current status of the lead.")
    assigned_to: Optional[UUID] = Field(None, description="The user ID to whom the lead is assigned.")
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class LeadFilter(BaseModel):
    """Model for filtering leads."""
    location: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    revenue: Optional[str] = None
    website_presence: Optional[bool] = None
    linkedin_presence: Optional[bool] = None

class PaginatedLeads(BaseModel):
    items: List[Lead]
    total: int
    page: int
    size: int 