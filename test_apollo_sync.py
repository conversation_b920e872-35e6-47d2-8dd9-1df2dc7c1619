#!/usr/bin/env python3
"""
Synchronous test of Apollo.io API
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_apollo_sync():
    """Test Apollo.io API synchronously"""
    print("🧪 Testing Apollo.io API (Synchronous)")
    print("=" * 50)
    
    api_key = os.getenv("APOLLO_API_KEY")
    if not api_key:
        print("❌ APOLLO_API_KEY not found in environment")
        return
    
    print(f"🔑 API Key: {api_key[:10]}...")
    
    headers = {
        "accept": "application/json",
        "Cache-Control": "no-cache",
        "Content-Type": "application/json",
        "x-api-key": api_key
    }
    
    # Test 1: People search
    print("\n1️⃣ Testing people search...")
    search_url = "https://api.apollo.io/api/v1/mixed_people/search"
    search_data = {
        "page": 1,
        "per_page": 3,
        "q_organization_locations": "London",
        "organization_industries": "restaurants"
    }
    
    try:
        print(f"📤 POST {search_url}")
        print(f"📤 Data: {search_data}")
        
        response = requests.post(search_url, json=search_data, headers=headers, timeout=30)
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            people = data.get("people", [])
            print(f"✅ Search successful: {len(people)} people found")
            
            if people:
                person_ids = [person.get("id") for person in people if person.get("id")]
                print(f"🆔 Person IDs: {person_ids}")
                
                # Show sample person
                sample = people[0]
                print(f"👤 Sample person:")
                print(f"   Name: {sample.get('first_name')} {sample.get('last_name')}")
                print(f"   Email: {sample.get('email')}")
                print(f"   ID: {sample.get('id')}")
                
                # Test 2: Bulk enrichment
                print(f"\n2️⃣ Testing bulk enrichment...")
                enrich_url = "https://api.apollo.io/api/v1/people/bulk_match?reveal_personal_emails=true&reveal_phone_number=false"
                enrich_data = {
                    "details": [{"id": person_id} for person_id in person_ids[:3]]
                }
                
                print(f"📤 POST {enrich_url}")
                print(f"📤 Data: {enrich_data}")
                
                try:
                    enrich_response = requests.post(enrich_url, json=enrich_data, headers=headers, timeout=30)
                    print(f"📥 Status: {enrich_response.status_code}")
                    
                    if enrich_response.status_code == 200:
                        enrich_data = enrich_response.json()
                        print(f"✅ Enrichment successful!")
                        print(f"📄 Response: {json.dumps(enrich_data, indent=2)}")
                        
                        # Check for matches vs people
                        matches = enrich_data.get("matches", [])
                        people_enriched = enrich_data.get("people", [])
                        
                        print(f"📊 Results:")
                        print(f"   Matches: {len(matches)}")
                        print(f"   People: {len(people_enriched)}")
                        
                        if matches:
                            print(f"📧 Sample emails from matches:")
                            for i, match in enumerate(matches[:3]):
                                email = match.get("email", "N/A")
                                print(f"   Match {i+1}: {email}")
                        
                    else:
                        print(f"❌ Enrichment failed: {enrich_response.status_code}")
                        print(f"❌ Response: {enrich_response.text}")
                        
                except Exception as enrich_error:
                    print(f"❌ Enrichment request failed: {enrich_error}")
            
        else:
            print(f"❌ Search failed: {response.status_code}")
            print(f"❌ Response: {response.text}")
            
    except Exception as search_error:
        print(f"❌ Search request failed: {search_error}")

if __name__ == "__main__":
    test_apollo_sync()
