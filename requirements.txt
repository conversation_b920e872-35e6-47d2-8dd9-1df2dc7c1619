# Core Framework
fastapi==0.115.9
uvicorn[standard]==0.34.3
pydantic[email]==2.11.6
pydantic-settings==2.9.1

# CrewAI Framework
crewai==0.130.0
crewai-tools==0.0.1

# OpenAI Integration
openai==1.86.0
langchain-openai==0.3.23
langchain-core==0.3.65

# Database & ORM
supabase==2.15.3
postgrest==1.0.2
sqlalchemy==2.0.41
alembic==1.16.1

# Authentication & Security
python-jose[cryptography]==3.5.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.20
bcrypt==4.3.0

# HTTP Client
httpx==0.28.1
requests==2.32.4

# Background Tasks
celery==5.5.3
redis==6.2.0

# Payment Processing
stripe==12.2.0

# Email Services
sendgrid==6.12.4
# mailgun (alternative)
# requests-mailgun==0.1.1

# Google Services
google-auth==2.40.3
google-auth-oauthlib==1.2.2
google-auth-httplib2==0.2.0
google-api-python-client==2.172.0

# LinkedIn Integration (unofficial)
linkedin-api==2.3.1

# Apollo.io Integration
# Note: Apollo.io doesn't have an official Python SDK, we'll use requests

# Data Processing
pandas==2.3.0
numpy==2.3.0

# Validation & Serialization
marshmallow==4.0.0
marshmallow-sqlalchemy==1.4.2

# Utilities
python-dotenv==1.1.0
pytz==2025.2
python-dateutil==2.9.0.post0
uuid==1.30

# Logging & Monitoring
structlog==25.4.0
python-json-logger==3.3.0
sentry-sdk[fastapi]==2.30.0

# Rate Limiting
slowapi==0.1.9

# File Processing
python-magic==0.4.27
pillow==11.2.1

# Testing
pytest==8.4.0
pytest-asyncio==1.0.0
pytest-cov==6.2.1
pytest-mock==3.14.1

# Development Tools
black==25.1.0
isort==6.0.1
flake8==7.2.0
mypy==1.16.0
pre-commit==4.2.0

# Documentation
mkdocs==1.6.1
mkdocs-material==9.6.14

# Production Server
gunicorn==23.0.0

# Environment Management
python-decouple==3.8
