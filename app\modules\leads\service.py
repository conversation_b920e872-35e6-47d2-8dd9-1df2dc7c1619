"""
Lead service layer
"""

import logging
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta

from app.core.database import SupabaseService, supabase_service, BaseRepository
from app.core.exceptions import NotFoundError, ConflictError, ValidationError
from app.shared.models import PaginationParams
from app.modules.leads.models import Lead, LeadCreate, LeadUpdate, LeadStatus, LeadFilter, PaginatedLeads
from app.modules.leads.apollo_service import ApolloService

logger = logging.getLogger(__name__)

class LeadService:
    """Service for managing leads"""

    def __init__(self, db: SupabaseService = None):
        self.db = db or supabase_service
        self.leads_repo = BaseRepository("leads", self.db)
        self.email_sends_repo = BaseRepository("email_sends", self.db)

    async def get_lead_by_id(self, lead_id: UUID, organization_id: UUID) -> Lead:
        """Get a single lead by its ID"""
        lead_data = await self.leads_repo.get_by_id(str(lead_id))
        if not lead_data or str(lead_data.get("organization_id")) != str(organization_id):
            raise NotFoundError("Lead not found")
        return Lead(**lead_data)

    async def create_lead(self, lead_data: LeadCreate) -> Lead:
        """Create a new lead"""
        # Check for duplicates within the same organization
        existing_lead = await self.leads_repo.get_by_conditions(
            {"organization_id": str(lead_data.organization_id), "contact_email": lead_data.contact_email}
        )
        if existing_lead:
            raise ConflictError("A lead with this email already exists in the organization.")

        # Check if the domain is on cooldown
        if await self.is_domain_recently_contacted(lead_data.contact_email, lead_data.organization_id):
            raise ValidationError("This domain has been contacted recently and is on a cooldown period.")
        
        # Create lead
        created_lead_data = await self.leads_repo.create(lead_data.dict())
        return Lead(**created_lead_data)

    async def get_leads(self, organization_id: UUID, pagination: PaginationParams, filters: LeadFilter) -> PaginatedLeads:
        """Get a paginated list of leads with filtering"""
        conditions = {"organization_id": str(organization_id)}
        if filters.location:
            conditions['location'] = filters.location
        if filters.industry:
            conditions['industry'] = filters.industry
        
        # This is a simplification. Real filtering might be more complex (e.g., using ilike for text search)
        
        total = await self.leads_repo.count(conditions)
        
        db_leads = await self.leads_repo.get_many(
            conditions=conditions,
            limit=pagination.limit,
            offset=pagination.offset,
            order_by="created_at DESC"
        )
        
        items = [Lead(**lead) for lead in db_leads]
        
        return PaginatedLeads(
            items=items,
            total=total,
            page=pagination.page,
            size=pagination.limit
        )

    async def update_lead(self, lead_id: UUID, update_data: LeadUpdate, organization_id: UUID) -> Lead:
        """Update a lead's information"""
        await self.get_lead_by_id(lead_id, organization_id) #
        
        update_dict = update_data.dict(exclude_unset=True)
        update_dict['updated_at'] = datetime.utcnow().isoformat()
        
        updated_lead_data = await self.leads_repo.update(str(lead_id), update_dict)
        return Lead(**updated_lead_data)

    async def update_lead_status(self, lead_id: UUID, status: LeadStatus, organization_id: UUID) -> Lead:
        """Update the status of a lead"""
        return await self.update_lead(lead_id, LeadUpdate(status=status), organization_id)

    async def blacklist_lead(self, lead_id: UUID, organization_id: UUID) -> Lead:
        """Blacklist a lead to prevent further contact"""
        return await self.update_lead_status(lead_id, LeadStatus.BLACKLISTED, organization_id)
    
    async def is_domain_recently_contacted(self, email: str, organization_id: UUID, days: int = 90) -> bool:
        """Check if any lead with the same domain has been contacted recently."""
        domain = email.split('@')[1]
        ninety_days_ago = datetime.utcnow() - timedelta(days=days)

        # This requires a more complex query than BaseRepository provides.
        # We need to query leads by domain and check their last_contacted_at.
        # This is a placeholder for the actual implementation. A custom SQL query would be needed here.
        # For example: SELECT 1 FROM leads WHERE organization_id = :org_id AND contact_email LIKE :domain AND last_contacted_at > :date LIMIT 1
        
        # A simplified check on email_sends table:
        recent_sends = await self.email_sends_repo.get_many(
            conditions={"to_email": f"%@{domain}"},
            # This is not a valid condition for the base repo, it would require `like` support
            # And we need to check organization_id as well, which is not in email_sends table directly
        )
        # This logic needs to be more robust.
        return False

    async def apply_cooldown(self, lead_id: UUID, organization_id: UUID):
        """
        Applies a cooldown status to a lead if it has been used in multiple campaigns.
        A lead that has been used 3+ times is set to cooldown status.
        """
        lead = await self.get_lead_by_id(lead_id, organization_id)
        if len(lead.used_by_campaigns) >= 3:
            await self.update_lead_status(lead_id, LeadStatus.COOLDOWN, organization_id)

    async def source_leads_from_apollo(self, filters: LeadFilter, organization_id: UUID, api_key: str, page: int = 1) -> List[Lead]:
        """Sourcing leads from Apollo.io and saving them to the database."""
        apollo_service = ApolloService(api_key=api_key)
        
        # Fetch leads from Apollo
        apollo_leads = await apollo_service.search_leads(filters, str(organization_id), page)
        
        created_leads = []
        for lead_data in apollo_leads:
            try:
                # The create_lead method already handles duplicate checks
                created_lead = await self.create_lead(lead_data)
                created_leads.append(created_lead)
            except ConflictError:
                logger.info(f"Skipping duplicate lead: {lead_data.contact_email}")
            except Exception as e:
                logger.error(f"Error creating lead from Apollo data: {e}")

        return created_leads
