import logging
from typing import Dict, Any, List
from app.modules.agents.base import Selda<PERSON>gent, agent_registry
from app.modules.agents.models import (
    AgentType,
    StrategicPlanningInput,
    StrategicPlanningOutput
)
from app.modules.leads.apollo_service import apollo_service

logger = logging.getLogger(__name__)

@agent_registry.register(AgentType.STRATEGIC_PLANNING)
class StrategicPlanningAgent(SeldaAgent):
    """Agent for lead research and campaign planning"""

    @property
    def agent_type(self) -> AgentType:
        return AgentType.STRATEGIC_PLANNING

    @property
    def name(self) -> str:
        return "Strategic Planning Specialist"

    @property
    def description(self) -> str:
        return "Plans sales campaigns, researches leads, and develops campaign strategies."

    def _get_agent_goal(self) -> str:
        return "Develop a comprehensive sales campaign strategy, research and score leads, and outline a plan for outreach and success."

    def _get_agent_backstory(self) -> str:
        return "You are a sales strategist with expertise in campaign planning, lead research, and data-driven decision making."

    def get_capabilities(self) -> List[Dict[str, Any]]:
        return [
            {
                "name": "lead_generation",
                "description": "Generate and score leads using Apollo.io and internal data.",
                "parameters": {"input": "Campaign and audience details", "output": "Scored leads list"}
            },
            {
                "name": "campaign_strategy",
                "description": "Develop a campaign plan including content, timeline, and metrics.",
                "parameters": {"input": "Goal and constraints", "output": "Campaign strategy"}
            }
        ]

    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute strategic planning logic: lead research, scoring, market research, and campaign plan."""
        try:
            sp_input = StrategicPlanningInput(**input_data)
            # 1. Lead Generation using Apollo.io
            lead_query = self._build_lead_query(sp_input)
            leads = await apollo_service.search_leads(lead_query)
            # 2. Lead Scoring
            scored_leads = self._score_leads(leads, sp_input)
            # 3. Market Research
            market_research = await self._perform_market_research(sp_input)
            # 4. Campaign Strategy
            campaign_strategy = self._build_campaign_strategy(sp_input, scored_leads, market_research)
            # 5. Content Strategy (placeholder, could use LLM)
            content_strategy = self._build_content_strategy(sp_input)
            # 6. Timeline
            timeline = self._build_timeline(sp_input)
            # 7. Success Metrics
            success_metrics = self._suggest_success_metrics(sp_input)
            # 8. Risk Assessment
            risk_assessment = self._assess_risks(sp_input, scored_leads)
            # 9. Campaign Plan
            campaign_plan = self._build_campaign_plan(sp_input, campaign_strategy, content_strategy, timeline, market_research)
            output = StrategicPlanningOutput(
                campaign_strategy=campaign_strategy,
                lead_generation_plan={"scored_leads": scored_leads},
                content_strategy=content_strategy,
                timeline=timeline,
                success_metrics=success_metrics,
                risk_assessment=risk_assessment
            )
            # Add market research and campaign plan to output
            output_dict = output.dict()
            output_dict["market_research"] = market_research
            output_dict["campaign_plan"] = campaign_plan
            return output_dict
        except Exception as e:
            logger.error(f"Strategic planning execution failed: {e}")
            raise

    def _build_lead_query(self, sp_input: StrategicPlanningInput) -> Dict[str, Any]:
        # Build Apollo.io query from input
        query = {}
        if sp_input.target_audience.get("industry"):
            query["industry"] = sp_input.target_audience["industry"]
        if sp_input.target_audience.get("company_size"):
            query["company_size"] = sp_input.target_audience["company_size"]
        if sp_input.target_audience.get("job_titles"):
            query["job_titles"] = ",".join(sp_input.target_audience["job_titles"])
        if sp_input.target_audience.get("geography"):
            query["location"] = sp_input.target_audience["geography"]
        query["per_page"] = 20
        return query

    def _score_leads(self, leads: List[Dict[str, Any]], sp_input: StrategicPlanningInput) -> List[Dict[str, Any]]:
        # Simple scoring: prioritize by title match, industry, and company size
        scored = []
        for lead in leads:
            score = 0
            if "job_title" in lead and lead["job_title"] in sp_input.target_audience.get("job_titles", []):
                score += 3
            if "industry" in lead and lead["industry"] == sp_input.target_audience.get("industry"):
                score += 2
            if "company_size" in lead and lead["company_size"] == sp_input.target_audience.get("company_size"):
                score += 1
            lead["score"] = score
            scored.append(lead)
        # Sort by score descending
        return sorted(scored, key=lambda l: l["score"], reverse=True)

    async def _perform_market_research(self, sp_input: StrategicPlanningInput) -> Dict[str, Any]:
        # Use Apollo.io to gather industry/company insights
        research = {}
        industry = sp_input.target_audience.get("industry")
        if industry:
            companies = await apollo_service.search_companies({"industry": industry, "per_page": 10})
            research["top_companies"] = companies
        # Add more research as needed (e.g., trends, competitors)
        return research

    def _build_campaign_strategy(self, sp_input: StrategicPlanningInput, scored_leads: List[Dict[str, Any]], market_research: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "objective": sp_input.goal.get("objective"),
            "target_audience": sp_input.target_audience,
            "lead_count": len(scored_leads),
            "primary_channels": ["email", "linkedin"],
            "budget": sp_input.budget_constraints,
            "timeline": sp_input.timeline_constraints,
            "market_research_summary": market_research,
            "key_actions": [
                "Segment leads by score",
                "Personalize outreach",
                "Monitor engagement",
                "Iterate based on results"
            ]
        }

    def _build_content_strategy(self, sp_input: StrategicPlanningInput) -> Dict[str, Any]:
        return {
            "content_types": ["email sequences", "LinkedIn messages", "case studies"],
            "personalization": True,
            "brand_guidelines": sp_input.existing_data.get("brand_guidelines") if sp_input.existing_data else None
        }

    def _build_timeline(self, sp_input: StrategicPlanningInput) -> Dict[str, Any]:
        return {
            "phases": [
                {"name": "Preparation", "duration": "1 week"},
                {"name": "Outreach", "duration": "4 weeks"},
                {"name": "Follow-up", "duration": "2 weeks"}
            ],
            "milestones": ["Campaign launch", "First response", "First meeting", "Deal closed"]
        }

    def _suggest_success_metrics(self, sp_input: StrategicPlanningInput) -> List[str]:
        return [
            "Number of qualified leads",
            "Meetings booked",
            "Response rate",
            "Conversion rate",
            "Revenue generated"
        ]

    def _assess_risks(self, sp_input: StrategicPlanningInput, scored_leads: List[Dict[str, Any]]) -> Dict[str, Any]:
        risks = []
        if not scored_leads:
            risks.append("No qualified leads found for the specified criteria.")
        if sp_input.budget_constraints and sp_input.budget_constraints.get("total") == 0:
            risks.append("No budget allocated for campaign.")
        return {"risks": risks, "mitigation": ["Broaden targeting", "Increase budget", "Test new channels"]}

    def _build_campaign_plan(self, sp_input: StrategicPlanningInput, campaign_strategy: Dict[str, Any], content_strategy: Dict[str, Any], timeline: Dict[str, Any], market_research: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "strategy": campaign_strategy,
            "content": content_strategy,
            "timeline": timeline,
            "market_research": market_research,
            "steps": [
                "Finalize target audience and lead list",
                "Develop content and messaging",
                "Schedule outreach phases",
                "Launch campaign",
                "Track and optimize performance"
            ]
        }

# Register the agent
agent_registry[AgentType.STRATEGIC_PLANNING] = StrategicPlanningAgent 