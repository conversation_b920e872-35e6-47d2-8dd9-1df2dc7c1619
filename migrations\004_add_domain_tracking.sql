-- Migration 004: Add domain tracking for cooldown management
-- This migration adds domain column to leads table for efficient cooldown tracking

-- Add domain column to leads table
ALTER TABLE leads 
    ADD COLUMN IF NOT EXISTS domain VARCHAR(255);

-- Add index on domain for efficient cooldown queries
CREATE INDEX IF NOT EXISTS idx_leads_domain ON leads(domain);

-- Add index on last_contacted_at for cooldown queries
CREATE INDEX IF NOT EXISTS idx_leads_last_contacted_at ON leads(last_contacted_at);

-- Add composite index for organization + domain + last_contacted_at
CREATE INDEX IF NOT EXISTS idx_leads_org_domain_contacted 
    ON leads(organization_id, domain, last_contacted_at);

-- Update existing leads to populate domain from contact_email
UPDATE leads 
SET domain = SPLIT_PART(contact_email, '@', 2)
WHERE domain IS NULL AND contact_email IS NOT NULL AND contact_email LIKE '%@%';

-- Add comment for domain column
COMMENT ON COLUMN leads.domain IS 'Email domain extracted from contact_email for cooldown tracking';
