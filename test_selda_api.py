#!/usr/bin/env python3
"""
Comprehensive API Test Script for Selda AI Sales Autopilot
Tests authentication, campaign creation, and workflow execution
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional


class SeldaAPITester:
    """Test client for Selda AI API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.organization_id: Optional[str] = None
        self.user_id: Optional[str] = None
        
    def _make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """Make HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        
        default_headers = {"Content-Type": "application/json"}
        if self.access_token:
            default_headers["Authorization"] = f"Bearer {self.access_token}"
        
        if headers:
            default_headers.update(headers)
        
        try:
            response = requests.request(
                method=method,
                url=url,
                json=data,
                headers=default_headers,
                timeout=30
            )
            
            print(f"📡 {method} {endpoint} -> {response.status_code}")
            
            if response.status_code >= 400:
                print(f"❌ Error: {response.text}")
                return {"error": response.text, "status_code": response.status_code}
            
            return response.json()
            
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return {"error": str(e)}
    
    def test_health_check(self) -> bool:
        """Test basic health check"""
        print("\n🏥 Testing Health Check...")
        print("=" * 50)
        
        result = self._make_request("GET", "/health")
        
        if "status" in result and result["status"] == "healthy":
            print("✅ Health check passed")
            return True
        else:
            print("❌ Health check failed")
            return False
    
    def test_authentication(self) -> bool:
        """Test user registration and login"""
        print("\n🔐 Testing Authentication...")
        print("=" * 50)
        
        # Test registration
        register_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "TestPass123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        print("📝 Registering new user...")
        register_result = self._make_request("POST", "/api/v1/auth/register", register_data)
        
        if "error" in register_result:
            print("❌ Registration failed")
            return False
        
        print("✅ Registration successful")
        
        # Test login
        login_data = {
            "email": register_data["email"],
            "password": register_data["password"]
        }
        
        print("🔑 Logging in...")
        login_result = self._make_request("POST", "/api/v1/auth/login", login_data)
        
        if "access_token" in login_result:
            self.access_token = login_result["access_token"]
            self.user_id = login_result["user"]["id"]
            self.organization_id = login_result["user"]["organization_id"]
            print("✅ Login successful")
            print(f"   User ID: {self.user_id}")
            print(f"   Organization ID: {self.organization_id}")
            return True
        else:
            print("❌ Login failed")
            return False
    
    def test_campaign_creation(self) -> Optional[str]:
        """Test campaign creation"""
        print("\n📋 Testing Campaign Creation...")
        print("=" * 50)
        
        campaign_data = {
            "name": "Test SaaS Outreach Campaign",
            "description": "Automated test campaign for SaaS companies",
            "target_audience": {
                "industry": "Technology",
                "location": "London",
                "company_size": "50-200"
            },
            "goals": {
                "meetings_target": 10,
                "contacts_target": 500,
                "conversion_rate_target": 2.0
            },
            "status": "active"
        }
        
        result = self._make_request("POST", "/api/v1/campaigns", campaign_data)
        
        if "id" in result:
            campaign_id = result["id"]
            print("✅ Campaign created successfully")
            print(f"   Campaign ID: {campaign_id}")
            print(f"   Campaign Name: {result['name']}")
            return campaign_id
        else:
            print("❌ Campaign creation failed")
            return None
    
    def test_individual_agents(self, campaign_id: str) -> bool:
        """Test individual agent execution"""
        print("\n🤖 Testing Individual Agents...")
        print("=" * 50)
        
        # Test Lead Sourcing Agent
        print("🔍 Testing Lead Sourcing Agent...")
        sourcing_data = {
            "agent_type": "lead_sourcing",
            "input_data": {
                "campaign_id": campaign_id,
                "target_audience": {
                    "industry": "Technology",
                    "location": "London"
                },
                "location": "London",
                "industry": "Technology",
                "lead_count_target": 50,
                "sources": ["apollo"],
                "quality_threshold": 70
            }
        }
        
        sourcing_result = self._make_request("POST", "/api/v1/agents/execute", sourcing_data)
        
        if "leads_sourced" in sourcing_result:
            print("✅ Lead Sourcing Agent executed successfully")
            print(f"   Leads Sourced: {sourcing_result.get('leads_sourced', 0)}")
            print(f"   Quality Distribution: {sourcing_result.get('quality_distribution', {})}")
        else:
            print("❌ Lead Sourcing Agent failed")
            print(f"   Error: {sourcing_result}")
        
        # Test Lead Distribution Agent
        print("\n📊 Testing Lead Distribution Agent...")
        distribution_data = {
            "agent_type": "lead_distribution",
            "input_data": {
                "campaign_id": campaign_id,
                "leads_requested": 25,
                "target_audience": {
                    "industry": "Technology",
                    "location": "London"
                },
                "quality_threshold": 70,
                "exclude_domains": ["example.com"],
                "priority_level": 3
            }
        }
        
        distribution_result = self._make_request("POST", "/api/v1/agents/execute", distribution_data)
        
        if "leads_allocated" in distribution_result:
            print("✅ Lead Distribution Agent executed successfully")
            print(f"   Leads Allocated: {distribution_result.get('leads_allocated', 0)}")
            print(f"   Quality Stats: {distribution_result.get('quality_stats', {})}")
        else:
            print("❌ Lead Distribution Agent failed")
            print(f"   Error: {distribution_result}")
        
        return True
    
    def test_workflow_execution(self, campaign_id: str) -> bool:
        """Test complete workflow execution"""
        print("\n🔄 Testing Complete Workflow Execution...")
        print("=" * 50)
        
        # Start workflow
        workflow_data = {
            "campaign_id": campaign_id,
            "target_meetings": 5,
            "target_contacts": 100,
            "target_audience": {
                "industry": "Technology",
                "location": "London",
                "company_size": "50-200",
                "job_titles": ["CEO", "CTO", "VP Engineering"]
            },
            "campaign_goals": {
                "objective": "Generate qualified leads for our SaaS platform",
                "value_proposition": "AI-powered sales automation",
                "pain_points": ["Manual lead generation", "Low conversion rates"]
            },
            "workflow_type": "sales_autopilot"
        }
        
        print("🚀 Starting workflow...")
        workflow_result = self._make_request("POST", "/api/v1/campaigns/workflow/start", workflow_data)
        
        if "workflow_id" not in workflow_result:
            print("❌ Failed to start workflow")
            print(f"   Error: {workflow_result}")
            return False
        
        workflow_id = workflow_result["workflow_id"]
        print("✅ Workflow started successfully")
        print(f"   Workflow ID: {workflow_id}")
        
        # Monitor workflow progress
        print("\n📊 Monitoring workflow progress...")
        max_wait_time = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status_result = self._make_request("GET", f"/api/v1/campaigns/workflow/{workflow_id}/status")
            
            if "status" in status_result:
                status = status_result["status"]
                progress = status_result.get("progress_percentage", 0)
                current_stage = status_result.get("current_stage", "unknown")
                
                print(f"   Status: {status} | Progress: {progress:.1f}% | Stage: {current_stage}")
                
                if status in ["completed", "failed"]:
                    break
            
            time.sleep(10)  # Wait 10 seconds between checks
        
        # Get final workflow details
        print("\n📋 Getting final workflow details...")
        details_result = self._make_request("GET", f"/api/v1/campaigns/workflow/{workflow_id}/details")
        
        if "workflow" in details_result:
            workflow_info = details_result["workflow"]
            stages = details_result["stages"]
            
            print("✅ Workflow completed successfully")
            print(f"   Final Status: {workflow_info['status']}")
            print(f"   Stages Completed: {workflow_info['stages_completed']}/{workflow_info['total_stages']}")
            print(f"   Total Execution Time: {workflow_info['execution_time_ms']}ms")
            
            print("\n📊 Stage Results:")
            for stage in stages:
                status_icon = "✅" if stage["status"] == "success" else "❌"
                print(f"   {status_icon} {stage['stage']}: {stage['execution_time_ms']}ms")
                if stage.get("output_summary"):
                    for key, value in stage["output_summary"].items():
                        print(f"      {key}: {value}")
            
            return True
        else:
            print("❌ Failed to get workflow details")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all tests in sequence"""
        print("🚀 Starting Selda AI Sales Autopilot API Tests")
        print("=" * 60)
        
        results = []
        
        # Test 1: Health Check
        results.append(self.test_health_check())
        
        # Test 2: Authentication
        results.append(self.test_authentication())
        
        if not self.access_token:
            print("❌ Cannot continue without authentication")
            return False
        
        # Test 3: Campaign Creation
        campaign_id = self.test_campaign_creation()
        if not campaign_id:
            print("❌ Cannot continue without campaign")
            return False
        
        results.append(True)
        
        # Test 4: Individual Agents
        results.append(self.test_individual_agents(campaign_id))
        
        # Test 5: Complete Workflow
        results.append(self.test_workflow_execution(campaign_id))
        
        # Summary
        print("\n" + "=" * 60)
        if all(results):
            print("🎉 ALL TESTS PASSED!")
            print("\n✨ Selda AI Sales Autopilot is working correctly!")
            print("   ✅ Health check passed")
            print("   ✅ Authentication working")
            print("   ✅ Campaign creation working")
            print("   ✅ Individual agents working")
            print("   ✅ Complete workflow execution working")
            return True
        else:
            print("❌ SOME TESTS FAILED!")
            print(f"   Passed: {sum(results)}/{len(results)} tests")
            return False


def main():
    """Main test execution"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    print(f"Testing Selda AI API at: {base_url}")
    
    tester = SeldaAPITester(base_url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
