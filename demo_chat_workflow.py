#!/usr/bin/env python3
"""
Demo Chat Workflow - Shows how the real chat interface would work
Simulates user interactions without requiring actual input
"""

import asyncio
import sys
import os
import time

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent

class DemoChatWorkflow:
    """Demo of the chat workflow with simulated user interactions"""
    
    def __init__(self):
        self.organization_id = "00000000-0000-4000-8000-000000000001"
        
    def print_banner(self):
        """Print welcome banner"""
        print("\n" + "="*70)
        print("🤖 SELDA AI - Interactive Lead Sourcing Assistant")
        print("="*70)
        print("Welcome! I'm your AI assistant for sourcing high-quality sales leads.")
        print("I'll help you:")
        print("  • Understand your sales goals")
        print("  • Ask clarifying questions if needed")
        print("  • Source targeted leads from Apollo.io")
        print("  • Provide quality scoring and filtering")
        print("\n💡 Example goals:")
        print("  • 'I need 10 meetings with restaurant owners in London'")
        print("  • 'Find me tech startup CEOs in San Francisco for 5 meetings'")
        print("  • 'Source leads for SaaS companies in New York'")
        print("-"*70)
        
    def simulate_typing(self, text: str, delay: float = 0.03):
        """Simulate typing effect"""
        for char in text:
            print(char, end='', flush=True)
            time.sleep(delay)
        print()
        
    def print_user_input(self, text: str):
        """Print simulated user input"""
        print(f"\n👤 {text}")
        
    def print_ai_response(self, text: str):
        """Print AI response"""
        print(f"\n🤖 ", end='')
        self.simulate_typing(text)
        
    def print_separator(self, title: str = ""):
        """Print section separator"""
        if title:
            print(f"\n{'='*20} {title} {'='*20}")
        else:
            print("\n" + "-"*60)
            
    async def demo_scenario_1(self):
        """Demo scenario 1: Complete goal from start"""
        self.print_separator("DEMO SCENARIO 1: Complete Goal")
        
        # User provides complete goal
        user_goal = "I need 3 meetings with restaurant owners in London"
        self.print_user_input(user_goal)
        
        self.print_ai_response("Let me understand your goal...")
        
        # Process with goal agent
        goal_agent = GoalUnderstandingAgent(self.organization_id)
        
        goal_input = {
            "goal_description": user_goal,
            "context": "Demo chat interface for lead sourcing",
            "user_id": "demo-user-001",
            "organization_id": self.organization_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result = await goal_agent.execute(goal_input)
        
        self.print_ai_response("Perfect! I have all the information I need:")

        parsed_goal = result.get('parsed_goal', {})
        target_audience = parsed_goal.get('target_audience', {})

        print(f"   🎯 Industry: {target_audience.get('industry', 'restaurants')}")
        print(f"   📍 Location: {target_audience.get('geography', 'London')}")
        print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 3)} meetings")
        print(f"   ✅ Confidence: {result.get('confidence_score', 0.95):.1%}")
        
        # Proceed to lead sourcing with extracted data
        lead_sourcing_data = {
            "industry": target_audience.get('industry', 'restaurants'),
            "location": target_audience.get('geography', 'London'),
            "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 3)
        }
        await self.demo_lead_sourcing(lead_sourcing_data)
        
    async def demo_scenario_2(self):
        """Demo scenario 2: Incomplete goal with follow-ups"""
        self.print_separator("DEMO SCENARIO 2: Incomplete Goal with Follow-ups")
        
        # User provides incomplete goal
        user_goal = "I need some meetings with business owners"
        self.print_user_input(user_goal)
        
        self.print_ai_response("Let me understand your goal...")
        
        # Process with goal agent
        goal_agent = GoalUnderstandingAgent(self.organization_id)
        
        goal_input = {
            "goal_description": user_goal,
            "context": "Demo chat interface for lead sourcing",
            "user_id": "demo-user-002",
            "organization_id": self.organization_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result = await goal_agent.execute(goal_input)
        
        # Show follow-up questions
        follow_ups = result.get("follow_up_questions", [])
        if follow_ups:
            self.print_ai_response("I need some additional information to help you better:")
            
            for i, question in enumerate(follow_ups, 1):
                print(f"\n💬 ({i}/{len(follow_ups)}) {question}")
                
                # Simulate user answers
                if "industry" in question.lower():
                    answer = "restaurants"
                elif "location" in question.lower():
                    answer = "London, UK"
                elif "meeting" in question.lower() or "target" in question.lower():
                    answer = "5"
                else:
                    answer = "medium"
                
                self.print_user_input(answer)
            
            # Process follow-up answers
            self.print_ai_response("Thank you! Let me process your answers...")
            
            answers = {
                "industry": "restaurants",
                "location": "London, UK",
                "target_meetings": "5"
            }
            
            follow_up_input = {
                "goal_description": user_goal,
                "answers": answers
            }
            
            final_result = await goal_agent.execute(follow_up_input)
            
            self.print_ai_response("Excellent! Now I have everything I need:")
            print(f"   🎯 Industry: {final_result.get('industry', 'N/A')}")
            print(f"   📍 Location: {final_result.get('location', 'N/A')}")
            print(f"   🔢 Target: {final_result.get('target_meetings', 'N/A')} meetings")
            print(f"   ✅ Confidence: {final_result.get('confidence', 0):.1f}%")
            
            # Proceed to lead sourcing
            await self.demo_lead_sourcing(final_result)
            
    async def demo_lead_sourcing(self, goal_result):
        """Demo the lead sourcing phase"""
        self.print_separator("LEAD SOURCING")
        
        self.print_ai_response("Now I'll source leads for your goal. This may take a moment...")
        
        target_meetings = goal_result.get("target_meetings", 5)
        lead_count_target = target_meetings * 1  # 1 lead per meeting for demo
        
        print(f"\n🔍 Sourcing {lead_count_target} leads for {target_meetings} meetings...")
        print(f"   🎯 Industry: {goal_result.get('industry', 'N/A')}")
        print(f"   📍 Location: {goal_result.get('location', 'N/A')}")
        print(f"   📊 Quality Threshold: 50%")
        
        # Show progress
        print(f"\n⏳ Processing...")
        print("   🔍 Searching Apollo.io database...")
        time.sleep(1)
        print("   📧 Enriching contact emails...")
        time.sleep(1)
        print("   🎯 Filtering for quality...")
        time.sleep(1)
        print("   💾 Storing leads...")
        time.sleep(1)
        
        # Execute lead sourcing
        lead_agent = LeadSourcingAgent(self.organization_id)
        
        sourcing_input = {
            "campaign_id": f"demo-campaign-{int(time.time())}",
            "target_audience": {
                "industry": goal_result.get("industry", "restaurants"),
                "location": goal_result.get("location", "London"),
                "company_size": "any",
                "titles": ["CEO", "Owner", "Manager", "Director"]
            },
            "industry": goal_result.get("industry", "restaurants"),
            "location": goal_result.get("location", "London"),
            "lead_count_target": lead_count_target,
            "quality_threshold": 50,
            "sources": ["apollo"]
        }
        
        sourcing_result = await lead_agent.execute(sourcing_input)
        
        # Display results
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        duplicates = sourcing_result.get("duplicates_filtered", 0)
        cooldown = sourcing_result.get("cooldown_filtered", 0)
        
        self.print_ai_response("Lead sourcing completed! Here are the results:")
        print(f"   ✅ Leads sourced: {leads_sourced}")
        print(f"   🔄 Duplicates filtered: {duplicates}")
        print(f"   ⏰ Cooldown filtered: {cooldown}")
        
        # Show quality distribution
        quality_dist = sourcing_result.get("quality_distribution", {})
        if quality_dist:
            print(f"   📊 Quality distribution:")
            print(f"      🟢 High quality: {quality_dist.get('high', 0)} leads")
            print(f"      🟡 Medium quality: {quality_dist.get('medium', 0)} leads")
            print(f"      🔴 Low quality: {quality_dist.get('low', 0)} leads")
        
        # Show source breakdown
        sources = sourcing_result.get("leads_by_source", {})
        if sources:
            print(f"   🔗 Sources:")
            for source, count in sources.items():
                print(f"      📡 {source.title()}: {count} leads")
        
        # Final summary
        self.display_final_summary(goal_result, sourcing_result)
        
    def display_final_summary(self, goal_result, sourcing_result):
        """Display final summary"""
        self.print_separator("CAMPAIGN SUMMARY")
        
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        target_meetings = goal_result.get("target_meetings", 0)
        
        self.print_ai_response("🎉 Your lead sourcing campaign is complete!")
        print(f"\n📋 Campaign Details:")
        print(f"   🎯 Goal: {goal_result.get('goal_description', 'N/A')}")
        print(f"   🏭 Industry: {goal_result.get('industry', 'N/A')}")
        print(f"   🌍 Location: {goal_result.get('location', 'N/A')}")
        print(f"   🎯 Target Meetings: {target_meetings}")
        print(f"   📊 Leads Sourced: {leads_sourced}")
        
        if leads_sourced > 0:
            ratio = leads_sourced / target_meetings if target_meetings > 0 else 0
            print(f"   📈 Lead-to-Meeting Ratio: {ratio:.1f}:1")
            
            self.print_ai_response("✅ Success! Your leads are ready for outreach.")
            print("   💡 Next steps:")
            print("      1. Review the sourced leads in your dashboard")
            print("      2. Customize your outreach messages")
            print("      3. Start your email campaign")
            print("      4. Track responses and book meetings")
        else:
            self.print_ai_response("⚠️ No leads were sourced. This could be due to:")
            print("   • Very specific targeting criteria")
            print("   • API limitations or credits")
            print("   • Temporary service issues")
            print("   💡 Try adjusting your criteria or contact support.")
            
    async def run_demo(self):
        """Run the demo scenarios"""
        try:
            self.print_banner()
            
            print("\n🎬 This demo shows how the interactive chat interface works.")
            print("   I'll simulate user interactions and show the complete workflow.")
            
            # Demo scenario 1
            await self.demo_scenario_1()
            
            print("\n" + "="*70)
            print("🎬 DEMO SCENARIO 2 - Let's try an incomplete goal...")
            time.sleep(2)
            
            # Demo scenario 2
            await self.demo_scenario_2()
            
            print("\n" + "="*70)
            self.print_ai_response("🎬 Demo completed! This shows how users can interact with Selda AI:")
            print("   • Natural language goal input")
            print("   • Intelligent follow-up questions")
            print("   • Real-time lead sourcing from Apollo.io")
            print("   • Quality scoring and filtering")
            print("   • Complete campaign summary")
            print("\n🚀 The actual chat interface would work exactly like this,")
            print("   but with real user input instead of simulated responses!")
            
        except Exception as e:
            print(f"❌ Demo error: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """Main entry point"""
    demo = DemoChatWorkflow()
    await demo.run_demo()

if __name__ == "__main__":
    print("🚀 Starting Selda AI Chat Workflow Demo...")
    asyncio.run(main())
