#!/usr/bin/env python3
"""Test script for campaign planning functions"""

import asyncio
from real_chat_interface import calculate_lead_volume, plan_lead_sources, create_campaign_timeline

def test_campaign_planning():
    """Test the campaign planning functions"""
    print("🧪 Testing Campaign Planning Functions")
    print("="*50)
    
    # Test lead volume calculation
    lead_plan = calculate_lead_volume(5)
    print("📊 Lead Volume for 5 meetings:")
    print(f"   Total leads needed: {lead_plan['total_leads_needed']}")
    print(f"   Expected responses: {lead_plan['responses_needed']}")
    print(f"   Expected opens: {lead_plan['opens_needed']}")
    print(f"   Email open rate: {lead_plan['email_open_rate']}%")
    print(f"   Response rate: {lead_plan['response_rate']}%")
    print(f"   Meeting rate: {lead_plan['meeting_rate']}%")
    
    # Test source distribution
    sources = plan_lead_sources(lead_plan['total_leads_needed'])
    print(f"\n🔗 Source Distribution:")
    for source, count in sources.items():
        percentage = (count / lead_plan['total_leads_needed']) * 100
        print(f"   {source}: {count} leads ({percentage:.1f}%)")
    
    # Test timeline
    timeline = create_campaign_timeline(5, lead_plan['total_leads_needed'])
    print(f"\n📅 Campaign Timeline:")
    print(f"   Duration: {timeline['total_duration']} days")
    print(f"   Sourcing phase: {timeline['sourcing_phase']['duration']} days")
    print(f"   Email phases: {len(timeline['email_phases'])}")
    
    for phase in timeline['email_phases']:
        print(f"     • {phase['name']}: Day {phase['start_day']}-{phase['end_day']}")
    
    print("\n✅ Campaign planning functions working!")

if __name__ == "__main__":
    test_campaign_planning()
