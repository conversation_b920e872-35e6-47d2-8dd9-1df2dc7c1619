"""
Custom exceptions and exception handlers for the application
"""

from typing import Any, Dict
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

logger = logging.getLogger(__name__)


class SeldaAIException(Exception):
    """Base exception for Selda AI application"""
    
    def __init__(self, message: str, code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(SeldaAIException):
    """Authentication related errors"""
    pass


class AuthorizationError(SeldaAIException):
    """Authorization related errors"""
    pass


class ValidationError(SeldaAIException):
    """Data validation errors"""
    pass


class NotFoundError(SeldaAIException):
    """Resource not found errors"""
    pass


class ConflictError(SeldaAIException):
    """Resource conflict errors"""
    pass


class ExternalServiceError(SeldaAIException):
    """External service integration errors"""
    pass


class RateLimitError(SeldaAIException):
    """Rate limiting errors"""
    pass


class AgentError(SeldaAIException):
    """AI Agent related errors"""
    pass


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup global exception handlers"""
    
    @app.exception_handler(SeldaAIException)
    async def selda_ai_exception_handler(request: Request, exc: SeldaAIException):
        """Handle custom Selda AI exceptions"""
        logger.error(
            f"Selda AI Exception [{exc.code}]: {exc.message} | "
            f"Path: {request.url.path} | Method: {request.method} | "
            f"Details: {exc.details}",
            exc_info=exc
        )
        
        status_code = 400
        if isinstance(exc, AuthenticationError):
            status_code = 401
        elif isinstance(exc, AuthorizationError):
            status_code = 403
        elif isinstance(exc, NotFoundError):
            status_code = 404
        elif isinstance(exc, ConflictError):
            status_code = 409
        elif isinstance(exc, RateLimitError):
            status_code = 429
        elif isinstance(exc, ExternalServiceError):
            status_code = 502
        
        return JSONResponse(
            status_code=status_code,
            content={
                "error": {
                    "code": exc.code,
                    "message": exc.message,
                    "details": exc.details
                }
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle FastAPI HTTP exceptions"""
        logger.warning(
            f"HTTP Exception [{exc.status_code}]: {exc.detail} | "
            f"Path: {request.url.path} | Method: {request.method}"
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "details": {}
                }
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle request validation errors"""
        logger.warning(
            f"Request validation failed | Path: {request.url.path} | "
            f"Method: {request.method} | Errors: {exc.errors()}"
        )
        
        # Convert errors to JSON-serializable format
        errors = []
        for error in exc.errors():
            if isinstance(error, dict):
                # Convert any non-serializable values to strings
                serializable_error = {}
                for key, value in error.items():
                    try:
                        import json
                        json.dumps(value)  # Test if value is JSON serializable
                        serializable_error[key] = value
                    except (TypeError, ValueError):
                        serializable_error[key] = str(value)
                errors.append(serializable_error)
            else:
                errors.append(str(error))

        return JSONResponse(
            status_code=422,
            content={
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "Request validation failed",
                    "details": {
                        "errors": errors
                    }
                }
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def starlette_exception_handler(request: Request, exc: StarletteHTTPException):
        """Handle Starlette HTTP exceptions"""
        logger.error(
            f"Starlette HTTP Exception [{exc.status_code}]: {exc.detail} | "
            f"Path: {request.url.path} | Method: {request.method}"
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "details": {}
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle all other exceptions"""
        logger.error(
            f"Unhandled Exception: {str(exc)} | "
            f"Path: {request.url.path} | Method: {request.method}",
            exc_info=exc
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "An unexpected error occurred",
                    "details": {}
                }
            }
        )
