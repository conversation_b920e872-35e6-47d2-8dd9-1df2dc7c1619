"""
Lead Distribution Agent - Manages fair distribution of leads to campaigns and prevents duplicates
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from uuid import UUID

from app.modules.agents.base import SeldaAgent, agent_registry
from app.modules.agents.models import AgentType
from app.modules.leads.models import LeadStatus
from app.core.database import BaseRepository

logger = logging.getLogger(__name__)


class LeadDistributionInput(BaseModel):
    """Input model for lead distribution agent"""
    campaign_id: str = Field(..., description="Campaign ID requesting leads")
    leads_requested: int = Field(..., description="Number of leads requested")
    target_audience: Dict[str, Any] = Field(..., description="Target audience criteria")
    quality_threshold: int = Field(70, description="Minimum quality score")
    exclude_domains: List[str] = Field(default=[], description="Domains to exclude")
    priority_level: int = Field(1, description="Campaign priority (1-5, 5 being highest)")


class LeadDistributionOutput(BaseModel):
    """Output model for lead distribution agent"""
    leads_allocated: int = Field(..., description="Number of leads allocated to campaign")
    lead_ids: List[str] = Field(..., description="List of allocated lead IDs")
    quality_stats: Dict[str, Any] = Field(..., description="Quality statistics of allocated leads")
    distribution_summary: Dict[str, Any] = Field(..., description="Distribution operation summary")
    next_agent_input: Dict[str, Any] = Field(..., description="Input for next agent in pipeline")


@agent_registry.register(AgentType.LEAD_DISTRIBUTION)
class LeadDistributionAgent(SeldaAgent):
    """Agent for managing lead distribution and allocation"""

    @property
    def agent_type(self) -> AgentType:
        return AgentType.LEAD_DISTRIBUTION

    @property
    def name(self) -> str:
        return "Lead Distribution Manager"

    @property
    def description(self) -> str:
        return "Manages fair distribution of leads to campaigns, prevents duplicates, and ensures optimal lead allocation"

    def _get_agent_goal(self) -> str:
        return """Fairly distribute leads to campaigns while preventing duplicates, managing cooldowns, 
        and ensuring optimal lead allocation based on campaign priorities and requirements."""

    def _get_agent_backstory(self) -> str:
        return """You are a lead distribution specialist responsible for ensuring fair and efficient 
        allocation of leads across campaigns. You prevent over-contact, manage lead lifecycles, 
        and optimize distribution for maximum campaign success."""

    def get_capabilities(self) -> List[Dict[str, Any]]:
        return [
            {
                "name": "lead_allocation",
                "description": "Allocate leads to campaigns based on criteria",
                "parameters": {"campaign_id": "string", "lead_count": "integer", "filters": "object"}
            },
            {
                "name": "duplicate_prevention",
                "description": "Prevent duplicate lead allocation across campaigns",
                "parameters": {"domain": "string", "timeframe_days": "integer"}
            },
            {
                "name": "priority_management",
                "description": "Manage campaign priorities for lead allocation",
                "parameters": {"priority_level": "integer", "campaign_type": "string"}
            },
            {
                "name": "cooldown_enforcement",
                "description": "Enforce cooldown periods for lead reuse",
                "parameters": {"domain": "string", "cooldown_days": "integer"}
            }
        ]

    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute lead distribution logic"""
        try:
            distribution_input = LeadDistributionInput(**input_data)
            
            # Initialize repositories
            leads_repo = BaseRepository("leads", self.db)
            distribution_repo = BaseRepository("lead_distribution", self.db)
            campaigns_repo = BaseRepository("campaigns", self.db)
            
            # Get available leads
            available_leads = await self._get_available_leads(
                distribution_input, leads_repo, distribution_repo
            )
            
            # Allocate leads based on priority and criteria
            allocated_leads = await self._allocate_leads(
                available_leads, distribution_input, leads_repo, distribution_repo
            )
            
            # Calculate quality statistics
            quality_stats = self._calculate_quality_stats(allocated_leads)
            
            # Update lead statuses
            await self._update_lead_statuses(allocated_leads, distribution_input.campaign_id, leads_repo)
            
            # Create distribution records
            await self._create_distribution_records(
                allocated_leads, distribution_input.campaign_id, distribution_repo
            )
            
            # Prepare next agent input
            next_agent_input = {
                "campaign_id": distribution_input.campaign_id,
                "allocated_leads": [lead["id"] for lead in allocated_leads],
                "lead_count": len(allocated_leads),
                "target_audience": distribution_input.target_audience,
                "quality_stats": quality_stats
            }
            
            output = LeadDistributionOutput(
                leads_allocated=len(allocated_leads),
                lead_ids=[lead["id"] for lead in allocated_leads],
                quality_stats=quality_stats,
                distribution_summary={
                    "requested": distribution_input.leads_requested,
                    "allocated": len(allocated_leads),
                    "fulfillment_rate": len(allocated_leads) / distribution_input.leads_requested * 100,
                    "average_quality": quality_stats.get("average_score", 0),
                    "priority_level": distribution_input.priority_level
                },
                next_agent_input=next_agent_input
            )
            
            return output.dict()
            
        except Exception as e:
            logger.error(f"Lead distribution execution failed: {e}")
            raise

    async def _get_available_leads(
        self, 
        distribution_input: LeadDistributionInput,
        leads_repo: BaseRepository,
        distribution_repo: BaseRepository
    ) -> List[Dict[str, Any]]:
        """Get available leads that meet criteria and aren't in cooldown"""
        
        # Build filters for lead selection
        filters = {
            "organization_id": self.organization_id,
            "status": LeadStatus.UNUSED.value
        }
        
        # Add audience filters
        if distribution_input.target_audience.get("industry"):
            filters["industry"] = distribution_input.target_audience["industry"]
        
        if distribution_input.target_audience.get("location"):
            filters["location"] = distribution_input.target_audience["location"]
        
        # Get potential leads
        potential_leads = await leads_repo.list(
            filters=filters,
            limit=distribution_input.leads_requested * 3,  # Get more than needed for filtering
            order_by="contact_quality_score",
            ascending=False,  # Highest quality first
            use_admin=True
        )
        
        # Filter out leads that are in cooldown or excluded domains
        available_leads = []
        for lead in potential_leads:
            # Check quality threshold
            if lead.get("contact_quality_score", 0) < distribution_input.quality_threshold:
                continue
            
            # Check excluded domains
            domain = lead.get("domain", "")
            if domain in distribution_input.exclude_domains:
                continue
            
            # Check if domain is in cooldown
            if await self._is_domain_in_cooldown(domain, distribution_repo):
                continue
            
            # Check if lead has been used too many times
            used_campaigns = lead.get("used_by_campaigns", [])
            if len(used_campaigns) >= 3:  # Max 3 uses before cooldown
                continue
            
            available_leads.append(lead)
            
            # Stop when we have enough leads
            if len(available_leads) >= distribution_input.leads_requested:
                break
        
        return available_leads

    async def _allocate_leads(
        self,
        available_leads: List[Dict[str, Any]],
        distribution_input: LeadDistributionInput,
        leads_repo: BaseRepository,
        distribution_repo: BaseRepository
    ) -> List[Dict[str, Any]]:
        """Allocate leads to the campaign"""
        
        # Sort by quality score (highest first) and priority factors
        sorted_leads = sorted(
            available_leads,
            key=lambda x: (
                x.get("contact_quality_score", 0),
                -len(x.get("used_by_campaigns", [])),  # Prefer less used leads
                x.get("created_at", "")  # Prefer newer leads
            ),
            reverse=True
        )
        
        # Take the requested number of leads
        allocated_leads = sorted_leads[:distribution_input.leads_requested]
        
        return allocated_leads

    async def _update_lead_statuses(
        self,
        allocated_leads: List[Dict[str, Any]],
        campaign_id: str,
        leads_repo: BaseRepository
    ) -> None:
        """Update lead statuses to reserved"""
        
        for lead in allocated_leads:
            # Update used_by_campaigns
            used_campaigns = lead.get("used_by_campaigns", [])
            if campaign_id not in used_campaigns:
                used_campaigns.append(campaign_id)
            
            # Update lead
            await leads_repo.update(
                lead["id"],
                {
                    "status": LeadStatus.RESERVED.value,
                    "used_by_campaigns": used_campaigns,
                    "updated_at": datetime.utcnow().isoformat()
                },
                use_admin=True
            )

    async def _create_distribution_records(
        self,
        allocated_leads: List[Dict[str, Any]],
        campaign_id: str,
        distribution_repo: BaseRepository
    ) -> None:
        """Create distribution tracking records"""
        
        for lead in allocated_leads:
            await distribution_repo.create({
                "organization_id": self.organization_id,
                "campaign_id": campaign_id,
                "lead_id": lead["id"],
                "status": "assigned"
            }, use_admin=True)

    async def _is_domain_in_cooldown(
        self,
        domain: str,
        distribution_repo: BaseRepository
    ) -> bool:
        """Check if domain is in cooldown period"""
        
        if not domain:
            return False
        
        # Check for recent contacts to this domain (90 days)
        cooldown_date = datetime.utcnow() - timedelta(days=90)
        
        recent_distributions = await distribution_repo.list(
            filters={
                "organization_id": self.organization_id,
                "status": "contacted"
            },
            limit=100,
            use_admin=True
        )
        
        # Check if any recent distribution involved this domain
        for dist in recent_distributions:
            if dist.get("assigned_at"):
                assigned_date = datetime.fromisoformat(dist["assigned_at"].replace("Z", "+00:00"))
                if assigned_date > cooldown_date:
                    # Would need to join with leads table to check domain
                    # For now, implement basic cooldown logic
                    return False
        
        return False

    def _calculate_quality_stats(self, allocated_leads: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate quality statistics for allocated leads"""
        
        if not allocated_leads:
            return {
                "average_score": 0,
                "min_score": 0,
                "max_score": 0,
                "score_distribution": {"high": 0, "medium": 0, "low": 0}
            }
        
        scores = [lead.get("contact_quality_score", 0) for lead in allocated_leads]
        
        # Score distribution
        high_quality = len([s for s in scores if s >= 80])
        medium_quality = len([s for s in scores if 60 <= s < 80])
        low_quality = len([s for s in scores if s < 60])
        
        return {
            "average_score": sum(scores) / len(scores),
            "min_score": min(scores),
            "max_score": max(scores),
            "score_distribution": {
                "high": high_quality,
                "medium": medium_quality,
                "low": low_quality
            },
            "total_leads": len(allocated_leads)
        }
