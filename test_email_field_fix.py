#!/usr/bin/env python3
"""
Test to verify the email field fix
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.leads.apollo_service import ApolloService

async def test_email_field_fix():
    """Test that we're correctly detecting real emails from Apollo.io"""
    print("🧪 Testing Email Field Fix")
    print("=" * 40)
    
    try:
        apollo_service = ApolloService()
        
        # Test 1: Small people search
        print("\n1️⃣ Testing people search...")
        search_data = {
            "page": 1,
            "per_page": 3,  # Very small sample
            "q_organization_locations": "London",
            "organization_industries": "restaurants"
        }
        
        resp = await apollo_service._request("POST", "/mixed_people/search", data=search_data)
        people = resp.get("people", [])
        
        print(f"   Found {len(people)} people")
        
        if not people:
            print("❌ No people found")
            return False
        
        # Show original emails
        print("\n2️⃣ Original emails from search:")
        for i, person in enumerate(people):
            email = person.get("email", "N/A")
            print(f"   Person {i+1}: {email}")
        
        # Test 2: Bulk enrichment
        person_ids = [person.get("id") for person in people if person.get("id")]
        print(f"\n3️⃣ Testing bulk enrichment for {len(person_ids)} people...")
        
        enriched_people = await apollo_service.bulk_enrich_people(person_ids)
        
        print(f"   Enriched {len(enriched_people)} people")
        
        # Test 3: Check email detection
        print("\n4️⃣ Email analysis after enrichment:")
        real_emails = []
        locked_emails = []
        
        for i, person in enumerate(enriched_people):
            email = person.get("email", "N/A")
            is_real = email and "email_not_unlocked" not in email and "@domain.com" not in email
            
            print(f"   Person {i+1}: Email='{email}', Real={is_real}")
            
            if is_real:
                real_emails.append(email)
            else:
                locked_emails.append(email)
        
        print(f"\n📊 Results:")
        print(f"   Real emails found: {len(real_emails)}")
        print(f"   Locked emails: {len(locked_emails)}")
        
        if real_emails:
            print(f"   ✅ Real emails: {real_emails}")
            return True
        else:
            print(f"   ⚠️ No real emails found (may need Apollo.io credits)")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_email_field_fix()
    
    if success:
        print("\n🎉 Email field fix is working!")
    else:
        print("\n🔧 Email field needs attention")

if __name__ == "__main__":
    asyncio.run(main())
