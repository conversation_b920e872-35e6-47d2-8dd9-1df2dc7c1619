"""
Shared Pydantic models and schemas
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields"""
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None


class BaseResponse(BaseModel):
    """Base response model"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Error response model"""
    success: bool = False
    error: Dict[str, Any]


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(default=1, ge=1)
    limit: int = Field(default=20, ge=1, le=100)
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.limit


class PaginatedResponse(BaseModel):
    """Paginated response model"""
    items: List[Any]
    total: int
    page: int
    limit: int
    pages: int
    
    @validator('pages', pre=True, always=True)
    def calculate_pages(cls, v, values):
        total = values.get('total', 0)
        limit = values.get('limit', 20)
        return (total + limit - 1) // limit if total > 0 else 0


class SubscriptionTier(str, Enum):
    """Subscription tier enumeration"""
    FREE = "free"
    BASIC = "basic"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class UserRole(str, Enum):
    """User role enumeration"""
    USER = "user"
    ADMIN = "admin"
    OWNER = "owner"


class CampaignStatus(str, Enum):
    """Campaign status enumeration"""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class LeadStatus(str, Enum):
    """Lead status enumeration"""
    NEW = "new"
    CONTACTED = "contacted"
    RESPONDED = "responded"
    QUALIFIED = "qualified"
    CONVERTED = "converted"
    UNQUALIFIED = "unqualified"


class EmailStatus(str, Enum):
    """Email status enumeration"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    OPENED = "opened"
    CLICKED = "clicked"
    REPLIED = "replied"
    BOUNCED = "bounced"
    FAILED = "failed"


class AgentType(str, Enum):
    """Agent type enumeration"""
    GOAL_UNDERSTANDING = "goal_understanding"
    STRATEGIC_PLANNING = "strategic_planning"
    CONTENT_CREATION = "content_creation"
    EMAIL_AUTOMATION = "email_automation"
    REPLY_ANALYSIS = "reply_analysis"
    MEETING_BOOKING = "meeting_booking"
    PERFORMANCE_MONITORING = "performance_monitoring"
    LINKEDIN_OUTREACH = "linkedin_outreach"


# User Models
class UserBase(BaseModel):
    """Base user model"""
    email: EmailStr
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    is_active: bool = True


class UserCreate(UserBase):
    """User creation model"""
    password: str = Field(..., min_length=8, max_length=100)


class UserUpdate(BaseModel):
    """User update model"""
    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    is_active: Optional[bool] = None


class UserResponse(UserBase, TimestampMixin):
    """User response model"""
    id: str
    role: UserRole
    organization_id: Optional[str] = None
    
    class Config:
        from_attributes = True


# Organization Models
class OrganizationBase(BaseModel):
    """Base organization model"""
    name: str = Field(..., min_length=1, max_length=100)
    subscription_tier: SubscriptionTier = SubscriptionTier.FREE


class OrganizationCreate(OrganizationBase):
    """Organization creation model"""
    pass


class OrganizationUpdate(BaseModel):
    """Organization update model"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    subscription_tier: Optional[SubscriptionTier] = None


class OrganizationResponse(OrganizationBase, TimestampMixin):
    """Organization response model"""
    id: str
    owner_id: str
    
    class Config:
        from_attributes = True


# Lead Models
class LeadBase(BaseModel):
    """Base lead model"""
    email: EmailStr
    first_name: Optional[str] = Field(None, max_length=50)
    last_name: Optional[str] = Field(None, max_length=50)
    company: Optional[str] = Field(None, max_length=100)
    title: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    linkedin_url: Optional[str] = None
    source: Optional[str] = Field(None, max_length=50)
    notes: Optional[str] = None


class LeadCreate(LeadBase):
    """Lead creation model"""
    pass


class LeadUpdate(BaseModel):
    """Lead update model"""
    first_name: Optional[str] = Field(None, max_length=50)
    last_name: Optional[str] = Field(None, max_length=50)
    company: Optional[str] = Field(None, max_length=100)
    title: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    linkedin_url: Optional[str] = None
    status: Optional[LeadStatus] = None
    score: Optional[int] = Field(None, ge=0, le=100)
    notes: Optional[str] = None


class LeadResponse(LeadBase, TimestampMixin):
    """Lead response model"""
    id: str
    organization_id: str
    status: LeadStatus = LeadStatus.NEW
    score: Optional[int] = Field(None, ge=0, le=100)
    
    class Config:
        from_attributes = True


# Campaign Models
class CampaignBase(BaseModel):
    """Base campaign model"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None


class CampaignCreate(CampaignBase):
    """Campaign creation model"""
    goal_id: Optional[str] = None


class CampaignUpdate(BaseModel):
    """Campaign update model"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    status: Optional[CampaignStatus] = None
    settings: Optional[Dict[str, Any]] = None


class CampaignResponse(CampaignBase, TimestampMixin):
    """Campaign response model"""
    id: str
    organization_id: str
    goal_id: Optional[str] = None
    status: CampaignStatus = CampaignStatus.DRAFT
    
    class Config:
        from_attributes = True
