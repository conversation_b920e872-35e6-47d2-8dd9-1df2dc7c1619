"""
Authentication service
"""

from typing import Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
import uuid
import logging

from app.core.database import supabase_service, BaseRepository
from app.core.exceptions import (
    AuthenticationError,
    ValidationError,
    ConflictError,
    NotFoundError
)
from app.core.supabase_auth import supabase_auth_manager
from app.modules.auth.utils import (
    hash_password,
    verify_password,
    create_access_token,
    create_refresh_token,
    verify_token,
    generate_email_verification_token,
    generate_password_reset_token,
    create_email_verification_email,
    create_password_reset_email,
    token_blacklist
)
from app.modules.auth.models import (
    LoginRequest,
    RegisterRequest,
    RefreshTokenRequest,
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerificationRequest,
    ChangePasswordRequest
)

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service class"""
    
    def __init__(self):
        self.users_repo = BaseRepository("users", supabase_service)
        self.organizations_repo = BaseRepository("organizations", supabase_service)
    
    async def register(self, request: RegisterRequest) -> Dict[str, Any]:
        """Register a new user"""
        try:
            # Check if user already exists
            existing_user = await self._get_user_by_email(request.email)
            if existing_user:
                raise ConflictError("User with this email already exists")
            
            # Create organization if provided
            organization_id = None
            if request.organization_name:
                organization_data = {
                    "name": request.organization_name,
                    "subscription_tier": "free"
                }
                organization = await self.organizations_repo.create(organization_data, use_admin=True)
                organization_id = organization["id"]
            
            # Hash password
            password_hash = hash_password(request.password)
            
            # Generate email verification token
            verification_token = generate_email_verification_token()
            
            # Create user
            user_data = {
                "email": request.email,
                "password_hash": password_hash,
                "first_name": request.first_name,
                "last_name": request.last_name,
                "organization_id": organization_id,
                "email_verified": False,
                "is_active": True,
                "role": "owner" if organization_id else "user"
            }
            
            user = await self.users_repo.create(user_data, use_admin=True)
            
            # Update organization owner if created
            if organization_id:
                await self.organizations_repo.update(
                    organization_id, 
                    {"owner_id": user["id"]}, 
                    use_admin=True
                )
            
            # Store verification token (in production, use Redis with expiration)
            # For now, we'll store it in a simple way
            await self._store_verification_token(user["id"], verification_token)
            
            # Send verification email (implement email service)
            await self._send_verification_email(request.email, verification_token)
            
            logger.info(f"User registered successfully: {user['id']}")
            
            return {
                "user_id": user["id"],
                "message": "Registration successful. Please check your email to verify your account.",
                "email_verification_sent": True
            }
            
        except (ConflictError, ValidationError) as e:
            raise e
        except Exception as e:
            logger.error(f"Registration failed: {e}")
            raise AuthenticationError("Registration failed")
    
    async def login(self, request: LoginRequest) -> Dict[str, Any]:
        """Authenticate user and return tokens"""
        try:
            # Get user by email
            user = await self._get_user_by_email(request.email)
            if not user:
                raise AuthenticationError("Invalid email or password")
            
            # Verify password
            if not verify_password(request.password, user["password_hash"]):
                raise AuthenticationError("Invalid email or password")
            
            # Check if user is active
            if not user.get("is_active", True):
                raise AuthenticationError("Account is disabled")
            
            # Update last login (use admin for this system operation)
            await self.users_repo.update(
                user["id"],
                {"last_login": datetime.utcnow().isoformat()},
                use_admin=True
            )
            
            # Create Supabase-compatible auth session
            auth_session = supabase_auth_manager.create_auth_session(user)

            logger.info(f"User logged in successfully: {user['id']}")

            return auth_session
            
        except AuthenticationError as e:
            raise e
        except Exception as e:
            logger.error(f"Login failed: {e}")
            raise AuthenticationError("Login failed")
    
    async def refresh_token(self, request: RefreshTokenRequest) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        try:
            # Verify refresh token
            payload = verify_token(request.refresh_token, "refresh")
            
            # Check if token is blacklisted
            if token_blacklist.is_blacklisted(request.refresh_token):
                raise AuthenticationError("Token has been revoked")
            
            # Get user
            user_id = payload["sub"]
            user = await self.users_repo.get_by_id(user_id, use_admin=True)
            if not user or not user.get("is_active", True):
                raise AuthenticationError("User not found or inactive")
            
            # Create new access token using Supabase auth manager
            new_tokens = supabase_auth_manager.refresh_access_token(request.refresh_token, user)

            # Blacklist old refresh token
            token_blacklist.add(request.refresh_token)

            logger.debug(f"Token refreshed for user: {user_id}")

            return new_tokens
            
        except AuthenticationError as e:
            raise e
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise AuthenticationError("Token refresh failed")
    
    async def logout(self, access_token: str, refresh_token: Optional[str] = None) -> Dict[str, Any]:
        """Logout user and blacklist tokens"""
        try:
            # Add tokens to blacklist
            token_blacklist.add_token(access_token)
            if refresh_token:
                token_blacklist.add_token(refresh_token)
            
            logger.debug("User logged out successfully")
            
            return {"message": "Logged out successfully"}
            
        except Exception as e:
            logger.error(f"Logout failed: {e}")
            raise AuthenticationError("Logout failed")
    
    async def request_password_reset(self, request: PasswordResetRequest) -> Dict[str, Any]:
        """Request password reset"""
        try:
            # Get user by email
            user = await self._get_user_by_email(request.email)
            if not user:
                # Don't reveal if email exists or not
                return {"message": "If the email exists, a password reset link has been sent."}
            
            # Generate reset token
            reset_token = generate_password_reset_token()
            
            # Store reset token (in production, use Redis with expiration)
            await self._store_password_reset_token(user["id"], reset_token)
            
            # Send reset email
            await self._send_password_reset_email(request.email, reset_token)
            
            logger.info(f"Password reset requested for user: {user['id']}")
            
            return {"message": "If the email exists, a password reset link has been sent."}
            
        except Exception as e:
            logger.error(f"Password reset request failed: {e}")
            return {"message": "If the email exists, a password reset link has been sent."}
    
    async def _get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        try:
            response = supabase_service.admin_client.table("users").select("*").eq("email", email).execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            return None
    
    async def _store_verification_token(self, user_id: str, token: str) -> None:
        """Store email verification token (implement with Redis in production)"""
        # TODO: Implement with Redis and expiration
        pass
    
    async def _store_password_reset_token(self, user_id: str, token: str) -> None:
        """Store password reset token (implement with Redis in production)"""
        # TODO: Implement with Redis and expiration
        pass
    
    async def _send_verification_email(self, email: str, token: str) -> None:
        """Send email verification email (implement email service)"""
        # TODO: Implement email service
        email_content = create_email_verification_email(email, token)
        logger.info(f"Email verification email would be sent to: {email}")
    
    async def _send_password_reset_email(self, email: str, token: str) -> None:
        """Send password reset email (implement email service)"""
        # TODO: Implement email service
        email_content = create_password_reset_email(email, token)
        logger.info(f"Password reset email would be sent to: {email}")


# Global service instance
auth_service = AuthService()
