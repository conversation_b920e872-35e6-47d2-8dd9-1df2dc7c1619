#!/usr/bin/env python3
"""
Simple test script for the goal workflow without authentication
Tests the goal understanding agent directly
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent, LeadSourcingInput

class SimpleGoalWorkflowTester:
    def __init__(self):
        # Use demo organization ID
        self.organization_id = "00000000-0000-4000-8000-000000000001"
    
    async def test_goal_understanding_complete(self):
        """Test goal understanding with complete information"""
        print("\n🧪 Testing goal understanding with complete goal...")
        
        try:
            goal_agent = GoalUnderstandingAgent(self.organization_id)
            
            goal_input = {
                "goal_description": "I need 5 restaurant clients in London for our POS system",
                "context": "We provide modern point-of-sale systems for restaurants",
                "user_id": "test-user-001",
                "organization_id": self.organization_id,
                "conversation_history": [],
                "is_follow_up": False,
                "answered_questions": {}
            }
            
            result = await goal_agent.execute(goal_input)
            
            print(f"✅ Goal understanding result:")
            print(f"   Complete: {result.get('is_complete', False)}")
            print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
            
            parsed_goal = result.get('parsed_goal', {})
            target_audience = parsed_goal.get('target_audience', {})
            print(f"   Industry: {target_audience.get('industry', 'Not detected')}")
            print(f"   Location: {target_audience.get('geography', 'Not detected')}")
            print(f"   Target: {parsed_goal.get('metrics', {}).get('target_value', 'Not detected')}")
            
            if not result.get('is_complete', False):
                questions = result.get('clarification_questions', [])
                print(f"   Follow-up questions: {questions}")
            
            return result
            
        except Exception as e:
            print(f"❌ Goal understanding error: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_goal_understanding_incomplete(self):
        """Test goal understanding with incomplete information"""
        print("\n🧪 Testing goal understanding with incomplete goal...")
        
        try:
            goal_agent = GoalUnderstandingAgent(self.organization_id)
            
            goal_input = {
                "goal_description": "I want 3 new clients for my business",
                "context": "We need to grow our customer base",
                "user_id": "test-user-001",
                "organization_id": self.organization_id,
                "conversation_history": [],
                "is_follow_up": False,
                "answered_questions": {}
            }
            
            result = await goal_agent.execute(goal_input)
            
            print(f"✅ Goal understanding result:")
            print(f"   Complete: {result.get('is_complete', False)}")
            print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
            
            if not result.get('is_complete', False):
                questions = result.get('clarification_questions', [])
                print(f"   Follow-up questions: {questions}")
                
                # Test follow-up with answers
                return await self.test_follow_up_answers(goal_input, questions)
            
            return result
            
        except Exception as e:
            print(f"❌ Goal understanding error: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_follow_up_answers(self, original_input, questions):
        """Test answering follow-up questions"""
        print("\n🧪 Testing follow-up answers...")
        
        try:
            goal_agent = GoalUnderstandingAgent(self.organization_id)
            
            # Simulate answering follow-up questions
            answers = {
                "industry": "restaurants",
                "location": "London, UK"
            }
            
            print(f"   Providing answers: {answers}")
            
            # Update input with answers
            followup_input = original_input.copy()
            followup_input["is_follow_up"] = True
            followup_input["answered_questions"] = answers
            
            result = await goal_agent.execute(followup_input)
            
            print(f"✅ Follow-up result:")
            print(f"   Complete: {result.get('is_complete', False)}")
            print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
            
            parsed_goal = result.get('parsed_goal', {})
            target_audience = parsed_goal.get('target_audience', {})
            print(f"   Final Industry: {target_audience.get('industry', 'Not detected')}")
            print(f"   Final Location: {target_audience.get('geography', 'Not detected')}")
            
            return result
            
        except Exception as e:
            print(f"❌ Follow-up error: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_lead_sourcing_execution(self, parsed_goal):
        """Test actual lead sourcing execution with Apollo.io"""
        print("\n🧪 Testing Apollo.io lead sourcing execution...")

        try:
            target_audience = parsed_goal.get('target_audience', {})
            metrics = parsed_goal.get('metrics', {})

            # Prepare lead sourcing input
            target_count = metrics.get('target_value', 5)
            lead_count_target = target_count * 1  # 10 leads per client

            sourcing_input = LeadSourcingInput(
                campaign_id="test-campaign-001",
                target_audience=target_audience,
                industry=target_audience.get('industry', ''),
                location=target_audience.get('geography', ''),
                company_size="any",
                lead_count_target=lead_count_target,
                quality_threshold=50,
                sources=["apollo"]
            )

            print(f"✅ Lead sourcing targeting:")
            print(f"   Industry: {sourcing_input.industry}")
            print(f"   Location: {sourcing_input.location}")
            print(f"   Target leads: {sourcing_input.lead_count_target}")
            print(f"   Quality threshold: {sourcing_input.quality_threshold}")
            print(f"   Sources: {sourcing_input.sources}")

            # Execute actual Apollo.io lead sourcing
            print("\n🚀 Executing Apollo.io lead sourcing...")
            lead_agent = LeadSourcingAgent(self.organization_id)

            result = await lead_agent.execute(sourcing_input.model_dump())

            print(f"✅ Apollo.io lead sourcing completed!")
            print(f"   Leads sourced: {result.get('leads_sourced', 0)}")
            print(f"   Duplicates filtered: {result.get('duplicates_found', 0)}")
            print(f"   Cooldown filtered: {result.get('cooldown_filtered', 0)}")

            # Show breakdown by source
            leads_by_source = result.get('leads_by_source', {})
            for source, count in leads_by_source.items():
                print(f"   {source.title()}: {count} leads")

            # Show quality distribution
            quality_dist = result.get('quality_distribution', {})
            if quality_dist:
                print(f"   Quality distribution:")
                for quality, count in quality_dist.items():
                    print(f"     {quality.title()}: {count} leads")

            return result

        except Exception as e:
            print(f"❌ Lead sourcing execution error: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def run_all_tests(self):
        """Run all goal workflow tests"""
        print("🚀 Starting Simple Goal Workflow Tests")
        print("=" * 50)
        
        # Test 1: Complete goal
        complete_result = await self.test_goal_understanding_complete()
        
        # Test 2: Incomplete goal with follow-up
        incomplete_result = await self.test_goal_understanding_incomplete()
        
        # Test 3: Apollo.io lead sourcing execution
        lead_sourcing_result = None
        if complete_result and complete_result.get('is_complete'):
            lead_sourcing_result = await self.test_lead_sourcing_execution(complete_result.get('parsed_goal', {}))
        elif incomplete_result and incomplete_result.get('is_complete'):
            lead_sourcing_result = await self.test_lead_sourcing_execution(incomplete_result.get('parsed_goal', {}))
        
        print("\n📊 Test Summary:")
        print(f"   Complete Goal Test: {'✅ Passed' if complete_result else '❌ Failed'}")
        print(f"   Incomplete Goal Test: {'✅ Passed' if incomplete_result else '❌ Failed'}")
        print(f"   Apollo.io Lead Sourcing: {'✅ Passed' if lead_sourcing_result else '❌ Failed'}")

        if lead_sourcing_result:
            leads_count = lead_sourcing_result.get('leads_sourced', 0)
            print(f"   Total Leads Sourced: {leads_count}")

        print("\n✅ All goal workflow tests completed!")
        return True


async def main():
    """Main test function"""
    tester = SimpleGoalWorkflowTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
