#!/usr/bin/env python3
"""
Demo chat interface that bypasses database for testing
"""

import sys
import asyncio
import uuid
import json

# Add the app directory to the path
sys.path.append('.')

class DemoGoalUnderstandingAgent:
    """Demo version of goal understanding agent that doesn't use database"""
    
    def __init__(self, organization_id: str):
        self.organization_id = organization_id
        self.name = "Goal Understanding Specialist (Demo)"
    
    async def execute(self, input_data):
        """Execute goal understanding without database"""
        try:
            # Handle simple string input
            if isinstance(input_data, str):
                goal_description = input_data
            else:
                goal_description = input_data.get("goal_description", "")
            
            print(f"🧠 Analyzing goal: '{goal_description}'")
            
            # Simple goal parsing (without AI for demo)
            parsed_goal = self._parse_goal_simple(goal_description)
            
            # Calculate confidence
            confidence_score = 0.8 if len(goal_description) > 10 else 0.5
            
            # Create output
            result = {
                "parsed_goal": parsed_goal,
                "clarification_questions": [],
                "suggested_metrics": ["Number of clients acquired", "Revenue generated"],
                "confidence_score": confidence_score,
                "next_steps": ["Proceed to strategic planning", "Begin lead generation"],
                "is_complete": True,
                "missing_information": [],
                "next_agent_input": {
                    "goal": parsed_goal,
                    "target_audience": parsed_goal.get("target_audience", {}),
                    "organization_id": self.organization_id
                }
            }
            
            return result
            
        except Exception as e:
            print(f"❌ Error in goal understanding: {e}")
            raise
    
    def _parse_goal_simple(self, goal_description):
        """Simple goal parsing without AI"""
        # Extract basic information from goal
        goal_lower = goal_description.lower()
        
        # Detect numbers
        import re
        numbers = re.findall(r'\d+', goal_description)
        target_value = numbers[0] if numbers else "5"
        
        # Detect industry
        industry = "Restaurant" if "restaurant" in goal_lower else "General Business"
        
        # Detect location
        geography = "London" if "london" in goal_lower else "Not specified"
        if "uk" in goal_lower or "united kingdom" in goal_lower:
            geography = "United Kingdom"
        
        return {
            "objective": f"Acquire {target_value} new clients",
            "target_audience": {
                "industry": industry,
                "company_size": "Small to Medium",
                "job_titles": ["Owner", "Manager", "Decision Maker"],
                "geography": geography
            },
            "metrics": {
                "primary_metric": "Number of clients",
                "target_value": target_value,
                "secondary_metrics": ["Revenue", "Conversion rate"]
            },
            "timeline": {
                "duration": "3 months",
                "start_date": "Immediate",
                "milestones": ["Lead generation", "First contact", "Client acquisition"]
            },
            "budget": {
                "total_budget": "Not specified",
                "budget_allocation": "Lead generation and outreach"
            },
            "constraints": [],
            "success_criteria": [f"Acquire {target_value} new clients", "Positive ROI"],
            "missing_information": []
        }


async def demo_chat():
    """Demo chat function"""
    print("🤖 Demo Selda AI Sales Autopilot!")
    print("=" * 50)
    print("This is a demo version that works without database dependencies.")
    print("Just tell me your sales goal in natural language.")
    print("Type 'quit' to exit.\n")
    
    # Generate demo IDs
    org_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    
    print(f"🔧 Demo Organization ID: {org_id}")
    print(f"🔧 Demo User ID: {user_id}\n")
    
    while True:
        try:
            # Get user input
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("\n👋 Goodbye! Thanks for using Selda AI Demo!")
                break
            
            if not user_input:
                print("Please enter your sales goal.")
                continue
            
            # Process the goal
            print(f"\n🤖 Processing your goal: '{user_input}'")
            print("=" * 50)
            
            # Create demo agent
            agent = DemoGoalUnderstandingAgent(org_id)
            
            # Execute goal understanding
            print("📋 Step 1: Understanding your goal...")
            result = await agent.execute(user_input)
            
            print(f"✅ Goal understood with {result['confidence_score']:.1%} confidence")
            
            # Display parsed goal
            parsed_goal = result['parsed_goal']
            print(f"\n📊 Parsed Goal:")
            print(f"   Objective: {parsed_goal.get('objective', 'Not specified')}")
            
            if parsed_goal.get('target_audience'):
                ta = parsed_goal['target_audience']
                print(f"   Target Industry: {ta.get('industry', 'Not specified')}")
                print(f"   Geography: {ta.get('geography', 'Not specified')}")
                print(f"   Target Value: {parsed_goal.get('metrics', {}).get('target_value', 'Not specified')}")
            
            print(f"\n🎯 Next Steps:")
            for step in result.get('next_steps', []):
                print(f"   • {step}")
            
            print(f"\n📈 Suggested Metrics:")
            for metric in result.get('suggested_metrics', []):
                print(f"   • {metric}")
            
            print(f"\n✅ Goal processing complete!")
            print(f"💡 In the full version, this would now:")
            print(f"   • Create a strategic plan")
            print(f"   • Source leads from Apollo.io")
            print(f"   • Store data in Supabase")
            print(f"   • Run the complete agent workflow")
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Thanks for using Selda AI Demo!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again.\n")


async def main():
    """Main function"""
    await demo_chat()


if __name__ == "__main__":
    asyncio.run(main())
