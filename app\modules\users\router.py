"""
User router
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import logging
from typing import Optional

from app.modules.users.models import (
    UserProfileResponse,
    UserProfileUpdate,
    UserProfileUpdateResponse,
    UsersListResponse,
    UserSearchParams,
    InviteUserRequest,
    InviteUserResponse,
    ChangeUserRoleRequest,
    UserRoleChangeResponse,
    ChangeUserStatusRequest,
    UserStatusChangeResponse,
    UserStatsResponse,
    BulkUserAction,
    BulkUserActionResponse,
    UserExportRequest,
    UserExportResponse
)
from app.modules.users.service import user_service
from app.shared.models import BaseResponse, PaginationParams, UserRole
from app.shared.dependencies import (
    get_current_active_user,
    get_current_organization,
    require_role
)
from app.core.exceptions import (
    NotFoundError,
    ConflictError,
    ValidationError,
    AuthorizationError
)

logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Security
security = HTTPBearer()

# Router
router = APIRouter()


@router.get("/me", response_model=UserProfileResponse)
async def get_my_profile(
    current_user: dict = Depends(get_current_active_user)
):
    """Get current user's profile"""
    try:
        return await user_service.get_user_profile(current_user['id'])
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.put("/me", response_model=UserProfileUpdateResponse)
@limiter.limit("10/minute")
async def update_my_profile(
    request: Request,
    profile_data: UserProfileUpdate,
    current_user: dict = Depends(get_current_active_user)
):
    """Update current user's profile"""
    try:
        updated_user = await user_service.update_user_profile(
            user_id=current_user['id'],
            update_data=profile_data,
            current_user_id=current_user['id']
        )
        return UserProfileUpdateResponse(
            success=True,
            message="Profile updated successfully",
            user=updated_user
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )


@router.get("/stats", response_model=UserStatsResponse)
async def get_my_stats(
    current_user: dict = Depends(get_current_active_user)
):
    """Get current user's statistics"""
    try:
        return await user_service.get_user_stats(current_user['id'])
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user statistics"
        )


@router.get("/organization/members", response_model=UsersListResponse)
async def get_organization_members(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    query: Optional[str] = Query(None, description="Search by name or email"),
    role: Optional[UserRole] = Query(None, description="Filter by role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get organization members"""
    try:
        pagination = PaginationParams(page=page, limit=limit)
        search_params = UserSearchParams(
            query=query,
            role=role,
            is_active=is_active
        )
        
        result = await user_service.get_organization_members(
            organization_id=organization['id'],
            pagination=pagination,
            search_params=search_params
        )
        
        return UsersListResponse(**result)
        
    except Exception as e:
        logger.error(f"Error getting organization members: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get organization members"
        )


@router.post("/organization/invite", response_model=InviteUserResponse)
@limiter.limit("10/hour")
async def invite_user_to_organization(
    request: Request,
    invite_data: InviteUserRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    _: dict = Depends(require_role("admin"))  # Only admins can invite users
):
    """Invite user to organization"""
    try:
        result = await user_service.invite_user_to_organization(
            organization_id=organization['id'],
            invite_data=invite_data,
            invited_by=current_user['id']
        )
        
        return InviteUserResponse(
            success=True,
            message=result['message'],
            invitation_id=result.get('invitation_id'),
            expires_at=result.get('expires_at')
        )
        
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error inviting user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to invite user"
        )


@router.get("/{user_id}", response_model=UserProfileResponse)
async def get_user_profile(
    user_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get user profile by ID (organization members only)"""
    try:
        # Check if the requested user is in the same organization
        user_profile = await user_service.get_user_profile(user_id)
        
        if user_profile.organization_id != organization['id']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: User not in your organization"
            )
        
        return user_profile
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting user profile {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.put("/{user_id}/role", response_model=UserRoleChangeResponse)
async def change_user_role(
    user_id: str,
    role_data: ChangeUserRoleRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    _: dict = Depends(require_role("admin"))  # Only admins can change roles
):
    """Change user role"""
    try:
        result = await user_service.update_user_role(
            user_id=user_id,
            role_data=role_data,
            updated_by=current_user['id']
        )
        
        return UserRoleChangeResponse(
            success=True,
            message=result['message'],
            user_id=result['user_id'],
            new_role=UserRole(result['new_role'])
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AuthorizationError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error changing user role {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change user role"
        )


@router.put("/{user_id}/status", response_model=UserStatusChangeResponse)
async def change_user_status(
    user_id: str,
    status_data: ChangeUserStatusRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    _: dict = Depends(require_role("admin"))  # Only admins can change status
):
    """Change user active status"""
    try:
        result = await user_service.update_user_status(
            user_id=user_id,
            status_data=status_data,
            updated_by=current_user['id']
        )
        
        return UserStatusChangeResponse(
            success=True,
            message=result['message'],
            user_id=result['user_id'],
            is_active=result['is_active']
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AuthorizationError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error changing user status {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change user status"
        )


@router.get("/{user_id}/stats", response_model=UserStatsResponse)
async def get_user_stats(
    user_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get user statistics"""
    try:
        # Check permissions - users can only see their own stats unless admin
        if user_id != current_user['id'] and current_user.get('role') not in ['admin', 'owner']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: Insufficient permissions"
            )
        
        return await user_service.get_user_stats(user_id)
        
    except Exception as e:
        logger.error(f"Error getting user stats {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user statistics"
        )


# Note: Rate limit exception handler is added to the main app, not the router
