#!/usr/bin/env python3
"""
Quick API test for the goal workflow endpoint
"""

import asyncio
import httpx
import json

BASE_URL = "http://localhost:8000"

async def test_goal_workflow_api():
    """Test the goal workflow API endpoint"""
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        
        # Test 1: Register a test user
        print("🧪 Testing user registration...")
        register_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",  # Strong password required
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        try:
            register_response = await client.post("/api/v1/auth/register", json=register_data)
            print(f"   Registration: {register_response.status_code}")
        except Exception as e:
            print(f"   Registration error: {e}")
        
        # Test 2: Login
        print("\n🧪 Testing user login...")
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        try:
            login_response = await client.post("/api/v1/auth/login", json=login_data)
            if login_response.status_code == 200:
                token = login_response.json()["access_token"]
                print(f"   ✅ Login successful")
                
                # Test 3: Goal workflow
                print("\n🧪 Testing goal workflow endpoint...")
                headers = {"Authorization": f"Bearer {token}"}
                
                goal_data = {
                    "sales_goal": "I need 3 restaurant clients in London for our POS system",
                    "context": "We provide modern point-of-sale systems"
                }
                
                workflow_response = await client.post(
                    "/api/v1/workflow/start-from-goal",
                    json=goal_data,
                    headers=headers
                )
                
                if workflow_response.status_code == 200:
                    result = workflow_response.json()
                    print(f"   ✅ Workflow started successfully!")
                    print(f"   Complete: {result.get('is_complete', False)}")
                    print(f"   Message: {result.get('message', 'No message')}")
                    
                    if result.get('workflow_id'):
                        print(f"   Workflow ID: {result['workflow_id']}")
                    
                    if result.get('follow_up_questions'):
                        print(f"   Follow-up questions: {result['follow_up_questions']}")
                    
                    return True
                else:
                    print(f"   ❌ Workflow failed: {workflow_response.status_code}")
                    print(f"   Response: {workflow_response.text}")
                    return False
            else:
                print(f"   ❌ Login failed: {login_response.status_code}")
                print(f"   Response: {login_response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ API test error: {e}")
            return False

async def main():
    print("🚀 Quick API Test for Goal Workflow")
    print("=" * 40)
    
    success = await test_goal_workflow_api()
    
    if success:
        print("\n✅ API test completed successfully!")
    else:
        print("\n❌ API test failed!")

if __name__ == "__main__":
    asyncio.run(main())
