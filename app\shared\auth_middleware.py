"""
Authentication middleware for automatic RLS context setting
"""

from typing import Callable
from fastapi import Request, Response
from fastapi.security.utils import get_authorization_scheme_param
from starlette.middleware.base import BaseHTTPMiddleware
from jose import jwt, JWTError
import logging

from app.core.config import settings
from app.core.database import supabase_service

logger = logging.getLogger(__name__)


class AuthContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware that automatically sets Supabase auth context for authenticated requests
    
    This middleware:
    1. Extracts JWT tokens from Authorization headers
    2. Validates the tokens
    3. Sets the Supabase auth context for RLS to work properly
    4. Clears the context after the request
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = {
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/health",
            "/health/detailed",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/api/v1/auth/password-reset",
            "/api/v1/auth/verify-email"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and set auth context if needed"""
        
        # Skip auth context for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        # Skip auth context for non-authenticated endpoints
        if request.method == "OPTIONS":
            return await call_next(request)
        
        # Extract authorization header
        authorization = request.headers.get("Authorization")
        if not authorization:
            # No auth header, proceed without setting context
            return await call_next(request)
        
        # Parse the authorization header
        scheme, token = get_authorization_scheme_param(authorization)
        if scheme.lower() != "bearer" or not token:
            # Invalid auth header format, proceed without setting context
            return await call_next(request)
        
        try:
            # Verify the token is valid and Supabase-compatible
            payload = jwt.decode(
                token,
                settings.jwt_secret_key,
                algorithms=["HS256"]
            )
            
            # Check if it's a Supabase-compatible token
            if (payload.get("aud") == "authenticated" and 
                payload.get("role") == "authenticated" and
                payload.get("iss") == "supabase"):
                
                # Set the auth context for the regular client
                supabase_service.set_auth(token)
                
                logger.debug(f"Set auth context for user: {payload.get('sub')}")
                
                try:
                    # Process the request with auth context
                    response = await call_next(request)
                    return response
                finally:
                    # Always clear auth context after request
                    try:
                        supabase_service.clear_auth()
                        logger.debug("Cleared auth context")
                    except Exception as e:
                        logger.warning(f"Failed to clear auth context: {e}")
            else:
                # Not a Supabase-compatible token, proceed without setting context
                logger.debug("Token is not Supabase-compatible, skipping auth context")
                return await call_next(request)
                
        except JWTError as e:
            # Invalid token, proceed without setting context
            logger.debug(f"Invalid JWT token: {e}")
            return await call_next(request)
        except Exception as e:
            # Other errors, proceed without setting context
            logger.warning(f"Error processing auth context: {e}")
            return await call_next(request)


class RLSEnforcementMiddleware(BaseHTTPMiddleware):
    """
    Middleware that enforces RLS by ensuring authenticated endpoints have proper context
    
    This middleware validates that protected endpoints have proper auth context set.
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.protected_prefixes = [
            "/api/v1/users",
            "/api/v1/organizations", 
            "/api/v1/leads",
            "/api/v1/campaigns",
            "/api/v1/agents",
            "/api/v1/analytics"
        ]
        self.excluded_paths = {
            "/docs",
            "/redoc",
            "/openapi.json", 
            "/health",
            "/health/detailed",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/api/v1/auth/password-reset",
            "/api/v1/auth/verify-email"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Validate RLS context for protected endpoints"""
        
        # Skip validation for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        # Skip validation for non-protected paths
        is_protected = any(
            request.url.path.startswith(prefix) 
            for prefix in self.protected_prefixes
        )
        
        if not is_protected:
            return await call_next(request)
        
        # For protected paths, ensure we have authorization header
        authorization = request.headers.get("Authorization")
        if not authorization:
            from fastapi import HTTPException, status
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required for this endpoint",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Parse and validate the token
        scheme, token = get_authorization_scheme_param(authorization)
        if scheme.lower() != "bearer" or not token:
            from fastapi import HTTPException, status
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication scheme",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        try:
            # Verify token format
            payload = jwt.decode(
                token,
                settings.jwt_secret_key,
                algorithms=["HS256"]
            )
            
            # Ensure it's a proper Supabase token for RLS
            if (payload.get("aud") != "authenticated" or 
                payload.get("role") != "authenticated" or
                payload.get("iss") != "supabase"):
                
                from fastapi import HTTPException, status
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token format for RLS operations",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            # Token is valid, proceed with request
            return await call_next(request)
            
        except JWTError as e:
            from fastapi import HTTPException, status
            logger.warning(f"JWT validation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        except Exception as e:
            logger.error(f"Unexpected error in RLS enforcement: {e}")
            from fastapi import HTTPException, status
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication validation error"
            )
