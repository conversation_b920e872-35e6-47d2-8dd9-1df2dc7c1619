"""
Chat Interface Router for Selda AI Sales Autopilot
Handles natural language goal input and starts complete workflows
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field

from app.modules.agents.service import agent_service
from app.modules.agents.models import AgentType
from app.modules.agents.simple_workflow import simple_workflow_manager
from app.shared.dependencies import (
    get_current_active_user,
    get_current_organization
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/chat", tags=["Chat Interface"])


class ChatMessage(BaseModel):
    """Chat message model"""
    role: str = Field(..., description="Message role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: Optional[str] = Field(None, description="Message timestamp")


class GoalInputRequest(BaseModel):
    """Request for initial goal input"""
    goal_description: str = Field(..., min_length=10, max_length=2000, description="Natural language sales goal")
    context: Optional[str] = Field(None, description="Additional business context")


class FollowUpRequest(BaseModel):
    """Request for follow-up responses"""
    conversation_id: str = Field(..., description="Conversation ID")
    user_response: str = Field(..., description="User's response to questions")
    answered_questions: Dict[str, str] = Field(default_factory=dict, description="Questions and answers")


class ChatResponse(BaseModel):
    """Chat response model"""
    conversation_id: str = Field(..., description="Unique conversation ID")
    agent_response: str = Field(..., description="Agent's response message")
    questions: List[str] = Field(default_factory=list, description="Follow-up questions")
    is_complete: bool = Field(default=False, description="Whether goal understanding is complete")
    confidence_score: float = Field(default=0.0, description="Confidence in understanding")
    next_action: Optional[str] = Field(None, description="Next action to take")
    parsed_goal: Optional[Dict[str, Any]] = Field(None, description="Extracted goal information")


class WorkflowStartResponse(BaseModel):
    """Response when workflow is ready to start"""
    conversation_id: str
    workflow_ready: bool = True
    parsed_goal: Dict[str, Any]
    next_agent_input: Dict[str, Any]
    message: str = "Goal understanding complete! Ready to start your sales campaign."


class SimpleGoalRequest(BaseModel):
    """Simple goal input request"""
    goal: str = Field(..., min_length=10, max_length=2000, description="Natural language sales goal")


class SimpleWorkflowResponse(BaseModel):
    """Response for simple workflow start"""
    success: bool
    workflow_id: Optional[str] = None
    campaign_id: Optional[str] = None
    message: str
    goal_understanding: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/goal/start", response_model=ChatResponse)
async def start_goal_conversation(
    request: GoalInputRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Start a new goal understanding conversation"""
    try:
        # Generate conversation ID
        import uuid
        conversation_id = f"conv_{uuid.uuid4().hex[:8]}"
        
        # Prepare input for Goal Understanding Agent
        agent_input = {
            "goal_description": request.goal_description,
            "context": request.context,
            "user_id": current_user["id"],
            "organization_id": organization["id"],
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        # Execute Goal Understanding Agent
        from app.modules.agents.models import CreateAgentTaskRequest, TaskPriority

        task_request = CreateAgentTaskRequest(
            agent_type=AgentType.GOAL_UNDERSTANDING,
            task_type="goal_understanding",
            priority=TaskPriority.HIGH,
            input_data=agent_input
        )

        task = await agent_service.create_agent_task(
            organization_id=organization["id"],
            request=task_request
        )

        # Get the result from the task
        result = task.output_data
        
        # Generate agent response message
        if result.get("clarification_questions"):
            agent_response = f"I understand you want to: {request.goal_description}\n\nTo help you achieve this goal, I need a bit more information:"
        else:
            agent_response = f"Perfect! I understand your goal: {request.goal_description}\n\nI have all the information I need to proceed."
        
        return ChatResponse(
            conversation_id=conversation_id,
            agent_response=agent_response,
            questions=result.get("clarification_questions", []),
            is_complete=result.get("is_complete", False),
            confidence_score=result.get("confidence_score", 0.0),
            next_action="answer_questions" if result.get("clarification_questions") else "start_workflow",
            parsed_goal=result.get("parsed_goal") if result.get("is_complete") else None
        )
        
    except Exception as e:
        logger.error(f"Failed to start goal conversation: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process your goal: {str(e)}"
        )


@router.post("/goal/followup", response_model=ChatResponse)
async def handle_followup_response(
    request: FollowUpRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Handle follow-up responses to clarification questions"""
    try:
        # Prepare input for Goal Understanding Agent with follow-up
        agent_input = {
            "goal_description": request.user_response,
            "user_id": current_user["id"],
            "organization_id": organization["id"],
            "is_follow_up": True,
            "answered_questions": request.answered_questions
        }
        
        # Execute Goal Understanding Agent
        result = await agent_service.execute_agent(
            organization_id=organization["id"],
            agent_type=AgentType.GOAL_UNDERSTANDING,
            input_data=agent_input
        )
        
        # Generate appropriate response
        if result.get("is_complete"):
            agent_response = "Excellent! I now have all the information I need. Your sales campaign is ready to be created."
        elif result.get("clarification_questions"):
            agent_response = "Thank you for that information! I have a few more questions to ensure I create the perfect campaign for you:"
        else:
            agent_response = "Thank you for the additional information. Let me process this and see what else I need to know."
        
        return ChatResponse(
            conversation_id=request.conversation_id,
            agent_response=agent_response,
            questions=result.get("clarification_questions", []),
            is_complete=result.get("is_complete", False),
            confidence_score=result.get("confidence_score", 0.0),
            next_action="start_workflow" if result.get("is_complete") else "answer_questions",
            parsed_goal=result.get("parsed_goal") if result.get("is_complete") else None
        )
        
    except Exception as e:
        logger.error(f"Failed to handle follow-up: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process your response. Please try again."
        )


@router.post("/goal/complete", response_model=WorkflowStartResponse)
async def complete_goal_understanding(
    conversation_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Complete goal understanding and prepare for workflow start"""
    try:
        # In a real implementation, you would retrieve the conversation state
        # For now, we'll return a sample response
        
        # This would typically retrieve the final parsed goal from the conversation
        parsed_goal = {
            "objective": "Generate qualified leads",
            "target_audience": {
                "industry": "Technology",
                "location": "London",
                "company_size": "50-200"
            },
            "timeline": "3 months",
            "budget": "$5000",
            "success_criteria": "10 qualified meetings"
        }
        
        next_agent_input = {
            "goal": parsed_goal,
            "target_audience": parsed_goal["target_audience"],
            "timeline_constraints": {"duration": parsed_goal["timeline"]},
            "budget_constraints": {"total_budget": parsed_goal["budget"]},
            "organization_id": organization["id"],
            "user_id": current_user["id"]
        }
        
        return WorkflowStartResponse(
            conversation_id=conversation_id,
            parsed_goal=parsed_goal,
            next_agent_input=next_agent_input
        )
        
    except Exception as e:
        logger.error(f"Failed to complete goal understanding: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete goal understanding"
        )


@router.post("/simple/start", response_model=SimpleWorkflowResponse)
async def start_simple_workflow(
    request: SimpleGoalRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Start a complete workflow from just a goal string"""
    try:
        # Set organization ID for the workflow manager
        simple_workflow_manager.organization_id = organization["id"]

        # Start workflow from goal
        result = await simple_workflow_manager.start_from_goal(
            goal=request.goal,
            user_id=current_user["id"]
        )

        if result["success"]:
            return SimpleWorkflowResponse(
                success=True,
                workflow_id=result.get("workflow", {}).get("workflow_id"),
                campaign_id=result.get("workflow", {}).get("campaign_id"),
                message="Workflow started successfully! Your sales campaign is now running.",
                goal_understanding=result.get("goal_understanding")
            )
        else:
            return SimpleWorkflowResponse(
                success=False,
                message="Failed to start workflow",
                error=result.get("error")
            )

    except Exception as e:
        logger.error(f"Failed to start simple workflow: {e}")
        return SimpleWorkflowResponse(
            success=False,
            message="Failed to process your goal",
            error=str(e)
        )


@router.get("/workflow/{workflow_id}/status")
async def get_workflow_status(
    workflow_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get workflow status"""
    try:
        simple_workflow_manager.organization_id = organization["id"]
        status = await simple_workflow_manager.get_workflow_status(workflow_id)

        if status:
            return {"success": True, "status": status}
        else:
            return {"success": False, "message": "Workflow not found"}

    except Exception as e:
        logger.error(f"Failed to get workflow status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get workflow status"
        )


@router.get("/goal/examples", response_model=List[str])
async def get_goal_examples():
    """Get example goal descriptions for users"""
    examples = [
        "I want to get 10 new restaurant clients in London within 3 months",
        "Generate 50 qualified leads for our SaaS platform targeting tech companies",
        "Book 15 sales meetings with healthcare companies in the US",
        "Find potential customers for our marketing agency in Europe",
        "Get 20 demo bookings for our new software product",
        "Generate leads for our consulting services targeting startups",
        "Find enterprise clients for our cybersecurity solution",
        "Book meetings with e-commerce companies for our logistics service"
    ]

    return examples


@router.get("/conversation/{conversation_id}/status")
async def get_conversation_status(
    conversation_id: str,
    current_user: dict = Depends(get_current_active_user)
):
    """Get the current status of a conversation"""
    # In a real implementation, this would retrieve conversation state from database
    return {
        "conversation_id": conversation_id,
        "status": "active",
        "messages_count": 3,
        "is_complete": False,
        "last_activity": "2024-01-15T10:30:00Z"
    }
