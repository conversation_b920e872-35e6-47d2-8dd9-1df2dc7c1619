"""
Agent service layer
"""

import logging
from typing import Optional, List, Dict, Any, Type
from datetime import datetime
from uuid import uuid4

from app.core.database import SupabaseService, supabase_service, BaseRepository
from app.core.exceptions import (
    NotFoundError,
    ConflictError,
    ValidationError,
    ExternalServiceError
)
from app.modules.agents.models import (
    AgentType,
    AgentStatus,
    TaskStatus,
    AgentTask,
    AgentExecution,
    AgentConfiguration,
    AgentInfo,
    AgentMetrics,
    CreateAgentTaskRequest,
    AgentConfigurationUpdate
)
from app.modules.agents.base import SeldaAgent, CrewManager, agent_registry

logger = logging.getLogger(__name__)


class AgentService:
    """Service for managing AI agents"""
    
    def __init__(self, db: SupabaseService = None):
        self.db = db or supabase_service
        self.crew_managers: Dict[str, CrewManager] = {}
        self.agent_instances: Dict[str, Dict[AgentType, SeldaAgent]] = {}
        
        # Initialize repositories
        self.tasks_repo = BaseRepository("agent_tasks", self.db)
        self.configs_repo = BaseRepository("agent_configurations", self.db)
        self.executions_repo = BaseRepository("agent_executions", self.db)
    
    def get_crew_manager(self, organization_id: str) -> CrewManager:
        """Get or create crew manager for organization"""
        if organization_id not in self.crew_managers:
            self.crew_managers[organization_id] = CrewManager(organization_id, self.db)
        return self.crew_managers[organization_id]
    
    def get_agent_instance(self, organization_id: str, agent_type: AgentType) -> SeldaAgent:
        """Get or create agent instance for organization"""
        if organization_id not in self.agent_instances:
            self.agent_instances[organization_id] = {}
        
        if agent_type not in self.agent_instances[organization_id]:
            # Create agent instance
            agent_class = agent_registry.get(agent_type)
            if not agent_class:
                raise NotFoundError(f"Agent type {agent_type} not found in registry")
            
            agent_instance = agent_class(organization_id, self.db)
            self.agent_instances[organization_id][agent_type] = agent_instance
            
            # Register with crew manager
            crew_manager = self.get_crew_manager(organization_id)
            crew_manager.register_agent(agent_instance)
        
        return self.agent_instances[organization_id][agent_type]
    
    async def get_available_agents(self) -> List[AgentInfo]:
        """Get list of all available agents"""
        try:
            agents = []
            
            for agent_type, agent_class in agent_registry.items():
                # Create temporary instance to get info
                temp_agent = agent_class("temp", self.db)
                
                agent_info = AgentInfo(
                    agent_type=agent_type,
                    name=temp_agent.name,
                    description=temp_agent.description,
                    capabilities=[
                        {
                            "name": cap["name"],
                            "description": cap["description"],
                            "parameters": cap.get("parameters", {}),
                            "is_available": True
                        }
                        for cap in temp_agent.get_capabilities()
                    ],
                    status=AgentStatus.IDLE
                )
                
                agents.append(agent_info)
            
            return agents
            
        except Exception as e:
            logger.error(f"Error getting available agents: {e}")
            raise
    
    async def get_agent_info(
        self, 
        organization_id: str, 
        agent_type: AgentType
    ) -> AgentInfo:
        """Get detailed information about a specific agent"""
        try:
            agent_instance = self.get_agent_instance(organization_id, agent_type)
            
            # Get configuration
            configuration = await agent_instance.get_configuration()
            
            # Get metrics
            metrics = await self._get_agent_metrics(organization_id, agent_type)
            
            agent_info = AgentInfo(
                agent_type=agent_type,
                name=agent_instance.name,
                description=agent_instance.description,
                capabilities=[
                    {
                        "name": cap["name"],
                        "description": cap["description"],
                        "parameters": cap.get("parameters", {}),
                        "is_available": True
                    }
                    for cap in agent_instance.get_capabilities()
                ],
                status=agent_instance.status,
                configuration=configuration,
                metrics=metrics
            )
            
            return agent_info
            
        except Exception as e:
            logger.error(f"Error getting agent info for {agent_type}: {e}")
            raise
    
    async def create_agent_task(
        self,
        organization_id: str,
        request: CreateAgentTaskRequest
    ) -> AgentTask:
        """Create and execute an agent task"""
        try:
            # Get agent instance
            agent_instance = self.get_agent_instance(organization_id, request.agent_type)
            
            # Execute the agent
            result = await agent_instance.execute(request.input_data)
            
            # Get the created task from database
            tasks = await self.tasks_repo.list(
                filters={
                    'organization_id': organization_id,
                    'agent_type': request.agent_type.value
                },
                limit=1,
                order_by="created_at",
                ascending=False,
                use_admin=True
            )
            
            if not tasks:
                raise NotFoundError("Task not found after execution")
            
            return AgentTask(**tasks[0])
            
        except Exception as e:
            logger.error(f"Error creating agent task: {e}")
            raise
    
    async def get_agent_tasks(
        self,
        organization_id: str,
        agent_type: Optional[AgentType] = None,
        status: Optional[TaskStatus] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[AgentTask]:
        """Get agent tasks with optional filtering"""
        try:
            filters = {'organization_id': organization_id}
            
            if agent_type:
                filters['agent_type'] = agent_type.value
            
            if status:
                filters['status'] = status.value
            
            tasks_data = await self.tasks_repo.list(
                filters=filters,
                limit=limit,
                offset=offset,
                order_by="created_at",
                ascending=False,
                use_admin=True
            )
            
            return [AgentTask(**task) for task in tasks_data]
            
        except Exception as e:
            logger.error(f"Error getting agent tasks: {e}")
            raise
    
    async def get_agent_task(self, task_id: str) -> AgentTask:
        """Get specific agent task by ID"""
        try:
            task_data = await self.tasks_repo.get_by_id(task_id, use_admin=True)
            if not task_data:
                raise NotFoundError("Task not found")
            
            return AgentTask(**task_data)
            
        except Exception as e:
            logger.error(f"Error getting agent task {task_id}: {e}")
            raise
    
    async def update_agent_configuration(
        self,
        organization_id: str,
        agent_type: AgentType,
        update_data: AgentConfigurationUpdate
    ) -> AgentConfiguration:
        """Update agent configuration"""
        try:
            agent_instance = self.get_agent_instance(organization_id, agent_type)
            
            # Prepare update data
            settings = {}
            if update_data.settings:
                settings.update(update_data.settings)
            
            # Update configuration
            configuration = await agent_instance.update_configuration(settings)
            
            return configuration
            
        except Exception as e:
            logger.error(f"Error updating agent configuration: {e}")
            raise
    
    async def execute_crew_task(
        self,
        organization_id: str,
        agent_types: List[AgentType],
        task_description: str,
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a multi-agent crew task"""
        try:
            crew_manager = self.get_crew_manager(organization_id)
            
            # Ensure all agents are initialized
            for agent_type in agent_types:
                self.get_agent_instance(organization_id, agent_type)
            
            # Execute crew task
            result = await crew_manager.execute_crew_task(
                agent_types, task_description, input_data
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing crew task: {e}")
            raise
    
    async def _get_agent_metrics(
        self, 
        organization_id: str, 
        agent_type: AgentType
    ) -> AgentMetrics:
        """Get metrics for a specific agent"""
        try:
            # Get task statistics
            all_tasks = await self.tasks_repo.list(
                filters={
                    'organization_id': organization_id,
                    'agent_type': agent_type.value
                },
                limit=1000,  # Get all tasks for metrics
                use_admin=True
            )
            
            total_tasks = len(all_tasks)
            completed_tasks = len([t for t in all_tasks if t['status'] == TaskStatus.COMPLETED.value])
            failed_tasks = len([t for t in all_tasks if t['status'] == TaskStatus.FAILED.value])
            
            # Calculate success rate
            success_rate = (completed_tasks / total_tasks) if total_tasks > 0 else 0.0
            
            # Calculate average execution time
            execution_times = []
            for task in all_tasks:
                if task.get('started_at') and task.get('completed_at'):
                    try:
                        start = datetime.fromisoformat(task['started_at'].replace('Z', '+00:00'))
                        end = datetime.fromisoformat(task['completed_at'].replace('Z', '+00:00'))
                        execution_times.append((end - start).total_seconds())
                    except:
                        continue
            
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0.0
            
            # Get last execution time
            last_execution = None
            if all_tasks:
                try:
                    last_task = max(all_tasks, key=lambda t: t.get('created_at', ''))
                    last_execution = datetime.fromisoformat(
                        last_task['created_at'].replace('Z', '+00:00')
                    )
                except:
                    pass
            
            return AgentMetrics(
                agent_type=agent_type,
                total_tasks=total_tasks,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                average_execution_time=avg_execution_time,
                success_rate=success_rate,
                last_execution=last_execution
            )
            
        except Exception as e:
            logger.error(f"Error getting agent metrics: {e}")
            return AgentMetrics(agent_type=agent_type)


# Create service instance
agent_service = AgentService()
