"""
Campaign Planning Agent
Handles comprehensive campaign planning including lead volume calculations,
timeline creation, source distribution, and database storage.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from pydantic import BaseModel, Field

from app.modules.agents.base import SeldaAgent, agent_registry
from app.modules.agents.models import AgentType
from app.core.database import BaseRepository

logger = logging.getLogger(__name__)


class CampaignPlanningInput(BaseModel):
    """Input model for campaign planning agent"""
    campaign_id: str = Field(..., description="Unique campaign identifier")
    organization_id: str = Field(..., description="Organization ID")
    goal_description: str = Field(..., description="User's sales goal description")
    target_meetings: int = Field(..., description="Target number of meetings")
    target_industry: str = Field(..., description="Target industry")
    target_location: str = Field(..., description="Target location")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences for campaign")
    parsed_goal: Dict[str, Any] = Field(default_factory=dict, description="Parsed goal from goal understanding agent")


class CampaignPlanningOutput(BaseModel):
    """Output model for campaign planning agent"""
    campaign_plan_id: str = Field(..., description="Created campaign plan ID")
    lead_volume_plan: Dict[str, Any] = Field(..., description="Lead volume calculations")
    source_distribution: Dict[str, int] = Field(..., description="Lead source distribution")
    campaign_timeline: Dict[str, Any] = Field(..., description="Campaign timeline and phases")
    email_sequences: List[Dict[str, Any]] = Field(..., description="Email sequence templates")
    keyword_strategy: Dict[str, Any] = Field(..., description="Keyword sourcing strategy")
    success_metrics: Dict[str, Any] = Field(..., description="Expected success metrics")
    next_agent_input: Dict[str, Any] = Field(..., description="Input for next agent")


@agent_registry.register(AgentType.CAMPAIGN_PLANNING)
class CampaignPlanningAgent(SeldaAgent):
    """Agent responsible for comprehensive campaign planning"""

    @property
    def agent_type(self) -> AgentType:
        return AgentType.CAMPAIGN_PLANNING

    @property
    def name(self) -> str:
        return "Campaign Planning Specialist"

    @property
    def description(self) -> str:
        return "Creates comprehensive campaign plans including lead volume calculations, timeline creation, source distribution, and keyword strategies"

    def _get_agent_goal(self) -> str:
        return """Create a comprehensive campaign plan including lead volume calculations,
        timeline creation, source distribution, keyword strategies, and email sequences
        based on user goals and store all planning data in the database."""

    def _get_agent_backstory(self) -> str:
        return """You are a campaign planning specialist with expertise in lead volume calculations,
        conversion rate optimization, timeline planning, and multi-channel lead sourcing strategies."""

    def get_capabilities(self) -> List[str]:
        """Return list of agent capabilities"""
        return [
            "lead_volume_calculation",
            "conversion_rate_optimization",
            "campaign_timeline_planning",
            "multi_source_distribution",
            "keyword_strategy_development",
            "email_sequence_planning",
            "success_metrics_calculation",
            "database_storage"
        ]

    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute campaign planning logic"""
        try:
            planning_input = CampaignPlanningInput(**input_data)
            
            # Initialize repositories
            campaign_plans_repo = BaseRepository("campaign_plans", self.db)
            timeline_phases_repo = BaseRepository("campaign_timeline_phases", self.db)
            email_templates_repo = BaseRepository("email_sequence_templates", self.db)
            sourcing_strategies_repo = BaseRepository("lead_sourcing_strategies", self.db)
            
            logger.info(f"Starting campaign planning for {planning_input.target_meetings} meetings")
            
            # 1. Calculate lead volume requirements
            lead_volume_plan = self._calculate_lead_volume(planning_input.target_meetings)
            
            # 2. Plan source distribution
            source_distribution = self._plan_source_distribution(lead_volume_plan["total_leads_needed"])
            
            # 3. Create campaign timeline
            campaign_timeline = self._create_campaign_timeline(
                planning_input.target_meetings, 
                lead_volume_plan["total_leads_needed"]
            )
            
            # 4. Generate email sequences
            email_sequences = self._generate_email_sequences(campaign_timeline)
            
            # 5. Create keyword strategy using AI-generated keywords from goal understanding
            keyword_strategy = await self._create_enhanced_keyword_strategy(
                planning_input.target_industry,
                planning_input.goal_description,
                planning_input.parsed_goal
            )
            
            # 6. Calculate success metrics
            success_metrics = self._calculate_success_metrics(lead_volume_plan)
            
            # 7. Store campaign plan in database
            campaign_plan_id = await self._store_campaign_plan(
                planning_input, lead_volume_plan, source_distribution, 
                campaign_timeline, campaign_plans_repo
            )
            
            # 8. Store timeline phases
            await self._store_timeline_phases(
                campaign_plan_id, campaign_timeline, timeline_phases_repo
            )
            
            # 9. Store email templates
            await self._store_email_templates(
                campaign_plan_id, email_sequences, email_templates_repo
            )
            
            # 10. Store sourcing strategy
            await self._store_sourcing_strategy(
                planning_input, keyword_strategy, sourcing_strategies_repo
            )
            
            # Prepare output
            output = CampaignPlanningOutput(
                campaign_plan_id=campaign_plan_id,
                lead_volume_plan=lead_volume_plan,
                source_distribution=source_distribution,
                campaign_timeline=campaign_timeline,
                email_sequences=email_sequences,
                keyword_strategy=keyword_strategy,
                success_metrics=success_metrics,
                next_agent_input={
                    "campaign_id": planning_input.campaign_id,
                    "campaign_plan_id": campaign_plan_id,
                    "target_leads": lead_volume_plan["total_leads_needed"],
                    "apollo_leads_needed": source_distribution["apollo"],
                    "keyword_strategy": keyword_strategy,
                    "target_audience": {
                        "industry": planning_input.target_industry,
                        "location": planning_input.target_location,
                        "company_size": "any"
                    }
                }
            )
            
            logger.info(f"Campaign planning completed. Plan ID: {campaign_plan_id}")
            return output.model_dump()
            
        except Exception as e:
            logger.error(f"Campaign planning execution failed: {e}")
            raise

    def _calculate_lead_volume(self, target_meetings: int) -> Dict[str, Any]:
        """Calculate required lead volume based on industry conversion rates"""
        # Realistic conversion rates for B2B cold outreach
        email_open_rate = 30.0  # 30% open rate
        response_rate = 8.0     # 8% response rate from opens  
        meeting_rate = 40.0     # 40% of responses convert to meetings
        
        # Calculate backwards from target meetings
        responses_needed = target_meetings / (meeting_rate / 100)
        opens_needed = responses_needed / (response_rate / 100)
        total_leads_needed = opens_needed / (email_open_rate / 100)
        
        # Round up and add buffer, but cap at reasonable maximum
        total_leads_needed = int(total_leads_needed * 1.2)  # 20% buffer
        total_leads_needed = min(total_leads_needed, target_meetings * 100)  # Cap at 100x target meetings
        
        return {
            "target_meetings": target_meetings,
            "total_leads_needed": total_leads_needed,
            "responses_needed": int(responses_needed),
            "opens_needed": int(opens_needed),
            "email_open_rate": email_open_rate,
            "response_rate": response_rate,
            "meeting_rate": meeting_rate,
            "buffer_applied": 20.0
        }

    def _plan_source_distribution(self, total_leads_needed: int) -> Dict[str, int]:
        """Plan distribution across multiple lead sources"""
        # Currently using Apollo.io as primary source (100%)
        # Other sources will be added in future phases
        return {
            "apollo": total_leads_needed,  # 100% from Apollo.io for now
            "linkedin": 0,  # Future implementation
            "local_directories": 0  # Future implementation
        }

    def _create_campaign_timeline(self, target_meetings: int, total_leads: int) -> Dict[str, Any]:
        """Create detailed campaign timeline with email sequences using real dates"""
        # Calculate campaign duration based on lead volume
        leads_per_day = 100  # Increased daily sending limit for faster execution
        sourcing_days = max(2, (total_leads // 200) + 1)  # Faster sourcing

        # Get current date and time
        now = datetime.now()
        campaign_start = now + timedelta(hours=1)  # Start campaign 1 hour from now

        # Calculate actual dates for each phase
        initial_start = campaign_start
        initial_end = initial_start + timedelta(days=1)

        first_followup_start = initial_end + timedelta(days=2)  # 2 days after initial
        first_followup_end = first_followup_start + timedelta(days=1)

        second_followup_start = first_followup_end + timedelta(days=2)  # 2 days after first follow-up
        second_followup_end = second_followup_start + timedelta(days=1)

        breakup_date = second_followup_end + timedelta(days=3)  # 3 days after second follow-up

        campaign_end = breakup_date + timedelta(days=1)
        total_duration = (campaign_end - campaign_start).days

        # Format dates in human-readable format
        def format_datetime(dt):
            return dt.strftime("%A, %B %d, %Y at %I:%M %p")

        def format_date(dt):
            return dt.strftime("%A, %B %d, %Y")

        # Start outreach immediately after planning, with 2-3 day intervals
        timeline = {
            "total_duration": total_duration,
            "campaign_start_date": format_datetime(campaign_start),
            "campaign_end_date": format_datetime(campaign_end),
            "sourcing_phase": {
                "duration": sourcing_days,
                "start_date": format_datetime(now),
                "end_date": format_datetime(campaign_start),
                "description": f"Source {total_leads} leads from Apollo.io"
            },
            "email_phases": [
                {
                    "name": "Initial Outreach",
                    "start_date": format_datetime(initial_start),
                    "end_date": format_datetime(initial_end),
                    "start_day": 1,  # Keep for backward compatibility
                    "end_day": 2,
                    "description": "Send personalized cold emails to all leads",
                    "leads_per_day": leads_per_day,
                    "frequency": "Immediate start",
                    "human_schedule": f"Starts {format_date(initial_start)} at {initial_start.strftime('%I:%M %p')}"
                },
                {
                    "name": "First Follow-up",
                    "start_date": format_datetime(first_followup_start),
                    "end_date": format_datetime(first_followup_end),
                    "start_day": 4,  # Keep for backward compatibility
                    "end_day": 5,
                    "description": "Follow up with non-responders (value-add content)",
                    "leads_per_day": leads_per_day,
                    "frequency": "2 days after initial",
                    "human_schedule": f"Starts {format_date(first_followup_start)} at {first_followup_start.strftime('%I:%M %p')}"
                },
                {
                    "name": "Second Follow-up",
                    "start_date": format_datetime(second_followup_start),
                    "end_date": format_datetime(second_followup_end),
                    "start_day": 7,  # Keep for backward compatibility
                    "end_day": 8,
                    "description": "Final follow-up with case study/social proof",
                    "leads_per_day": leads_per_day,
                    "frequency": "2 days after first follow-up",
                    "human_schedule": f"Starts {format_date(second_followup_start)} at {second_followup_start.strftime('%I:%M %p')}"
                },
                {
                    "name": "Break-up Email",
                    "start_date": format_datetime(breakup_date),
                    "end_date": format_datetime(breakup_date),
                    "start_day": 11,  # Keep for backward compatibility
                    "end_day": 11,
                    "description": "Polite break-up email to remaining non-responders",
                    "leads_per_day": leads_per_day,
                    "frequency": "3 days after second follow-up",
                    "human_schedule": f"Scheduled for {format_date(breakup_date)} at {breakup_date.strftime('%I:%M %p')}"
                }
            ]
        }
        
        return timeline

    def _generate_email_sequences(self, campaign_timeline: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate email sequence templates"""
        email_sequences = []
        
        for i, phase in enumerate(campaign_timeline.get("email_phases", []), 1):
            sequence = {
                "sequence_number": i,
                "phase_name": phase["name"],
                "day_offset": phase["start_day"],
                "subject_template": self._get_email_subject_template(i),
                "content_strategy": self._get_email_content_strategy(i),
                "cta_template": self._get_email_cta_template(i)
            }
            email_sequences.append(sequence)
        
        return email_sequences

    def _get_email_subject_template(self, sequence_number: int) -> str:
        """Get email subject template for sequence"""
        subjects = {
            1: "Introduction & Value Proposition",
            2: "Industry Insights & Case Study", 
            3: "Social Proof & Testimonials",
            4: "Final Follow-up"
        }
        return subjects.get(sequence_number, "Follow-up")

    def _get_email_content_strategy(self, sequence_number: int) -> str:
        """Get email content strategy for sequence"""
        strategies = {
            1: "Personalized introduction with clear value proposition",
            2: "Share relevant industry insights and success story",
            3: "Customer testimonials and social proof",
            4: "Polite break-up email with door left open"
        }
        return strategies.get(sequence_number, "Follow-up content")

    def _get_email_cta_template(self, sequence_number: int) -> str:
        """Get email CTA template for sequence"""
        ctas = {
            1: "Schedule a brief call to discuss your needs",
            2: "Would you like to see how we helped [similar company]?",
            3: "Quick 15-minute call to explore opportunities",
            4: "Feel free to reach out when timing is better"
        }
        return ctas.get(sequence_number, "Let's connect")

    def _create_keyword_strategy(self, industry: str, goal_description: str) -> Dict[str, Any]:
        """Create keyword strategy for lead sourcing with focus on generic, high-volume terms"""
        # Generic keyword mapping - prioritize simple terms for better lead volume
        keyword_mapping = {
            "restaurant": {
                "primary": "restaurant",
                "related": ["restaurant", "food", "dining", "hospitality"]
            },
            "restaurants": {
                "primary": "restaurant",
                "related": ["restaurant", "food", "dining", "hospitality"]
            },
            "healthcare": {
                "primary": "hospital",
                "related": ["hospital", "healthcare", "medical", "clinic"]
            },
            "hospital": {
                "primary": "hospital",
                "related": ["hospital", "healthcare", "medical", "clinic"]
            },
            "technology": {
                "primary": "technology",
                "related": ["technology", "software", "tech", "IT"]
            },
            "retail": {
                "primary": "retail",
                "related": ["retail", "store", "shop", "commerce"]
            },
            "finance": {
                "primary": "finance",
                "related": ["finance", "bank", "financial", "investment"]
            }
        }

        # Get base keywords - prioritize generic terms
        base_industry = industry.lower()
        keywords_data = keyword_mapping.get(base_industry, {
            "primary": industry,
            "related": [industry, "business", "company"]
        })

        return {
            "primary_keywords": keywords_data["primary"],
            "related_keywords": keywords_data["related"],
            "total_variations": len(keywords_data["related"]) + 1,
            "strategy": "Generic keywords optimized for high lead volume",
            "source": "generic_mapping"
        }

    async def _create_enhanced_keyword_strategy(self, industry: str, goal_description: str, parsed_goal: Dict[str, Any]) -> Dict[str, Any]:
        """Create enhanced keyword strategy using AI-generated keywords from goal understanding"""
        try:
            # First, try to use Apollo keywords from goal understanding agent
            apollo_keywords = parsed_goal.get("apollo_keywords", {})

            if apollo_keywords and apollo_keywords.get("primary_keywords"):
                logger.info("Using AI-generated Apollo keywords from goal understanding")
                return {
                    "primary_keywords": apollo_keywords.get("primary_keywords", ""),
                    "related_keywords": apollo_keywords.get("related_keywords", []),
                    "total_variations": len(apollo_keywords.get("related_keywords", [])) + 1,
                    "strategy": apollo_keywords.get("reasoning", "AI-generated keywords based on goal analysis"),
                    "source": "goal_understanding_ai"
                }
            else:
                # Fallback to enhanced AI-based keyword generation
                logger.info("Generating enhanced keywords using AI analysis")
                return await self._generate_ai_keyword_strategy(industry, goal_description, parsed_goal)

        except Exception as e:
            logger.error(f"Enhanced keyword strategy failed: {e}")
            # Final fallback to original method
            return self._create_keyword_strategy(industry, goal_description)

    async def _generate_ai_keyword_strategy(self, industry: str, goal_description: str, parsed_goal: Dict[str, Any]) -> Dict[str, Any]:
        """Generate keyword strategy using AI analysis"""
        try:
            product_service = parsed_goal.get("product_or_service", "")
            target_audience = parsed_goal.get("target_audience", {})

            prompt = f"""
            Generate the most effective Apollo.io search keywords for lead sourcing based on this sales goal analysis:

            Industry: {industry}
            Product/Service: {product_service}
            Goal Description: {goal_description}
            Target Job Titles: {target_audience.get('job_titles', [])}
            Company Size: {target_audience.get('company_size', '')}

            Create keywords that will find decision-makers who would be interested in {product_service} in the {industry} industry.

            IMPORTANT: Prioritize generic, high-volume keywords over specific phrases.

            Return ONLY a JSON object with this structure:
            {{
                "primary_keywords": "Simple, generic industry terms (e.g., 'hospital', 'restaurant', 'technology')",
                "related_keywords": ["Single words or simple 2-word terms", "Generic industry variations", "Broad category terms"],
                "reasoning": "Why these keywords are optimal for finding high volume of relevant prospects"
            }}

            Focus on:
            1. Simple, generic industry terms that return high volume
            2. Single words or simple 2-word combinations
            3. Broad category terms rather than specific phrases
            4. Terms that cast a wide net for maximum lead coverage
            """

            response = await self.llm.ainvoke(prompt)

            # Parse the JSON response
            try:
                response_content = response.content.strip()
                if response_content.startswith('```json'):
                    response_content = response_content.replace('```json', '').replace('```', '').strip()
                elif response_content.startswith('```'):
                    response_content = response_content.replace('```', '').strip()

                keyword_data = json.loads(response_content)

                return {
                    "primary_keywords": keyword_data.get("primary_keywords", ""),
                    "related_keywords": keyword_data.get("related_keywords", []),
                    "total_variations": len(keyword_data.get("related_keywords", [])) + 1,
                    "strategy": keyword_data.get("reasoning", "AI-generated keywords"),
                    "source": "campaign_planning_ai"
                }

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI keyword response: {e}")
                return self._create_keyword_strategy(industry, goal_description)

        except Exception as e:
            logger.error(f"AI keyword generation failed: {e}")
            return self._create_keyword_strategy(industry, goal_description)

    def _calculate_success_metrics(self, lead_volume_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate expected success metrics"""
        total_leads = lead_volume_plan["total_leads_needed"]
        open_rate = lead_volume_plan["email_open_rate"]
        response_rate = lead_volume_plan["response_rate"]
        meeting_rate = lead_volume_plan["meeting_rate"]
        
        expected_opens = int(total_leads * (open_rate / 100))
        expected_responses = int(expected_opens * (response_rate / 100))
        expected_meetings = int(expected_responses * (meeting_rate / 100))
        
        return {
            "expected_opens": expected_opens,
            "expected_responses": expected_responses,
            "expected_meetings": expected_meetings,
            "conversion_rates": {
                "open_rate": open_rate,
                "response_rate": response_rate,
                "meeting_rate": meeting_rate
            }
        }

    async def _store_campaign_plan(
        self,
        planning_input: CampaignPlanningInput,
        lead_volume_plan: Dict[str, Any],
        source_distribution: Dict[str, int],
        campaign_timeline: Dict[str, Any],
        campaign_plans_repo: BaseRepository
    ) -> str:
        """Store campaign plan in database"""
        campaign_plan_data = {
            "organization_id": planning_input.organization_id,
            "campaign_id": planning_input.campaign_id,
            "goal_description": planning_input.goal_description,
            "target_meetings": planning_input.target_meetings,
            "target_industry": planning_input.target_industry,
            "target_location": planning_input.target_location,
            "total_leads_needed": lead_volume_plan["total_leads_needed"],
            "email_open_rate": lead_volume_plan["email_open_rate"],
            "response_rate": lead_volume_plan["response_rate"],
            "meeting_rate": lead_volume_plan["meeting_rate"],
            "buffer_percentage": lead_volume_plan["buffer_applied"],
            "apollo_leads": source_distribution["apollo"],
            "linkedin_leads": source_distribution["linkedin"],
            "local_directory_leads": source_distribution["local_directories"],
            "total_duration_days": campaign_timeline["total_duration"],
            "sourcing_duration_days": campaign_timeline["sourcing_phase"]["duration"],
            "status": "planned"
        }

        created_plan = await campaign_plans_repo.create(campaign_plan_data, use_admin=True)
        return str(created_plan["id"])

    async def _store_timeline_phases(
        self,
        campaign_plan_id: str,
        campaign_timeline: Dict[str, Any],
        timeline_phases_repo: BaseRepository
    ) -> None:
        """Store timeline phases in database"""
        # Store sourcing phase
        sourcing_phase_data = {
            "campaign_plan_id": campaign_plan_id,
            "phase_name": "Lead Sourcing",
            "phase_type": "sourcing",
            "start_day": 0,
            "end_day": campaign_timeline["sourcing_phase"]["duration"],
            "description": campaign_timeline["sourcing_phase"]["description"],
            "leads_per_day": 0,
            "frequency_description": "Continuous sourcing"
        }
        await timeline_phases_repo.create(sourcing_phase_data, use_admin=True)

        # Store email phases
        for phase in campaign_timeline["email_phases"]:
            phase_data = {
                "campaign_plan_id": campaign_plan_id,
                "phase_name": phase["name"],
                "phase_type": phase["name"].lower().replace(" ", "_"),
                "start_day": phase["start_day"],
                "end_day": phase["end_day"],
                "description": phase["description"],
                "leads_per_day": phase["leads_per_day"],
                "frequency_description": phase["frequency"]
            }
            await timeline_phases_repo.create(phase_data, use_admin=True)

    async def _store_email_templates(
        self,
        campaign_plan_id: str,
        email_sequences: List[Dict[str, Any]],
        email_templates_repo: BaseRepository
    ) -> None:
        """Store email sequence templates in database"""
        for sequence in email_sequences:
            template_data = {
                "campaign_plan_id": campaign_plan_id,
                "sequence_number": sequence["sequence_number"],
                "subject_template": sequence["subject_template"],
                "content_strategy": sequence["content_strategy"],
                "cta_template": sequence["cta_template"],
                "day_offset": sequence["day_offset"]
            }
            await email_templates_repo.create(template_data, use_admin=True)

    async def _store_sourcing_strategy(
        self,
        planning_input: CampaignPlanningInput,
        keyword_strategy: Dict[str, Any],
        sourcing_strategies_repo: BaseRepository
    ) -> None:
        """Store sourcing strategy in database"""
        strategy_data = {
            "organization_id": planning_input.organization_id,
            "campaign_id": planning_input.campaign_id,
            "industry": planning_input.target_industry,
            "goal_description": planning_input.goal_description,
            "primary_keywords": keyword_strategy["primary_keywords"],
            "related_keywords": keyword_strategy["related_keywords"],
            "keywords_used": [],
            "total_attempts": 0,
            "successful_attempts": 0,
            "leads_per_attempt": {},
            "total_leads_sourced": 0,
            "target_leads": 0,  # Will be updated by lead sourcing agent
            "success_rate": 0.0
        }
        await sourcing_strategies_repo.create(strategy_data, use_admin=True)
