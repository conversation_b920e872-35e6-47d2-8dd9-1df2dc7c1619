#!/usr/bin/env python3
"""
Interactive Demo - Shows exactly how the real chat interface works
This demonstrates the actual user experience with simulated typing
"""

import asyncio
import sys
import os
import time
import uuid

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent

class InteractiveDemo:
    """Demo showing real interactive chat experience"""
    
    def __init__(self):
        self.organization_id = "00000000-0000-4000-8000-000000000001"
        
    def print_banner(self):
        """Print welcome banner"""
        print("\n" + "="*70)
        print("🤖 SELDA AI - Interactive Lead Sourcing Assistant")
        print("="*70)
        print("Welcome! I'm your AI assistant for sourcing high-quality sales leads.")
        print("\n💡 How it works:")
        print("  1. Tell me your sales goal in natural language")
        print("  2. I'll ask follow-up questions if needed")
        print("  3. I'll source targeted leads from Apollo.io")
        print("  4. You'll get a complete campaign summary")
        print("\n📝 Example goals:")
        print("  • 'I need 10 meetings with restaurant owners in London'")
        print("  • 'Find me tech startup CEOs in San Francisco for 5 meetings'")
        print("  • 'Source leads for SaaS companies in New York'")
        print("\n⌨️ Commands:")
        print("  • Type 'help' for assistance")
        print("  • Type 'quit' or 'exit' to end session")
        print("  • Just type naturally - I understand!")
        print("-"*70)
        
    def simulate_user_typing(self, text: str, delay: float = 0.05):
        """Simulate user typing"""
        print("👤 ", end="", flush=True)
        for char in text:
            print(char, end='', flush=True)
            time.sleep(delay)
        print()
        
    def print_ai_response(self, text: str):
        """Print AI response"""
        print(f"\n🤖 {text}")
        
    async def demo_complete_goal(self):
        """Demo with a complete goal"""
        print("\n" + "="*20 + " DEMO 1: Complete Goal " + "="*20)
        
        # Simulate user input
        print("\n💬 What's your sales goal? (e.g., 'I need 5 meetings with restaurant owners in London')")
        time.sleep(1)
        user_goal = "I need 3 meetings with restaurant owners in London"
        self.simulate_user_typing(user_goal)
        
        self.print_ai_response("Let me understand your goal...")
        
        # Process with goal agent
        goal_agent = GoalUnderstandingAgent(self.organization_id)
        
        goal_input = {
            "goal_description": user_goal,
            "context": "Interactive demo for lead sourcing",
            "user_id": "demo-user-001",
            "organization_id": self.organization_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result = await goal_agent.execute(goal_input)
        
        # Show results
        if result.get("is_complete", False):
            parsed_goal = result.get('parsed_goal', {})
            target_audience = parsed_goal.get('target_audience', {})
            
            self.print_ai_response("Perfect! I have all the information I need:")
            print(f"   🎯 Industry: {target_audience.get('industry', 'restaurants')}")
            print(f"   📍 Location: {target_audience.get('geography', 'London, UK')}")
            print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 3)} meetings")
            print(f"   ✅ Confidence: {result.get('confidence_score', 0.95):.1%}")
            
            # Proceed to lead sourcing
            goal_data = {
                "industry": target_audience.get('industry', 'restaurants'),
                "location": target_audience.get('geography', 'London, UK'),
                "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 3),
                "goal_description": user_goal
            }
            
            await self.demo_lead_sourcing(goal_data)
        
    async def demo_incomplete_goal(self):
        """Demo with incomplete goal requiring follow-ups"""
        print("\n" + "="*20 + " DEMO 2: Incomplete Goal " + "="*20)
        
        # Simulate user input
        print("\n💬 What's your sales goal? (e.g., 'I need 5 meetings with restaurant owners in London')")
        time.sleep(1)
        user_goal = "I need some meetings with business owners"
        self.simulate_user_typing(user_goal)
        
        self.print_ai_response("Let me understand your goal...")
        
        # Process with goal agent
        goal_agent = GoalUnderstandingAgent(self.organization_id)
        
        goal_input = {
            "goal_description": user_goal,
            "context": "Interactive demo for lead sourcing",
            "user_id": "demo-user-002",
            "organization_id": self.organization_id,
            "conversation_history": [],
            "is_follow_up": False,
            "answered_questions": {}
        }
        
        result = await goal_agent.execute(goal_input)
        
        # Handle follow-up questions
        follow_ups = result.get("clarification_questions", [])
        if follow_ups:
            self.print_ai_response("I need some additional information to help you better:")
            
            # Simulate answering follow-up questions
            answers = {}
            for i, question in enumerate(follow_ups, 1):
                print(f"\n💬 ({i}/{len(follow_ups)}) {question}")
                time.sleep(1)
                
                # Simulate user answers
                if "industry" in question.lower():
                    answer = "technology"
                    answers["industry"] = answer
                elif "location" in question.lower():
                    answer = "San Francisco"
                    answers["location"] = answer
                elif "meeting" in question.lower() or "target" in question.lower():
                    answer = "5"
                    answers["target_meetings"] = answer
                else:
                    answer = "medium"
                    answers[f"question_{i}"] = answer
                
                self.simulate_user_typing(answer)
            
            # Process follow-up answers
            self.print_ai_response("Thank you! Let me process your answers...")
            
            follow_up_input = {
                "goal_description": user_goal,
                "context": "Interactive demo follow-up",
                "user_id": "demo-user-002",
                "organization_id": self.organization_id,
                "conversation_history": [],
                "is_follow_up": True,
                "answered_questions": answers
            }
            
            final_result = await goal_agent.execute(follow_up_input)
            
            if final_result.get("is_complete", False):
                parsed_goal = final_result.get('parsed_goal', {})
                target_audience = parsed_goal.get('target_audience', {})
                
                self.print_ai_response("Excellent! Now I have everything I need:")
                print(f"   🎯 Industry: {target_audience.get('industry', 'technology')}")
                print(f"   📍 Location: {target_audience.get('geography', 'San Francisco')}")
                print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 5)} meetings")
                print(f"   ✅ Confidence: {final_result.get('confidence_score', 0.95):.1%}")
                
                # Proceed to lead sourcing
                goal_data = {
                    "industry": target_audience.get('industry', 'technology'),
                    "location": target_audience.get('geography', 'San Francisco'),
                    "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 5),
                    "goal_description": user_goal
                }
                
                await self.demo_lead_sourcing(goal_data)
        
    async def demo_lead_sourcing(self, goal_data):
        """Demo lead sourcing phase"""
        print("\n" + "="*20 + " LEAD SOURCING " + "="*20)
        
        self.print_ai_response("Now I'll source leads for your goal. This may take a moment...")
        
        target_meetings = goal_data.get("target_meetings", 5)
        lead_count_target = target_meetings * 1  # 1 lead per meeting for demo
        
        print(f"\n🔍 Sourcing {lead_count_target} leads for {target_meetings} meetings...")
        print(f"   🎯 Industry: {goal_data.get('industry', 'N/A')}")
        print(f"   📍 Location: {goal_data.get('location', 'N/A')}")
        print(f"   📊 Quality Threshold: 50%")
        
        # Show progress
        print(f"\n⏳ Processing...")
        print("   🔍 Searching Apollo.io database...")
        time.sleep(1)
        print("   📧 Enriching contact emails...")
        time.sleep(1)
        print("   🎯 Filtering for quality...")
        time.sleep(1)
        print("   💾 Storing leads...")
        time.sleep(1)
        
        # Execute lead sourcing
        lead_agent = LeadSourcingAgent(self.organization_id)
        session_id = str(uuid.uuid4())
        
        sourcing_input = {
            "campaign_id": f"demo-campaign-{session_id}",
            "target_audience": {
                "industry": goal_data.get("industry", ""),
                "geography": goal_data.get("location", ""),
                "company_size": "any",
                "titles": ["CEO", "Owner", "Manager", "Director"]
            },
            "industry": goal_data.get("industry", ""),
            "location": goal_data.get("location", ""),
            "lead_count_target": lead_count_target,
            "quality_threshold": 50,
            "sources": ["apollo"]
        }
        
        sourcing_result = await lead_agent.execute(sourcing_input)
        
        # Display results
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        duplicates = sourcing_result.get("duplicates_filtered", 0)
        cooldown = sourcing_result.get("cooldown_filtered", 0)
        
        self.print_ai_response("Lead sourcing completed! Here are the results:")
        print(f"   ✅ Leads sourced: {leads_sourced}")
        print(f"   🔄 Duplicates filtered: {duplicates}")
        print(f"   ⏰ Cooldown filtered: {cooldown}")
        
        # Show quality distribution
        quality_dist = sourcing_result.get("quality_distribution", {})
        if quality_dist:
            print(f"   📊 Quality distribution:")
            print(f"      🟢 High quality: {quality_dist.get('high', 0)} leads")
            print(f"      🟡 Medium quality: {quality_dist.get('medium', 0)} leads")
            print(f"      🔴 Low quality: {quality_dist.get('low', 0)} leads")
        
        # Show source breakdown
        sources = sourcing_result.get("leads_by_source", {})
        if sources:
            print(f"   🔗 Sources:")
            for source, count in sources.items():
                print(f"      📡 {source.title()}: {count} leads")
        
        # Final summary
        self.display_summary(goal_data, sourcing_result)
        
    def display_summary(self, goal_data, sourcing_result):
        """Display campaign summary"""
        print("\n" + "="*20 + " CAMPAIGN SUMMARY " + "="*20)
        
        leads_sourced = sourcing_result.get("leads_sourced", 0)
        target_meetings = goal_data.get("target_meetings", 0)
        
        self.print_ai_response("🎉 Your lead sourcing campaign is complete!")
        print(f"\n📋 Campaign Details:")
        print(f"   🎯 Goal: {goal_data.get('goal_description', 'N/A')}")
        print(f"   🏭 Industry: {goal_data.get('industry', 'N/A')}")
        print(f"   🌍 Location: {goal_data.get('location', 'N/A')}")
        print(f"   🎯 Target Meetings: {target_meetings}")
        print(f"   📊 Leads Sourced: {leads_sourced}")
        
        if leads_sourced > 0:
            ratio = leads_sourced / target_meetings if target_meetings > 0 else 0
            print(f"   📈 Lead-to-Meeting Ratio: {ratio:.1f}:1")
            
            self.print_ai_response("✅ Success! Your leads are ready for outreach.")
            print("   💡 Next steps:")
            print("      1. Review the sourced leads in your dashboard")
            print("      2. Customize your outreach messages")
            print("      3. Start your email campaign")
            print("      4. Track responses and book meetings")
        else:
            self.print_ai_response("⚠️ No leads were sourced. This could be due to:")
            print("   • Very specific targeting criteria")
            print("   • API limitations or credits")
            print("   • Temporary service issues")
            print("   💡 Try adjusting your criteria or contact support.")
        
        # Simulate continue question
        print("\n💬 Would you like to create another campaign? (yes/no)")
        time.sleep(1)
        self.simulate_user_typing("no")
        self.print_ai_response("Thank you for using Selda AI! Goodbye! 👋")
        
    async def run_demo(self):
        """Run the interactive demo"""
        try:
            self.print_banner()
            
            print("\n🎬 This demo shows the EXACT experience users have with the interactive chat.")
            print("   Watch how users type their goals and interact with the AI!")
            
            # Demo 1: Complete goal
            await self.demo_complete_goal()
            
            print("\n" + "="*70)
            print("🎬 DEMO 2 - Now let's see what happens with an incomplete goal...")
            time.sleep(3)
            
            # Demo 2: Incomplete goal
            await self.demo_incomplete_goal()
            
            print("\n" + "="*70)
            self.print_ai_response("🎬 Demo completed! This is exactly how the real chat interface works:")
            print("   • Users type their goals naturally")
            print("   • AI asks intelligent follow-up questions")
            print("   • Real-time lead sourcing from Apollo.io")
            print("   • Complete campaign summaries")
            print("   • Professional, emoji-rich interface")
            print("\n🚀 The actual interface accepts real user input and works identically!")
            print("\n📁 Files created:")
            print("   • real_chat_interface.py - Full interactive version")
            print("   • interactive_chat.py - Advanced interactive version")
            print("   • simple_chat_test.py - Simple interactive version")
            
        except Exception as e:
            print(f"❌ Demo error: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """Main entry point"""
    demo = InteractiveDemo()
    await demo.run_demo()

if __name__ == "__main__":
    print("🚀 Starting Interactive Chat Demo...")
    asyncio.run(main())
