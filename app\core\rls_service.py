"""
RLS-aware service classes for database operations
"""

from typing import Dict, Any, List, Optional
from supabase import Client
import logging

from app.core.database import BaseRepository, supabase_service
from app.core.exceptions import ExternalServiceError

logger = logging.getLogger(__name__)


class RLSRepository(BaseRepository):
    """Repository class that enforces RLS by default"""
    
    def __init__(self, table_name: str, supabase_service=None):
        super().__init__(table_name, supabase_service or supabase_service)
        self._auth_client: Optional[Client] = None
    
    def set_auth_client(self, client: Client):
        """Set the authenticated client for RLS operations"""
        self._auth_client = client
    
    async def create(self, data: Dict[str, Any], use_admin: bool = False) -> Dict[str, Any]:
        """Create a new record with RLS context"""
        if not use_admin and not self._auth_client:
            raise ExternalServiceError("No authenticated client set for RLS operation")
        
        client = self._auth_client if not use_admin else None
        return await super().create(data, use_admin, client)
    
    async def get_by_id(self, record_id: str, use_admin: bool = False) -> Optional[Dict[str, Any]]:
        """Get record by ID with RLS context"""
        if not use_admin and not self._auth_client:
            raise ExternalServiceError("No authenticated client set for RLS operation")
        
        client = self._auth_client if not use_admin else None
        return await super().get_by_id(record_id, use_admin, client)
    
    async def update(self, record_id: str, data: Dict[str, Any], use_admin: bool = False) -> Dict[str, Any]:
        """Update record by ID with RLS context"""
        if not use_admin and not self._auth_client:
            raise ExternalServiceError("No authenticated client set for RLS operation")
        
        client = self._auth_client if not use_admin else None
        return await super().update(record_id, data, use_admin, client)
    
    async def delete(self, record_id: str, use_admin: bool = False) -> bool:
        """Delete record by ID with RLS context"""
        if not use_admin and not self._auth_client:
            raise ExternalServiceError("No authenticated client set for RLS operation")
        
        client = self._auth_client if not use_admin else None
        return await super().delete(record_id, use_admin, client)
    
    async def list(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 20,
        offset: int = 0,
        order_by: str = "created_at",
        ascending: bool = False,
        use_admin: bool = False
    ) -> List[Dict[str, Any]]:
        """List records with RLS context"""
        if not use_admin and not self._auth_client:
            raise ExternalServiceError("No authenticated client set for RLS operation")
        
        client = self._auth_client if not use_admin else None
        return await super().list(filters, limit, offset, order_by, ascending, use_admin, client)
    
    async def count(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        use_admin: bool = False
    ) -> int:
        """Count records with RLS context"""
        if not use_admin and not self._auth_client:
            raise ExternalServiceError("No authenticated client set for RLS operation")
        
        client = self._auth_client if not use_admin else None
        return await super().count(filters, use_admin, client)


class RLSService:
    """Base service class that provides RLS-aware repositories"""
    
    def __init__(self, auth_client: Client = None):
        self._auth_client = auth_client
        self._repositories: Dict[str, RLSRepository] = {}
    
    def set_auth_client(self, client: Client):
        """Set the authenticated client for all repositories"""
        self._auth_client = client
        for repo in self._repositories.values():
            repo.set_auth_client(client)
    
    def get_repository(self, table_name: str) -> RLSRepository:
        """Get or create a repository for the given table"""
        if table_name not in self._repositories:
            repo = RLSRepository(table_name)
            if self._auth_client:
                repo.set_auth_client(self._auth_client)
            self._repositories[table_name] = repo
        return self._repositories[table_name]
    
    @property
    def users(self) -> RLSRepository:
        """Get users repository"""
        return self.get_repository("users")
    
    @property
    def organizations(self) -> RLSRepository:
        """Get organizations repository"""
        return self.get_repository("organizations")
    
    @property
    def leads(self) -> RLSRepository:
        """Get leads repository"""
        return self.get_repository("leads")
    
    @property
    def campaigns(self) -> RLSRepository:
        """Get campaigns repository"""
        return self.get_repository("campaigns")
    
    @property
    def email_templates(self) -> RLSRepository:
        """Get email_templates repository"""
        return self.get_repository("email_templates")
    
    @property
    def email_sends(self) -> RLSRepository:
        """Get email_sends repository"""
        return self.get_repository("email_sends")
    
    @property
    def sales_goals(self) -> RLSRepository:
        """Get sales_goals repository"""
        return self.get_repository("sales_goals")
    
    @property
    def agent_configurations(self) -> RLSRepository:
        """Get agent_configurations repository"""
        return self.get_repository("agent_configurations")
    
    @property
    def api_keys(self) -> RLSRepository:
        """Get api_keys repository"""
        return self.get_repository("api_keys")


def get_rls_service(auth_client: Client) -> RLSService:
    """Factory function to create RLS service with auth context"""
    return RLSService(auth_client)
