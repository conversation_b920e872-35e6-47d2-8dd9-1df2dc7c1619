#!/usr/bin/env python3
"""
Test Apollo.io batch enrichment functionality
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.leads.apollo_service import ApolloService

async def test_apollo_batch_enrichment():
    """Test Apollo.io batch enrichment with small sample"""
    print("🧪 Testing Apollo.io Batch Enrichment")
    print("=" * 40)
    
    try:
        apollo_service = ApolloService()
        
        # Test 1: Small people search to get some IDs
        print("\n1️⃣ Testing people search...")
        search_data = {
            "page": 1,
            "per_page": 5,  # Small sample
            "q_organization_locations": "London",
            "organization_industries": "restaurants"
        }
        
        resp = await apollo_service._request("POST", "/mixed_people/search", data=search_data)
        people = resp.get("people", [])
        
        print(f"   Found {len(people)} people")
        
        if not people:
            print("❌ No people found, cannot test enrichment")
            return False
        
        # Extract person IDs
        person_ids = [person.get("id") for person in people if person.get("id")]
        print(f"   Person IDs: {person_ids[:3]}..." if len(person_ids) > 3 else f"   Person IDs: {person_ids}")
        
        # Test 2: Batch enrichment
        print(f"\n2️⃣ Testing batch enrichment for {len(person_ids)} people...")
        enriched_people = await apollo_service.bulk_enrich_people(person_ids)
        
        print(f"   Enriched {len(enriched_people)} people")
        
        # Test 3: Check for real emails
        real_emails = []
        locked_emails = []
        
        for person in enriched_people:
            email = person.get("email", "")
            if email and "email_not_unlocked" not in email:
                real_emails.append(email)
            else:
                locked_emails.append(email)
        
        print(f"   Real emails: {len(real_emails)}")
        print(f"   Locked emails: {len(locked_emails)}")
        
        if real_emails:
            print(f"   ✅ Sample real emails: {real_emails[:2]}")
        
        # Test 4: Show sample enriched data
        if enriched_people:
            sample = enriched_people[0]
            print(f"\n3️⃣ Sample enriched person:")
            print(f"   Name: {sample.get('first_name', '')} {sample.get('last_name', '')}")
            print(f"   Company: {sample.get('organization', {}).get('name', 'N/A')}")
            print(f"   Title: {sample.get('title', 'N/A')}")
            print(f"   Email: {sample.get('email', 'N/A')}")
            print(f"   LinkedIn: {sample.get('linkedin_url', 'N/A')}")
        
        success = len(enriched_people) > 0
        print(f"\n📊 Test Result: {'✅ Success' if success else '❌ Failed'}")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_apollo_batch_enrichment()
    
    if success:
        print("\n🎉 Apollo.io batch enrichment is working!")
    else:
        print("\n⚠️ Apollo.io batch enrichment needs attention")

if __name__ == "__main__":
    asyncio.run(main())
