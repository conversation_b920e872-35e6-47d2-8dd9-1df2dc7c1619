"""
User models and schemas
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator
from app.shared.models import (
    UserRole, 
    SubscriptionTier, 
    TimestampMixin, 
    BaseResponse,
    PaginatedResponse
)


class UserPreferences(BaseModel):
    """User preferences model"""
    timezone: str = Field(default="UTC", description="User's timezone")
    language: str = Field(default="en", description="User's preferred language")
    email_notifications: bool = Field(default=True, description="Enable email notifications")
    push_notifications: bool = Field(default=True, description="Enable push notifications")
    theme: str = Field(default="light", description="UI theme preference")
    date_format: str = Field(default="MM/DD/YYYY", description="Date format preference")
    time_format: str = Field(default="12h", description="Time format preference")


class UserSettings(BaseModel):
    """User settings model"""
    preferences: UserPreferences = Field(default_factory=UserPreferences)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)


class UserProfileUpdate(BaseModel):
    """User profile update model"""
    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    phone: Optional[str] = Field(None, max_length=20)
    avatar_url: Optional[str] = None
    bio: Optional[str] = Field(None, max_length=500)
    settings: Optional[UserSettings] = None


class UserProfileResponse(BaseModel):
    """User profile response model"""
    id: str
    email: EmailStr
    first_name: str
    last_name: str
    role: UserRole
    is_active: bool
    email_verified: bool
    organization_id: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    settings: UserSettings = Field(default_factory=UserSettings)
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserListItem(BaseModel):
    """User list item model for organization members"""
    id: str
    email: EmailStr
    first_name: str
    last_name: str
    role: UserRole
    is_active: bool
    email_verified: bool
    last_login: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class OrganizationMember(BaseModel):
    """Organization member model"""
    user_id: str
    organization_id: str
    role: UserRole
    user: UserListItem
    joined_at: datetime
    
    class Config:
        from_attributes = True


class OrganizationMembershipUpdate(BaseModel):
    """Organization membership update model"""
    role: UserRole


class InviteUserRequest(BaseModel):
    """Invite user to organization request"""
    email: EmailStr
    role: UserRole = UserRole.USER
    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    message: Optional[str] = Field(None, max_length=500)


class InviteUserResponse(BaseResponse):
    """Invite user response"""
    invitation_id: Optional[str] = None
    expires_at: Optional[datetime] = None


class UserActivityLog(BaseModel):
    """User activity log model"""
    id: str
    user_id: str
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    details: Dict[str, Any] = Field(default_factory=dict)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class UserStatsResponse(BaseModel):
    """User statistics response"""
    total_campaigns: int = 0
    active_campaigns: int = 0
    total_leads: int = 0
    emails_sent: int = 0
    response_rate: float = 0.0
    last_activity: Optional[datetime] = None


class UsersListResponse(PaginatedResponse):
    """Users list response with pagination"""
    items: List[UserListItem]


class UserSearchParams(BaseModel):
    """User search parameters"""
    query: Optional[str] = Field(None, description="Search by name or email")
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    organization_id: Optional[str] = None


class ChangeUserRoleRequest(BaseModel):
    """Change user role request"""
    role: UserRole


class ChangeUserStatusRequest(BaseModel):
    """Change user status request"""
    is_active: bool
    reason: Optional[str] = Field(None, max_length=200)


class UserPasswordChangeRequest(BaseModel):
    """User password change request (admin action)"""
    new_password: str = Field(..., min_length=8, max_length=100)
    force_password_reset: bool = Field(default=False, description="Force user to change password on next login")
    
    @validator('new_password')
    def validate_password_strength(cls, v):
        # Import here to avoid circular imports
        from app.modules.auth.utils import validate_password_strength
        if not validate_password_strength(v):
            raise ValueError('Password does not meet strength requirements')
        return v


class UserDeletionRequest(BaseModel):
    """User deletion request"""
    confirm_email: EmailStr
    reason: Optional[str] = Field(None, max_length=500)
    transfer_data_to: Optional[str] = Field(None, description="User ID to transfer data to")


class BulkUserAction(BaseModel):
    """Bulk user action model"""
    user_ids: List[str] = Field(..., min_items=1, max_items=100)
    action: str = Field(..., description="Action to perform: activate, deactivate, delete")
    reason: Optional[str] = Field(None, max_length=200)


class UserExportRequest(BaseModel):
    """User export request"""
    format: str = Field(default="csv", description="Export format: csv, xlsx")
    include_inactive: bool = Field(default=False)
    include_personal_data: bool = Field(default=True)
    filters: Optional[UserSearchParams] = None


# Response models
class UserProfileUpdateResponse(BaseResponse):
    """User profile update response"""
    user: UserProfileResponse


class UserRoleChangeResponse(BaseResponse):
    """User role change response"""
    user_id: str
    new_role: UserRole


class UserStatusChangeResponse(BaseResponse):
    """User status change response"""
    user_id: str
    is_active: bool


class UserDeletionResponse(BaseResponse):
    """User deletion response"""
    user_id: str
    deleted_at: datetime


class BulkUserActionResponse(BaseResponse):
    """Bulk user action response"""
    processed_count: int
    failed_count: int
    errors: List[Dict[str, Any]] = Field(default_factory=list)


class UserExportResponse(BaseResponse):
    """User export response"""
    download_url: str
    expires_at: datetime
    record_count: int
