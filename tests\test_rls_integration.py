"""
Test RLS (Row Level Security) integration with Supabase
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from jose import jwt

from app.main import app
from app.core.config import settings
from app.core.supabase_auth import supabase_auth_manager
from app.shared.dependencies import get_supabase_client_with_auth


class TestRLSIntegration:
    """Test RLS integration with authentication"""
    
    def setup_method(self):
        """Setup test data"""
        self.client = TestClient(app)
        self.test_user = {
            "id": "test-user-id-123",
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User",
            "role": "user",
            "organization_id": "test-org-id-123",
            "is_active": True
        }
    
    def test_create_supabase_compatible_jwt(self):
        """Test creating Supabase-compatible JWT tokens"""
        # Create auth session
        auth_session = supabase_auth_manager.create_auth_session(self.test_user)
        
        # Verify the structure
        assert "access_token" in auth_session
        assert "refresh_token" in auth_session
        assert "token_type" in auth_session
        assert "expires_in" in auth_session
        assert "user" in auth_session
        
        # Decode and verify the access token
        access_token = auth_session["access_token"]
        payload = jwt.decode(
            access_token,
            settings.jwt_secret_key,
            algorithms=["HS256"],
            audience="authenticated"
        )
        
        # Verify Supabase-specific fields
        assert payload["iss"] == "supabase"
        assert payload["ref"] == "aqjctgvrvtogryxlvwor"
        assert payload["role"] == "authenticated"
        assert payload["aud"] == "authenticated"
        assert payload["sub"] == self.test_user["id"]
        assert payload["email"] == self.test_user["email"]
        
        # Verify user metadata
        assert "user_metadata" in payload
        assert payload["user_metadata"]["email"] == self.test_user["email"]
        assert payload["user_metadata"]["first_name"] == self.test_user["first_name"]
        assert payload["user_metadata"]["last_name"] == self.test_user["last_name"]
    
    @patch('app.shared.dependencies.create_client')
    def test_supabase_client_with_auth(self, mock_create_client):
        """Test that Supabase client is properly configured with auth context"""
        # Create a mock client
        mock_client = Mock()
        mock_auth = Mock()
        mock_client.auth = mock_auth
        mock_create_client.return_value = mock_client
        
        # Create auth session and token
        auth_session = supabase_auth_manager.create_auth_session(self.test_user)
        access_token = auth_session["access_token"]
        
        # Create mock credentials
        from fastapi.security import HTTPAuthorizationCredentials
        mock_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials=access_token
        )
        
        # Test the dependency
        client = get_supabase_client_with_auth(mock_credentials)
        
        # Verify client creation
        mock_create_client.assert_called_once_with(
            settings.supabase_url,
            settings.supabase_anon_key
        )
        
        # Verify auth session was set
        mock_auth.set_session.assert_called_once_with(access_token, "")
        
        assert client == mock_client
    
    def test_invalid_token_handling(self):
        """Test handling of invalid tokens"""
        from fastapi.security import HTTPAuthorizationCredentials
        from app.core.exceptions import AuthenticationError
        
        # Test with invalid token
        invalid_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="invalid-token"
        )
        
        with pytest.raises(AuthenticationError):
            get_supabase_client_with_auth(invalid_credentials)
    
    def test_token_without_required_fields(self):
        """Test token without required Supabase fields"""
        from fastapi.security import HTTPAuthorizationCredentials
        from app.core.exceptions import AuthenticationError
        
        # Create token without required fields
        invalid_payload = {
            "sub": "user-id",
            "email": "<EMAIL>",
            "aud": "wrong-audience",  # Wrong audience
            "exp": 9999999999
        }
        
        invalid_token = jwt.encode(
            invalid_payload,
            settings.jwt_secret_key,
            algorithm="HS256"
        )
        
        invalid_credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials=invalid_token
        )
        
        with pytest.raises(AuthenticationError, match="wrong audience"):
            get_supabase_client_with_auth(invalid_credentials)
    
    @patch('app.modules.auth.service.supabase_service')
    def test_auth_service_integration(self, mock_supabase_service):
        """Test that auth service creates proper tokens"""
        from app.modules.auth.service import auth_service
        from app.modules.auth.models import LoginRequest
        
        # Mock database responses
        mock_repo = Mock()
        mock_supabase_service.users_repo = mock_repo
        
        # Mock user data
        mock_user = {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "password_hash": "$2b$12$test_hash",
            "first_name": "Test",
            "last_name": "User",
            "role": "user",
            "is_active": True,
            "organization_id": "test-org-id"
        }
        
        # Mock repository methods
        async def mock_get_user_by_email(email):
            return mock_user if email == "<EMAIL>" else None
        
        async def mock_update(user_id, data, use_admin=False):
            return mock_user
        
        auth_service._get_user_by_email = mock_get_user_by_email
        auth_service.users_repo.update = mock_update
        
        # Mock password verification
        with patch('app.modules.auth.service.verify_password', return_value=True):
            # Test login
            login_request = LoginRequest(
                email="<EMAIL>",
                password="test_password"
            )
            
            # This should work without errors
            result = asyncio.run(auth_service.login(login_request))
            
            # Verify result structure
            assert "access_token" in result
            assert "refresh_token" in result
            assert "user" in result
            
            # Verify the access token is Supabase-compatible
            access_token = result["access_token"]
            payload = jwt.decode(
                access_token,
                settings.jwt_secret_key,
                algorithms=["HS256"],
                audience="authenticated"
            )
            
            assert payload["aud"] == "authenticated"
            assert payload["role"] == "authenticated"
            assert payload["iss"] == "supabase"


if __name__ == "__main__":
    pytest.main([__file__])
