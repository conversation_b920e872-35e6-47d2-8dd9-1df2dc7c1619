#!/usr/bin/env python3
"""
Test Apollo.io configuration and API connection
"""

import os
import sys
import asyncio

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_apollo_config():
    """Test Apollo.io configuration"""
    print("🧪 Testing Apollo.io Configuration")
    print("=" * 40)
    
    # Check environment variable
    apollo_key = os.getenv('APOLLO_API_KEY')
    if apollo_key:
        print(f"✅ APOLLO_API_KEY found (length: {len(apollo_key)})")
        
        # Test Apollo.io service initialization
        try:
            from app.modules.leads.apollo_service import ApolloService
            
            apollo_service = ApolloService()
            print("✅ Apollo.io service initialized successfully")
            
            # Test authentication
            print("\n🔐 Testing Apollo.io authentication...")
            auth_result = await apollo_service.authenticate()
            
            if auth_result:
                print("✅ Apollo.io authentication successful!")
                return True
            else:
                print("❌ Apollo.io authentication failed")
                return False
                
        except Exception as e:
            print(f"❌ Apollo.io service error: {e}")
            return False
    else:
        print("❌ APOLLO_API_KEY not found in environment variables")
        print("\n💡 To set up Apollo.io:")
        print("   1. Get your API key from https://app.apollo.io/settings/integrations")
        print("   2. Set environment variable: APOLLO_API_KEY=your_key_here")
        print("   3. Or add it to your .env file")
        return False

async def main():
    success = await test_apollo_config()
    
    if success:
        print("\n🚀 Apollo.io is ready for lead sourcing!")
    else:
        print("\n⚠️  Apollo.io needs to be configured before running lead sourcing tests")

if __name__ == "__main__":
    asyncio.run(main())
