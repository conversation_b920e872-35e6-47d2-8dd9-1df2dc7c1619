#!/usr/bin/env python3
"""
Test script to verify enhanced goal understanding with email personalization
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_enhanced_goal_understanding():
    """Test the enhanced goal understanding with email personalization fields"""
    print("🧪 Testing Enhanced Goal Understanding for Email Personalization")
    print("="*70)
    
    # Test cases with different levels of detail
    test_cases = [
        {
            "name": "Basic Goal (should ask follow-ups)",
            "goal": "I need 1 meeting with hospital owner in london"
        },
        {
            "name": "Detailed Goal (should ask fewer follow-ups)",
            "goal": "I need 5 meetings with restaurant owners in London to sell our AI-powered POS system that reduces checkout time by 50% and saves 30% on operational costs. Our company TechFlow specializes in restaurant automation solutions."
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 Test Case: {test_case['name']}")
        print(f"   Goal: {test_case['goal']}")
        print("-" * 50)
        
        try:
            # Simulate the goal understanding process
            from app.modules.agents.goal_understanding import GoalUnderstandingAgent
            
            goal_agent = GoalUnderstandingAgent("test-org-uuid")
            
            test_input = {
                "goal_description": test_case["goal"],
                "context": "Test context",
                "user_id": "test-user-uuid",
                "organization_id": "test-org-uuid",
                "conversation_history": [],
                "is_follow_up": False,
                "answered_questions": {}
            }
            
            # Test the enhanced parsing
            parsed_goal = await goal_agent._parse_goal_enhanced(test_input)
            
            print(f"   ✅ Parsed Goal:")
            print(f"      🎯 Objective: {parsed_goal.get('objective', 'N/A')}")
            print(f"      🏭 Product/Service: {parsed_goal.get('product_or_service', 'N/A')}")
            print(f"      💡 Value Proposition: {parsed_goal.get('value_proposition', 'N/A')}")
            print(f"      🔧 Pain Points: {parsed_goal.get('pain_points', [])}")
            print(f"      🏢 Company Info: {parsed_goal.get('company_info', {})}")
            print(f"      🎯 Apollo Keywords: {parsed_goal.get('apollo_keywords', {}).get('primary_keywords', 'N/A')}")
            
            # Check missing information
            missing_info = parsed_goal.get('missing_information', [])
            print(f"      ❓ Missing Info: {missing_info}")
            
            # Test follow-up question generation
            if missing_info:
                questions = await goal_agent._generate_follow_up_questions(parsed_goal, missing_info)
                print(f"      💬 Follow-up Questions ({len(questions)}):")
                for i, question in enumerate(questions, 1):
                    print(f"         {i}. {question}")
            else:
                print(f"      ✅ No follow-up questions needed!")
                
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*70)
    print("🎯 Summary of Enhanced Features:")
    print("   ✅ Value proposition extraction")
    print("   ✅ Pain points identification") 
    print("   ✅ Company information capture")
    print("   ✅ Enhanced follow-up questions")
    print("   ✅ Generic keyword generation")
    print("   ✅ Email personalization data collection")
    print("\n✅ Enhanced goal understanding test completed!")

if __name__ == "__main__":
    asyncio.run(test_enhanced_goal_understanding())
