# Selda AI Sales Autopilot - Backend Implementation Plan

**Architecture**: Modular Monolith  
**AI Framework**: CrewAI  
**Lead Generation**: Apollo.io  
**Configuration**: Environment variables via .env file

## 0. Prerequisites & Initial Setup

### Task 0.1: Development Environment Setup

**Priority**: Critical  
**Estimated Time**: 2-4 hours  
**Description**: Set up complete development environment with all required tools  
**Deliverables**:

- [ ] Install Python 3.11+
- [ ] Install FastAPI and dependencies
- [ ] Install CrewAI framework
- [ ] Install Docker (optional for local development)
- [ ] Install Supabase CLI
- [ ] Install Redis for Celery
- [ ] Setup virtual environment
- [ ] Create initial project structure

### Task 0.2: Version Control & Project Structure ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 1-2 hours
**Description**: Initialize Git repository and establish modular monolith structure
**Deliverables**:

- [x] Initialize Git repository
- [x] Create .gitignore for Python/FastAPI
- [x] Establish modular monolith folder structure:
  ```
  backend/
  ├── app/
  │   ├── modules/
  │   │   ├── auth/
  │   │   ├── users/
  │   │   ├── campaigns/
  │   │   ├── leads/
  │   │   ├── agents/
  │   │   ├── payments/
  │   │   └── analytics/
  │   ├── core/
  │   ├── shared/
  │   └── main.py
  ├── tests/
  ├── migrations/
  └── requirements.txt
  ```

### Task 0.3: Environment Configuration Setup ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 1 hour
**Description**: Create .env configuration system for all secrets and API keys
**Deliverables**:

- [x] Create .env.example template
- [x] Setup Pydantic Settings for environment management
- [x] Document all required environment variables:
  - SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_KEY
  - OPENAI_API_KEY
  - APOLLO_API_KEY
  - STRIPE_PUBLISHABLE_KEY, STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET
  - SENDGRID_API_KEY (or chosen ESP)
  - GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET (for Calendar)
  - LINKEDIN_CLIENT_ID, LINKEDIN_CLIENT_SECRET
  - REDIS_URL
  - JWT_SECRET_KEY

## Phase 1: Foundation & Core Infrastructure (Weeks 1-2)

### Task 1.1: Supabase Setup & Database Schema ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 6-8 hours
**Description**: Initialize Supabase project and design complete database schema
**Deliverables**:

- [x] Create Supabase project (SQL schema created)
- [x] Design and implement database schema:
  - users (id, email, password_hash, created_at, updated_at)
  - organizations (id, name, owner_id, subscription_tier, created_at)
  - user_organizations (user_id, organization_id, role)
  - sales_goals (id, organization_id, title, description, target_metrics, status)
  - leads (id, organization_id, email, name, company, title, source, score, status)
  - campaigns (id, organization_id, goal_id, name, status, settings, created_at)
  - email_templates (id, organization_id, name, subject, body, variables)
  - email_sends (id, campaign_id, lead_id, template_id, sent_at, status)
  - email_interactions (id, send_id, type, timestamp, data)
  - agent_configurations (id, organization_id, agent_type, settings)
  - api_keys (id, organization_id, service, encrypted_key, created_at)
- [x] Create initial Supabase migrations
- [x] Setup Row Level Security (RLS) policies

### Task 1.2: FastAPI Modular Monolith Foundation ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 8-10 hours
**Description**: Create FastAPI application with modular monolith architecture
**Deliverables**:

- [x] Setup FastAPI main application
- [x] Create shared utilities and dependencies
- [x] Implement dependency injection system
- [x] Create base models and schemas using Pydantic
- [x] Setup CORS and middleware
- [x] Create health check endpoints
- [x] Implement modular routing system

### Task 1.3: Authentication Module ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 6-8 hours
**Description**: Implement complete authentication system with JWT
**Deliverables**:

- [x] Create auth module structure (app/modules/auth/)
- [x] Implement user registration with email verification
- [x] Implement login with JWT token generation
- [x] Implement token refresh mechanism
- [x] Implement password reset functionality
- [x] Create password hashing utilities (bcrypt)
- [x] Implement JWT middleware for protected routes
- [x] Create auth dependencies for route protection
- [x] Add rate limiting for auth endpoints

### Task 1.4: Users Module ✅ COMPLETED

**Priority**: High
**Estimated Time**: 4-6 hours
**Description**: User management and profile functionality
**Deliverables**:

- [x] Create users module structure (app/modules/users/)
- [x] Implement user CRUD operations
- [x] Create user profile management endpoints
- [x] Implement organization membership management
- [x] Create user preferences and settings
- [ ] Add user activity logging _(Partial: Logging method exists, but DB storage is TODO)_

### Task 1.5: Stripe Integration Module ✅ COMPLETED

**Priority**: High
**Estimated Time**: 8-10 hours
**Description**: Complete payment and subscription management
**Deliverables**:

- [x] Create payments module structure (app/modules/payments/)
- [x] Integrate Stripe SDK
- [x] Define subscription plans in Stripe Dashboard
- [x] Implement checkout session creation
- [x] Create webhook handler for Stripe events
- [x] Implement subscription management endpoints
- [x] Add billing history and invoice management
- [x] Create usage tracking for subscription limits _(Partial: Models exist, may need more logic)_

## Phase 2: CrewAI Integration & Core Agents (Weeks 2-4)

### Task 2.1: CrewAI Framework Integration ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 6-8 hours
**Description**: Setup CrewAI framework and create agent infrastructure
**Deliverables**:

- [x] Create agents module structure (app/modules/agents/)
- [x] Integrate CrewAI framework
- [x] Setup OpenAI GPT-4 integration
- [x] Create base agent classes and interfaces
- [x] Implement agent configuration management
- [x] Create agent execution pipeline
- [x] Setup agent communication and coordination
- [x] Implement agent state management

### Task 2.2: Goal Understanding Agent (Agent 1) ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 8-10 hours
**Description**: Agent for interpreting and clarifying sales goals
**Deliverables**:

- [x] Create GoalUnderstandingAgent class
- [x] Implement NLP pipeline for goal interpretation
- [x] Create intent recognition system
- [x] Implement parameter extraction logic
- [x] Build follow-up question generation
- [x] Create goal validation and storage
- [x] Implement goal refinement workflow
- [x] Add goal progress tracking setup

### Task 2.3: Apollo.io Integration Service ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 6-8 hours
**Description**: Complete Apollo.io API integration for lead generation
**Deliverables**:

- [x] Create Apollo.io service class
- [x] Implement authentication with Apollo.io API
- [x] Create lead search functionality
- [x] Implement company search and enrichment
- [x] Add contact enrichment features
- [x] Create rate limiting and error handling
- [x] Implement data mapping to internal lead schema _(basic mapping, can be extended)_
- [x] Add Apollo.io webhook handling (if available) _(not available in public API, so N/A)_

### Task 2.4: Strategic Planning Agent (Agent 2) ✅ COMPLETED

**Priority**: Critical
**Estimated Time**: 10-12 hours
**Description**: Agent for lead research and campaign planning
**Deliverables**:

- [x] Create StrategicPlanningAgent class
- [x] Implement lead generation logic using Apollo.io
- [x] Create lead scoring algorithm
- [x] Implement market research capabilities
- [x] Build campaign planning logic
- [ ] Create volume calculation and timing strategy
- [ ] Implement competitor analysis (basic)
- [ ] Add campaign optimization recommendations

### Task 2.5: Content Creation Agent (Agent 3) ✅ COMPLETED

**Priority**: High
**Estimated Time**: 8-10 hours
**Description**: Agent for dynamic, personalized content/email generation
**Deliverables**:

- [x] Create ContentCreationAgent class
- [x] Implement dynamic, personalized email/content generation using LLM
- [x] Add A/B testing (multiple content variants)
- [x] Enforce brand voice and guidelines
- [x] Return subject line, content, personalization variables, content score, and suggestions

### Task 2.6: Background Task Processing Setup

**Priority**: High
**Estimated Time**: 4-6 hours
**Description**: Setup Celery and Redis for asynchronous processing
**Deliverables**:

- [ ] Install and configure Redis
- [ ] Setup Celery worker configuration
- [ ] Create task queue management
- [ ] Implement task monitoring and logging
- [ ] Create retry logic for failed tasks
- [ ] Setup periodic tasks with Celery Beat
- [ ] Add task result storage and retrieval
- [ ] Implement task priority queues

## Phase 3: Lead Sourcing & CRM Enhancement

### Task 3.1: Enhance Lead Model and Database Schema

**Priority**: Critical
**Description**: Update the database schema and data models to support advanced lead management.
**Deliverables**:

- [ ] Create a new database migration for the `leads` table.
- [ ] The `leads` table will include the following fields:
  - `id` (Primary Key)
  - `company_id` (TEXT, for external IDs)
  - `name` (TEXT)
  - `industry` (TEXT)
  - `location` (TEXT)
  - `source` (TEXT, e.g., "Apollo", "LinkedIn", "YTJ")
  - `contact_email` (TEXT, unique)
  - `contact_name` (TEXT)
  - `status` (ENUM: "unused", "reserved", "contacted", "blacklisted", "cooldown")
  - `used_by_campaigns` (JSONB or TEXT[], to store campaign IDs)
  - `contact_quality_score` (INTEGER)
  - `last_contacted_at` (TIMESTAMP)
- [ ] Define the `Lead` Pydantic model in `app/modules/leads/models.py`.

### Task 3.2: Implement Lead Sourcing Services

**Priority**: High
**Description**: Implement services to gather leads from various sources.
**Deliverables**:

- [ ] Enhance `apollo_service.py` to fetch leads based on campaign filters (Location, Industry, Company Attributes).
- [ ] Create placeholder services for other lead sources:
  - LinkedIn (Sales Navigator)
  - Local directories (e.g., YTJ, Handelsregister)
- [ ] Implement logic to add leads to the internal user-enriched database.

### Task 3.3: Implement Lead Management Logic

**Priority**: Critical
**Description**: Build the business logic for lead status, deduplication, and quality control.
**Deliverables**:

- [ ] Create `app/modules/leads/service.py` to handle all lead-related business logic.
- [ ] Implement lead status transition logic ("unused" -> "reserved" -> "contacted").
- [ ] Implement a "cooldown" status for leads used more than 3 times.
- [ ] Implement a blacklisting mechanism for bounced or unsubscribed leads.
- [ ] Add logic to prevent contacting a domain that has been contacted in the last 90 days.
- [ ] Develop a `Contact Quality Score` based on interaction history (initial implementation).

### Task 3.4: Develop Lead Management API

**Priority**: High
**Description**: Expose lead management functionalities through a REST API.
**Deliverables**:

- [ ] Create/update `app/modules/leads/router.py`.
- [ ] Develop API endpoints for:
  - Sourcing new leads for a campaign.
  - Retrieving a list of available leads based on filters.
  - Manually adding/updating a lead.
  - Blacklisting a lead.
- [ ] Secure endpoints using the existing authentication middleware.

## Phase 4: Monitoring, Analytics & Optimization (Weeks 8-10)

### Task 4.1: Performance Monitoring Agent (Agent 7)

**Priority**: Critical
**Estimated Time**: 10-12 hours
**Description**: Real-time analytics and autonomous optimization
**Deliverables**:

- [ ] Create PerformanceMonitoringAgent class
- [ ] Implement real-time analytics tracking
- [ ] Create performance metrics dashboard data
- [ ] Build autonomous optimization logic
- [ ] Implement goal progress tracking
- [ ] Create performance alerts and notifications
- [ ] Add predictive analytics (basic)
- [ ] Implement ROI calculation and reporting
- [ ] Create performance benchmarking

### Task 4.2: Analytics Module

**Priority**: High
**Estimated Time**: 8-10 hours
**Description**: Comprehensive analytics and reporting system
**Deliverables**:

- [ ] Create analytics module structure (app/modules/analytics/)
- [ ] Implement data aggregation pipelines
- [ ] Create real-time metrics endpoints
- [ ] Build custom report generation
- [ ] Implement data export functionality
- [ ] Add comparative analytics (time periods, campaigns)
- [ ] Create funnel analysis and conversion tracking
- [ ] Implement cohort analysis
- [ ] Add predictive modeling capabilities

### Task 4.3: System Monitoring & Logging

**Priority**: High
**Estimated Time**: 6-8 hours
**Description**: Comprehensive system monitoring and error handling
**Deliverables**:

- [ ] Implement structured logging across all modules
- [ ] Create centralized error handling and reporting
- [ ] Setup application performance monitoring
- [ ] Implement health checks for all services
- [ ] Create system alerts and notifications
- [ ] Add database query optimization monitoring
- [ ] Implement security event logging
- [ ] Create system metrics dashboard endpoints

## Phase 5: Security, Testing & Deployment (Weeks 10-12)

### Task 5.1: Security Hardening

**Priority**: Critical
**Estimated Time**: 8-10 hours
**Description**: Complete security review and hardening
**Deliverables**:

- [ ] Implement input validation and sanitization
- [ ] Add SQL injection prevention
- [ ] Create XSS protection mechanisms
- [ ] Implement CSRF protection
- [ ] Add rate limiting to all endpoints
- [ ] Create API key rotation system
- [ ] Implement data encryption at rest
- [ ] Add audit logging for sensitive operations
- [ ] Perform security vulnerability scanning

### Task 5.2: Testing Framework

**Priority**: Critical
**Estimated Time**: 12-15 hours
**Description**: Comprehensive testing suite
**Deliverables**:

- [ ] Setup pytest testing framework
- [ ] Create unit tests for all modules
- [ ] Implement integration tests for agents
- [ ] Add API endpoint testing
- [ ] Create database testing with fixtures
- [ ] Implement mock services for external APIs
- [ ] Add performance and load testing
- [ ] Create end-to-end testing scenarios
- [ ] Setup continuous integration testing

### Task 5.3: API Documentation

**Priority**: High
**Estimated Time**: 4-6 hours
**Description**: Complete API documentation and developer resources
**Deliverables**:

- [ ] Generate OpenAPI/Swagger documentation
- [ ] Create API usage examples
- [ ] Document authentication flows
- [ ] Add webhook documentation
- [ ] Create SDK/client library documentation
- [ ] Implement API versioning strategy
- [ ] Add rate limiting documentation
- [ ] Create troubleshooting guides

### Task 5.4: Deployment Preparation

**Priority**: Critical
**Estimated Time**: 8-10 hours
**Description**: Production deployment setup and configuration
**Deliverables**:

- [ ] Create Docker containerization
- [ ] Setup CI/CD pipeline (GitHub Actions)
- [ ] Create production environment configuration
- [ ] Implement database migration strategy
- [ ] Setup monitoring and alerting in production
- [ ] Create backup and recovery procedures
- [ ] Implement blue-green deployment strategy
- [ ] Add production security configurations
- [ ] Create deployment documentation

## Phase 6: Post-Launch & Iteration (Ongoing)

### Task 6.1: Production Monitoring

**Priority**: Critical
**Estimated Time**: Ongoing
**Description**: Monitor production system and performance
**Deliverables**:

- [ ] Monitor system performance and uptime
- [ ] Track user engagement and feature usage
- [ ] Monitor API rate limits and quotas
- [ ] Track subscription and billing metrics
- [ ] Monitor agent performance and accuracy
- [ ] Review security logs and incidents
- [ ] Track customer support requests
- [ ] Monitor third-party service integrations

### Task 6.2: Feature Enhancement & Optimization

**Priority**: Medium
**Estimated Time**: Ongoing
**Description**: Continuous improvement based on user feedback
**Deliverables**:

- [ ] Gather and analyze user feedback
- [ ] Implement feature requests and improvements
- [ ] Optimize agent performance and accuracy
- [ ] Enhance user experience and interface
- [ ] Add new integrations and capabilities
- [ ] Improve system scalability and performance
- [ ] Update documentation and training materials
- [ ] Conduct regular security audits

# Next Steps

- Complete user activity logging (DB storage for audit logs)
- Finish Stripe webhook event logic (DB/email updates)
- Implement Apollo.io integration service
- Implement Strategic Planning Agent and related logic
- Expand CrewAI workflows and add more agents as needed

---

## Summary

**Total Estimated Development Time**: 10-12 weeks
**Architecture**: Modular Monolith with FastAPI
**AI Framework**: CrewAI with OpenAI GPT-4
**Lead Generation**: Apollo.io integration
**Key Technologies**: FastAPI, Supabase, Redis, Celery, Stripe, SendGrid

**Critical Path Dependencies**:

1. Foundation setup → Authentication → CrewAI integration
2. Apollo.io integration → Lead management → Campaign system
3. Email automation → Reply analysis → Performance monitoring
4. Testing → Security hardening → Deployment

**Risk Mitigation**:

- All API keys and secrets managed via .env configuration
- Modular architecture allows independent development and testing
- Comprehensive error handling and logging throughout
- Security-first approach with regular audits
- Extensive testing coverage before deployment
