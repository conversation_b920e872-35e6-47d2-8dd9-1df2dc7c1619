#!/usr/bin/env python3
"""
Test script for the new goal-to-workflow functionality
Tests the enhanced workflow that starts from sales goals with follow-ups
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "testpassword123"

class GoalWorkflowTester:
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=BASE_URL)
        self.token = None
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def authenticate(self):
        """Authenticate and get access token"""
        try:
            # Try to login first
            login_data = {
                "username": TEST_USER_EMAIL,
                "password": TEST_USER_PASSWORD
            }
            
            response = await self.client.post("/api/v1/auth/login", data=login_data)
            
            if response.status_code == 200:
                result = response.json()
                self.token = result["access_token"]
                print(f"✅ Authenticated successfully")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_headers(self):
        """Get headers with authentication"""
        return {"Authorization": f"Bearer {self.token}"}
    
    async def test_goal_workflow_complete(self):
        """Test workflow with a complete goal (no follow-ups needed)"""
        print("\n🧪 Testing complete goal workflow...")
        
        goal_request = {
            "sales_goal": "I need 10 restaurant clients in London for our POS system",
            "context": "We provide modern point-of-sale systems for restaurants"
        }
        
        try:
            response = await self.client.post(
                "/api/v1/workflow/start-from-goal",
                json=goal_request,
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Goal workflow response: {result['message']}")
                print(f"   Complete: {result['is_complete']}")
                print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
                
                if result['is_complete']:
                    print(f"   Workflow ID: {result.get('workflow_id', 'Not found')}")
                    return result
                else:
                    print(f"   Follow-up questions: {result.get('follow_up_questions', [])}")
                    return result
            else:
                print(f"❌ Goal workflow failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Goal workflow error: {e}")
            return None
    
    async def test_goal_workflow_with_followup(self):
        """Test workflow with incomplete goal requiring follow-ups"""
        print("\n🧪 Testing goal workflow with follow-ups...")
        
        # Start with incomplete goal
        goal_request = {
            "sales_goal": "I want 5 new clients for my business",
            "context": "We need to grow our customer base"
        }
        
        try:
            response = await self.client.post(
                "/api/v1/workflow/start-from-goal",
                json=goal_request,
                headers=self.get_headers()
            )
            
            if response.status_code != 200:
                print(f"❌ Initial goal request failed: {response.status_code}")
                return None
            
            result = response.json()
            print(f"✅ Initial goal processed: {result['message']}")
            print(f"   Complete: {result['is_complete']}")
            
            if not result['is_complete']:
                conversation_id = result['conversation_id']
                questions = result.get('follow_up_questions', [])
                print(f"   Follow-up questions: {questions}")
                
                # Answer follow-up questions
                answers = {
                    "industry": "restaurants",
                    "location": "London, UK"
                }
                
                followup_request = {
                    "conversation_id": conversation_id,
                    "answers": answers
                }
                
                print(f"   Answering follow-up questions: {answers}")
                
                response = await self.client.post(
                    "/api/v1/workflow/answer-follow-up",
                    json=followup_request,
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    final_result = response.json()
                    print(f"✅ Follow-up processed: {final_result['message']}")
                    print(f"   Complete: {final_result['is_complete']}")
                    
                    if final_result['is_complete']:
                        print(f"   Workflow ID: {final_result.get('workflow_id', 'Not found')}")
                    
                    return final_result
                else:
                    print(f"❌ Follow-up failed: {response.status_code}")
                    return None
            
            return result
            
        except Exception as e:
            print(f"❌ Follow-up workflow error: {e}")
            return None
    
    async def test_workflow_status(self, workflow_id: str):
        """Test workflow status monitoring"""
        if not workflow_id:
            return
            
        print(f"\n🧪 Testing workflow status for {workflow_id}...")
        
        try:
            response = await self.client.get(
                f"/api/v1/campaigns/workflow/{workflow_id}/status",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                status = response.json()
                print(f"✅ Workflow status: {status['status']}")
                print(f"   Current stage: {status['current_stage']}")
                print(f"   Progress: {status['progress_percentage']:.1f}%")
                print(f"   Stages completed: {status['stages_completed']}/{status['total_stages']}")
                return status
            else:
                print(f"❌ Status check failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Status check error: {e}")
            return None
    
    async def run_all_tests(self):
        """Run all goal workflow tests"""
        print("🚀 Starting Goal Workflow Tests")
        print("=" * 50)
        
        # Authenticate
        if not await self.authenticate():
            return False
        
        # Test 1: Complete goal workflow
        complete_result = await self.test_goal_workflow_complete()
        
        # Test 2: Goal workflow with follow-ups
        followup_result = await self.test_goal_workflow_with_followup()
        
        # Test 3: Monitor workflow status
        if complete_result and complete_result.get('workflow_id'):
            await self.test_workflow_status(complete_result['workflow_id'])
        elif followup_result and followup_result.get('workflow_id'):
            await self.test_workflow_status(followup_result['workflow_id'])
        
        print("\n✅ All goal workflow tests completed!")
        return True


async def main():
    """Main test function"""
    async with GoalWorkflowTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
