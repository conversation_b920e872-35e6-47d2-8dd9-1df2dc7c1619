#!/usr/bin/env python3
"""
Quick test with proper UUIDs
"""

import sys
import asyncio
import uuid

# Add the app directory to the path
sys.path.append('.')

async def test_with_proper_uuids():
    """Test with proper UUIDs"""
    try:
        print("🧪 Testing with proper UUIDs...")
        
        # Generate proper UUIDs
        org_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        print(f"Organization ID: {org_id}")
        print(f"User ID: {user_id}")
        
        # Import and test
        from app.modules.agents.goal_understanding import GoalUnderstandingAgent
        
        # Create agent with proper UUID
        agent = GoalUnderstandingAgent(org_id)
        print(f"✅ Agent created: {agent.name}")
        
        # Test with simple goal
        goal = "I want 1 restaurant client in London"
        print(f"Testing goal: {goal}")
        
        # Execute
        result = await agent.execute({
            "goal_description": goal,
            "user_id": user_id,
            "organization_id": org_id,
            "conversation_history": [],
            "is_follow_up": False
        })
        
        print(f"✅ Success!")
        print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
        print(f"   Complete: {result.get('is_complete', False)}")
        print(f"   Objective: {result.get('parsed_goal', {}).get('objective', 'Not found')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    await test_with_proper_uuids()

if __name__ == "__main__":
    asyncio.run(main())
