-- Selda AI Sales Autopilot - Initial Database Schema
-- This file contains the complete database schema for the application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> custom types
CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'pro', 'enterprise');
CREATE TYPE user_role AS ENUM ('user', 'admin', 'owner');
CREATE TYPE campaign_status AS ENUM ('draft', 'active', 'paused', 'completed', 'cancelled');
CREATE TYPE lead_status AS ENUM ('new', 'contacted', 'responded', 'qualified', 'converted', 'unqualified');
CREATE TYPE email_status AS ENUM ('pending', 'sent', 'delivered', 'opened', 'clicked', 'replied', 'bounced', 'failed');
CREATE TYPE agent_type AS ENUM ('goal_understanding', 'strategic_planning', 'content_creation', 'email_automation', 'reply_analysis', 'meeting_booking', 'performance_monitoring', 'linkedin_outreach');

-- Organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    subscription_tier subscription_tier DEFAULT 'free',
    owner_id UUID, -- Will be set after users table is created
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role user_role DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint for organization owner
ALTER TABLE organizations ADD CONSTRAINT fk_organizations_owner 
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL;

-- User organizations (many-to-many relationship)
CREATE TABLE user_organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    role user_role DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, organization_id)
);

-- Sales goals table
CREATE TABLE sales_goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    target_metrics JSONB DEFAULT '{}', -- e.g., {"leads": 100, "meetings": 20, "deals": 5}
    status VARCHAR(50) DEFAULT 'active',
    deadline DATE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leads table (CRM)
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    company VARCHAR(100),
    title VARCHAR(100),
    phone VARCHAR(20),
    linkedin_url TEXT,
    source VARCHAR(50), -- e.g., 'apollo', 'manual', 'import'
    status lead_status DEFAULT 'new',
    score INTEGER CHECK (score >= 0 AND score <= 100),
    notes TEXT,
    custom_fields JSONB DEFAULT '{}',
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, email)
);

-- Campaigns table
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    goal_id UUID REFERENCES sales_goals(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status campaign_status DEFAULT 'draft',
    settings JSONB DEFAULT '{}', -- Campaign configuration
    stats JSONB DEFAULT '{}', -- Campaign statistics
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email templates table
CREATE TABLE email_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    body TEXT NOT NULL,
    variables JSONB DEFAULT '[]', -- List of template variables
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email sends table
CREATE TABLE email_sends (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
    subject VARCHAR(200) NOT NULL,
    body TEXT NOT NULL,
    from_email VARCHAR(255) NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    status email_status DEFAULT 'pending',
    external_id VARCHAR(255), -- ID from email service provider
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    replied_at TIMESTAMP WITH TIME ZONE,
    bounced_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email interactions table (opens, clicks, etc.)
CREATE TABLE email_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    send_id UUID NOT NULL REFERENCES email_sends(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'open', 'click', 'reply', 'bounce', etc.
    data JSONB DEFAULT '{}', -- Additional interaction data
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agent configurations table
CREATE TABLE agent_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    agent_type agent_type NOT NULL,
    name VARCHAR(100) NOT NULL,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, agent_type, name)
);

-- API keys table (encrypted storage)
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    service VARCHAR(50) NOT NULL, -- 'openai', 'apollo', 'sendgrid', etc.
    name VARCHAR(100) NOT NULL, -- User-friendly name
    encrypted_key TEXT NOT NULL, -- Encrypted API key
    is_active BOOLEAN DEFAULT TRUE,
    last_used TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhooks table
CREATE TABLE webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    service VARCHAR(50) NOT NULL, -- 'stripe', 'sendgrid', etc.
    event_type VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit log table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_organization_id ON users(organization_id);
CREATE INDEX idx_leads_organization_id ON leads(organization_id);
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_campaigns_organization_id ON campaigns(organization_id);
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_email_sends_campaign_id ON email_sends(campaign_id);
CREATE INDEX idx_email_sends_lead_id ON email_sends(lead_id);
CREATE INDEX idx_email_sends_status ON email_sends(status);
CREATE INDEX idx_email_interactions_send_id ON email_interactions(send_id);
CREATE INDEX idx_agent_configurations_organization_id ON agent_configurations(organization_id);
CREATE INDEX idx_api_keys_organization_id ON api_keys(organization_id);
CREATE INDEX idx_webhooks_organization_id ON webhooks(organization_id);
CREATE INDEX idx_webhooks_processed ON webhooks(processed);
CREATE INDEX idx_audit_logs_organization_id ON audit_logs(organization_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sales_goals_updated_at BEFORE UPDATE ON sales_goals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_sends_updated_at BEFORE UPDATE ON email_sends FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_configurations_updated_at BEFORE UPDATE ON agent_configurations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_sends ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for organizations
CREATE POLICY "Users can view their own organization" ON organizations
    FOR SELECT USING (
        id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Organization owners can update their organization" ON organizations
    FOR UPDATE USING (owner_id = auth.uid());

-- RLS Policies for users
CREATE POLICY "Users can view users in their organization" ON users
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.uid());

-- RLS Policies for sales_goals
CREATE POLICY "Users can view goals in their organization" ON sales_goals
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can manage goals in their organization" ON sales_goals
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for leads
CREATE POLICY "Users can view leads in their organization" ON leads
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can manage leads in their organization" ON leads
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for campaigns
CREATE POLICY "Users can view campaigns in their organization" ON campaigns
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can manage campaigns in their organization" ON campaigns
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for email_templates
CREATE POLICY "Users can view email templates in their organization" ON email_templates
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can manage email templates in their organization" ON email_templates
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for email_sends
CREATE POLICY "Users can view email sends in their organization" ON email_sends
    FOR SELECT USING (
        campaign_id IN (
            SELECT id FROM campaigns
            WHERE organization_id IN (
                SELECT organization_id FROM users
                WHERE id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can manage email sends in their organization" ON email_sends
    FOR ALL USING (
        campaign_id IN (
            SELECT id FROM campaigns
            WHERE organization_id IN (
                SELECT organization_id FROM users
                WHERE id = auth.uid()
            )
        )
    );

-- RLS Policies for email_interactions
CREATE POLICY "Users can view email interactions in their organization" ON email_interactions
    FOR SELECT USING (
        send_id IN (
            SELECT es.id FROM email_sends es
            JOIN campaigns c ON es.campaign_id = c.id
            WHERE c.organization_id IN (
                SELECT organization_id FROM users
                WHERE id = auth.uid()
            )
        )
    );

-- RLS Policies for agent_configurations
CREATE POLICY "Users can view agent configurations in their organization" ON agent_configurations
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can manage agent configurations in their organization" ON agent_configurations
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for api_keys
CREATE POLICY "Users can view API keys in their organization" ON api_keys
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can manage API keys in their organization" ON api_keys
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for webhooks
CREATE POLICY "Users can view webhooks in their organization" ON webhooks
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for audit_logs
CREATE POLICY "Users can view audit logs in their organization" ON audit_logs
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users
            WHERE id = auth.uid()
        )
    );
