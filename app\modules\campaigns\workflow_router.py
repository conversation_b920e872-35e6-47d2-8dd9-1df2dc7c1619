"""
Campaign Workflow Router - API endpoints for managing sequential agent workflows
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from pydantic import BaseModel, Field

from app.modules.agents.workflow_manager import (
    workflow_manager, 
    WorkflowInput, 
    WorkflowExecution, 
    WorkflowStageResult
)
from app.shared.dependencies import (
    get_current_active_user, 
    get_current_organization,
    get_rls_service_with_auth
)
from app.core.rls_service import RLSService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/campaigns/workflow", tags=["Campaign Workflow"])


class StartWorkflowRequest(BaseModel):
    """Request to start a campaign workflow"""
    campaign_id: str = Field(..., description="Campaign ID")
    target_meetings: int = Field(default=10, description="Target number of meetings")
    target_contacts: int = Field(default=500, description="Target number of contacts")
    target_audience: Dict[str, Any] = Field(..., description="Target audience criteria")
    campaign_goals: Dict[str, Any] = Field(..., description="Campaign goals and objectives")
    workflow_type: str = Field(default="sales_autopilot", description="Type of workflow")


class WorkflowStatusResponse(BaseModel):
    """Response for workflow status"""
    workflow_id: str
    status: str
    current_stage: str
    stages_completed: int
    total_stages: int
    execution_time_ms: int
    started_at: str
    completed_at: Optional[str] = None
    progress_percentage: float


class WorkflowStageResponse(BaseModel):
    """Response for workflow stage details"""
    stage: str
    agent_type: str
    status: str
    execution_time_ms: int
    created_at: str
    input_summary: Dict[str, Any]
    output_summary: Dict[str, Any]
    error_message: Optional[str] = None


class WorkflowDetailResponse(BaseModel):
    """Detailed workflow response"""
    workflow: WorkflowStatusResponse
    stages: List[WorkflowStageResponse]
    final_output: Optional[Dict[str, Any]] = None


@router.post("/start", response_model=Dict[str, str])
async def start_campaign_workflow(
    request: StartWorkflowRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    rls_service: RLSService = Depends(get_rls_service_with_auth)
):
    """Start a sequential agent workflow for a campaign"""
    try:
        # Verify campaign exists and belongs to organization
        campaign = await rls_service.campaigns.get_by_id(request.campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Prepare workflow input
        workflow_input = WorkflowInput(
            campaign_id=request.campaign_id,
            organization_id=organization["id"],
            initial_input={
                "campaign_id": request.campaign_id,
                "organization_id": organization["id"],
                "user_id": current_user["id"],
                "target_meetings": request.target_meetings,
                "target_contacts": request.target_contacts,
                "target_audience": request.target_audience,
                "campaign_goals": request.campaign_goals,
                "campaign_name": campaign.get("name", ""),
                "campaign_description": campaign.get("description", "")
            },
            workflow_type=request.workflow_type,
            target_meetings=request.target_meetings,
            target_contacts=request.target_contacts
        )
        
        # Start workflow in background
        workflow_id = await workflow_manager.start_workflow(workflow_input)
        
        logger.info(f"Started workflow {workflow_id} for campaign {request.campaign_id}")
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "message": "Campaign workflow started successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start campaign workflow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start campaign workflow"
        )


@router.get("/{workflow_id}/status", response_model=WorkflowStatusResponse)
async def get_workflow_status(
    workflow_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get current status of a workflow"""
    try:
        workflow = await workflow_manager.get_workflow_status(workflow_id)
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Verify workflow belongs to organization
        if workflow.organization_id != organization["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this workflow"
            )
        
        # Calculate progress
        total_stages = 9  # Total number of stages in the workflow
        stages_completed = len(workflow.stages_completed)
        progress_percentage = (stages_completed / total_stages) * 100
        
        return WorkflowStatusResponse(
            workflow_id=workflow.workflow_id,
            status=workflow.status,
            current_stage=workflow.current_stage.value,
            stages_completed=stages_completed,
            total_stages=total_stages,
            execution_time_ms=workflow.total_execution_time_ms,
            started_at=workflow.started_at.isoformat(),
            completed_at=workflow.completed_at.isoformat() if workflow.completed_at else None,
            progress_percentage=progress_percentage
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workflow status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get workflow status"
        )


@router.get("/{workflow_id}/details", response_model=WorkflowDetailResponse)
async def get_workflow_details(
    workflow_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get detailed workflow information including all stage results"""
    try:
        # Get workflow status
        workflow = await workflow_manager.get_workflow_status(workflow_id)
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow not found"
            )
        
        # Verify workflow belongs to organization
        if workflow.organization_id != organization["id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this workflow"
            )
        
        # Get stage results
        stage_results = await workflow_manager.get_stage_results(workflow_id)
        
        # Convert to response format
        total_stages = 9
        stages_completed = len(workflow.stages_completed)
        progress_percentage = (stages_completed / total_stages) * 100
        
        workflow_status = WorkflowStatusResponse(
            workflow_id=workflow.workflow_id,
            status=workflow.status,
            current_stage=workflow.current_stage.value,
            stages_completed=stages_completed,
            total_stages=total_stages,
            execution_time_ms=workflow.total_execution_time_ms,
            started_at=workflow.started_at.isoformat(),
            completed_at=workflow.completed_at.isoformat() if workflow.completed_at else None,
            progress_percentage=progress_percentage
        )
        
        # Convert stage results
        stages = []
        for stage_result in stage_results:
            # Create summaries (limit data size for API response)
            input_summary = {
                "campaign_id": stage_result.input_data.get("campaign_id"),
                "organization_id": stage_result.input_data.get("organization_id"),
                "stage_specific_data": {
                    k: v for k, v in stage_result.input_data.items() 
                    if k not in ["campaign_id", "organization_id", "user_id"]
                }
            }
            
            output_summary = {}
            if stage_result.output_data:
                # Extract key metrics from output
                for key in ["leads_sourced", "leads_allocated", "content_created", "emails_sent", "meetings_booked"]:
                    if key in stage_result.output_data:
                        output_summary[key] = stage_result.output_data[key]
                
                # Add summary fields
                if "sourcing_summary" in stage_result.output_data:
                    output_summary["sourcing_summary"] = stage_result.output_data["sourcing_summary"]
                if "distribution_summary" in stage_result.output_data:
                    output_summary["distribution_summary"] = stage_result.output_data["distribution_summary"]
            
            stages.append(WorkflowStageResponse(
                stage=stage_result.stage.value,
                agent_type=stage_result.agent_type.value,
                status=stage_result.status,
                execution_time_ms=stage_result.execution_time_ms,
                created_at=stage_result.created_at.isoformat(),
                input_summary=input_summary,
                output_summary=output_summary,
                error_message=stage_result.error_message
            ))
        
        return WorkflowDetailResponse(
            workflow=workflow_status,
            stages=stages,
            final_output=workflow.final_output
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workflow details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get workflow details"
        )


@router.get("/organization/workflows", response_model=List[WorkflowStatusResponse])
async def list_organization_workflows(
    limit: int = 20,
    offset: int = 0,
    status_filter: Optional[str] = None,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    rls_service: RLSService = Depends(get_rls_service_with_auth)
):
    """List all workflows for the organization"""
    try:
        # Build filters
        filters = {"organization_id": organization["id"]}
        if status_filter:
            filters["status"] = status_filter
        
        # Get workflows from database
        workflows_data = await rls_service.get_repository("workflow_executions").list(
            filters=filters,
            limit=limit,
            offset=offset,
            order_by="created_at",
            ascending=False
        )
        
        # Convert to response format
        workflows = []
        for workflow_data in workflows_data:
            # Get stage count for this workflow
            stage_results = await workflow_manager.get_stage_results(workflow_data["workflow_id"])
            stages_completed = len(stage_results)
            total_stages = 9
            progress_percentage = (stages_completed / total_stages) * 100
            
            workflows.append(WorkflowStatusResponse(
                workflow_id=workflow_data["workflow_id"],
                status=workflow_data["status"],
                current_stage=workflow_data.get("current_stage", "unknown"),
                stages_completed=stages_completed,
                total_stages=total_stages,
                execution_time_ms=workflow_data.get("total_execution_time_ms", 0),
                started_at=workflow_data["started_at"],
                completed_at=workflow_data.get("completed_at"),
                progress_percentage=progress_percentage
            ))
        
        return workflows
        
    except Exception as e:
        logger.error(f"Failed to list organization workflows: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list workflows"
        )
