#!/usr/bin/env python3
"""
Test script to verify Supabase authentication integration
"""

import sys
import os
sys.path.append('.')

from app.core.supabase_auth import supabase_auth_manager
from jose import jwt
from app.core.config import settings

def test_supabase_auth():
    """Test Supabase authentication integration"""
    
    print("🔧 Testing Supabase Authentication Integration...")
    print("=" * 60)
    
    # Test user data
    test_user = {
        'id': 'test-user-123',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'role': 'user',
        'organization_id': 'test-org-123'
    }
    
    try:
        # Create auth session
        print("1. Creating Supabase-compatible auth session...")
        auth_session = supabase_auth_manager.create_auth_session(test_user)
        print("   ✅ Auth session created successfully!")
        print(f"   Token type: {auth_session['token_type']}")
        print(f"   Expires in: {auth_session['expires_in']} seconds")
        
        # Decode and verify the access token
        print("\n2. Verifying access token structure...")
        access_token = auth_session['access_token']
        payload = jwt.decode(
            access_token,
            settings.jwt_secret_key,
            algorithms=['HS256'],
            audience='authenticated'
        )
        
        print("   ✅ Token decoded successfully!")
        print(f"   Token payload:")
        print(f"     iss: {payload.get('iss')}")
        print(f"     ref: {payload.get('ref')}")
        print(f"     role: {payload.get('role')}")
        print(f"     aud: {payload.get('aud')}")
        print(f"     sub: {payload.get('sub')}")
        print(f"     email: {payload.get('email')}")
        
        # Verify required Supabase fields
        print("\n3. Validating Supabase compatibility...")
        required_fields = {
            'iss': 'supabase',
            'role': 'authenticated',
            'aud': 'authenticated',
            'ref': 'aqjctgvrvtogryxlvwor'
        }
        
        for field, expected_value in required_fields.items():
            actual_value = payload.get(field)
            if actual_value == expected_value:
                print(f"   ✅ {field}: {actual_value}")
            else:
                print(f"   ❌ {field}: expected '{expected_value}', got '{actual_value}'")
                return False
        
        # Test refresh token
        print("\n4. Testing refresh token functionality...")
        refresh_result = supabase_auth_manager.refresh_access_token(
            auth_session['refresh_token'], 
            test_user
        )
        print("   ✅ Refresh token working!")
        print(f"   New token expires in: {refresh_result['expires_in']} seconds")
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Supabase authentication integration is working correctly!")
        print("\nKey Features Verified:")
        print("✅ Supabase-compatible JWT token creation")
        print("✅ Proper token structure with required fields")
        print("✅ Token refresh functionality")
        print("✅ User metadata inclusion")
        print("✅ Correct audience and role settings")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rls_policies():
    """Test RLS policy structure"""
    print("\n🔒 Testing RLS Policy Requirements...")
    print("=" * 60)
    
    # Test that we can create tokens that will work with auth.uid()
    test_user = {
        'id': 'user-uuid-123',
        'email': '<EMAIL>',
        'first_name': 'RLS',
        'last_name': 'Test',
        'role': 'user',
        'organization_id': 'org-uuid-123'
    }
    
    try:
        auth_session = supabase_auth_manager.create_auth_session(test_user)
        access_token = auth_session['access_token']
        
        # Decode token
        payload = jwt.decode(
            access_token,
            settings.jwt_secret_key,
            algorithms=['HS256'],
            audience='authenticated'
        )
        
        print("✅ Token created for RLS testing")
        print(f"   User ID (sub): {payload.get('sub')}")
        print(f"   This will be available as auth.uid() in RLS policies")
        
        # Verify the token structure matches what Supabase expects
        if payload.get('sub') == test_user['id']:
            print("✅ auth.uid() will return correct user ID")
        else:
            print("❌ auth.uid() mapping issue")
            return False
            
        print("\n🔒 RLS Integration Ready!")
        print("   The tokens will work with policies like:")
        print("   WHERE id = auth.uid()")
        print("   WHERE organization_id IN (SELECT organization_id FROM users WHERE id = auth.uid())")
        
        return True
        
    except Exception as e:
        print(f"❌ RLS test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Authentication Integration Tests...\n")
    
    # Test basic auth functionality
    auth_success = test_supabase_auth()
    
    # Test RLS integration
    rls_success = test_rls_policies()
    
    print("\n" + "=" * 60)
    if auth_success and rls_success:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("\nYour authentication system is now properly configured to work with Supabase RLS!")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED!")
        print("\nPlease check the errors above and fix the issues.")
        sys.exit(1)
