"""
Agents module
"""

from app.modules.agents.router import router
from app.modules.agents.service import agent_service
from app.modules.agents.models import (
    AgentType,
    AgentStatus,
    TaskStatus,
    AgentTask,
    AgentConfiguration,
    CreateAgentTaskRequest
)

# Import agents to register them
from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.strategic_planning import StrategicPlanningAgent
from app.modules.agents.content_creation import ContentCreationAgent
from app.modules.agents.simple_workflow import simple_workflow_manager

__all__ = [
    "router",
    "agent_service",
    "AgentType",
    "AgentStatus",
    "TaskStatus",
    "AgentTask",
    "AgentConfiguration",
    "CreateAgentTaskRequest",
    "GoalUnderstandingAgent",
    "StrategicPlanningAgent",
    "ContentCreationAgent",
    "simple_workflow_manager"
]