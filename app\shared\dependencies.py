"""
Shared dependencies for FastAPI dependency injection
"""

from typing import Optional, Generator
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
import redis
from supabase import create_client, Client
import logging

from app.core.config import settings
from app.core.exceptions import AuthenticationError, ExternalServiceError
from app.core.rls_service import RLSService, get_rls_service

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Global clients (will be initialized in lifespan)
supabase_admin_client: Optional[Client] = None
redis_client: Optional[redis.Redis] = None


def get_supabase_admin_client() -> Client:
    """Get Supabase admin client instance (bypasses RLS)"""
    global supabase_admin_client
    if not supabase_admin_client:
        try:
            supabase_admin_client = create_client(
                settings.supabase_url,
                settings.supabase_service_key
            )
        except Exception as e:
            raise ExternalServiceError(
                message="Failed to connect to Supa<PERSON>",
                details={"error": str(e)}
            )
    return supabase_admin_client


def get_supabase_client_with_auth(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Client:
    """Get Supabase client with user authentication context for RLS"""
    try:
        # Create a new client instance with anon key for RLS
        client = create_client(
            settings.supabase_url,
            settings.supabase_anon_key
        )

        # The token should already be Supabase-compatible from our auth service
        # Just verify it's valid and set it directly
        payload = jwt.decode(
            credentials.credentials,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm],
            audience="authenticated"
        )

        # Verify it has the required Supabase fields (audience is already validated by jwt.decode)
        if not payload.get("sub"):
            raise AuthenticationError("Invalid token: missing user ID")

        if payload.get("role") != "authenticated":
            raise AuthenticationError("Invalid role")

        if payload.get("iss") != "supabase":
            raise AuthenticationError("Invalid issuer")

        # Note: We can't set auth session with our custom JWT tokens
        # because they're signed with our secret, not Supabase's secret.
        # For now, we'll use the service key client and handle user context
        # in the application logic rather than relying on RLS.
        # TODO: Implement proper Supabase auth integration

        logger.debug(f"Set Supabase auth context for user: {payload.get('sub')}")
        return client

    except JWTError as e:
        logger.error(f"JWT verification failed: {e}")
        raise AuthenticationError(f"Invalid token: {str(e)}")
    except Exception as e:
        logger.error(f"Failed to create authenticated Supabase client: {e}")
        raise ExternalServiceError(
            message="Failed to authenticate with database",
            details={"error": str(e)}
        )


def get_redis_client() -> redis.Redis:
    """Get Redis client instance"""
    global redis_client
    if not redis_client:
        try:
            redis_client = redis.from_url(settings.redis_url)
            # Test connection
            redis_client.ping()
        except Exception as e:
            raise ExternalServiceError(
                message="Failed to connect to Redis",
                details={"error": str(e)}
            )
    return redis_client


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """Extract user ID from JWT token"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm],
            audience="authenticated"
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise AuthenticationError("Invalid token: missing user ID")
        return user_id
    except JWTError as e:
        raise AuthenticationError(f"Invalid token: {str(e)}")


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """Get current user from database using JWT token"""
    try:
        # Decode the JWT token to get user information
        payload = jwt.decode(
            credentials.credentials,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm],
            audience="authenticated"
        )

        user_id = payload.get("sub")
        if not user_id:
            raise AuthenticationError("Invalid token: missing user ID")

        # Use admin client to query user data
        # Since we can't use RLS with our custom JWT, we'll filter manually
        supabase = get_supabase_admin_client()
        response = supabase.table("users").select("*").eq("id", user_id).execute()

        if not response.data:
            raise AuthenticationError("User not found")

        return response.data[0]

    except JWTError as e:
        raise AuthenticationError(f"Invalid token: {str(e)}")
    except Exception as e:
        logger.error(f"Failed to get current user: {e}")
        raise AuthenticationError(f"Failed to get user: {str(e)}")


async def get_current_active_user(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """Get current active user (not disabled)"""
    if current_user.get("is_active", True) is False:
        raise AuthenticationError("User account is disabled")
    return current_user


async def get_current_organization_id(
    current_user: dict = Depends(get_current_active_user)
) -> str:
    """Get current user's organization ID"""
    organization_id = current_user.get("organization_id")
    if not organization_id:
        raise AuthenticationError("User is not associated with any organization")
    return organization_id


async def get_current_organization(
    organization_id: str = Depends(get_current_organization_id)
) -> dict:
    """Get current organization from database using admin client"""
    try:
        # Use admin client to avoid RLS issues
        supabase = get_supabase_admin_client()
        response = supabase.table("organizations").select("*").eq("id", organization_id).execute()
        if not response.data:
            raise AuthenticationError("Organization not found")
        return response.data[0]
    except Exception as e:
        raise AuthenticationError(f"Failed to get organization: {str(e)}")


def require_subscription_tier(required_tier: str):
    """Dependency factory for subscription tier requirements"""
    async def check_subscription_tier(
        organization: dict = Depends(get_current_organization)
    ) -> dict:
        current_tier = organization.get("subscription_tier", "free")
        
        # Define tier hierarchy
        tier_hierarchy = ["free", "basic", "pro", "enterprise"]
        
        if tier_hierarchy.index(current_tier) < tier_hierarchy.index(required_tier):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"This feature requires {required_tier} subscription or higher"
            )
        
        return organization
    
    return check_subscription_tier


def require_role(required_role: str):
    """Dependency factory for role-based access control"""
    async def check_role(
        current_user: dict = Depends(get_current_active_user)
    ) -> dict:
        user_role = current_user.get("role", "user")
        
        # Define role hierarchy
        role_hierarchy = ["user", "admin", "owner"]
        
        if role_hierarchy.index(user_role) < role_hierarchy.index(required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"This action requires {required_role} role or higher"
            )
        
        return current_user
    
    return check_role


# Common dependency combinations
CurrentUser = Depends(get_current_active_user)
CurrentOrganization = Depends(get_current_organization)
RequireBasicTier = Depends(require_subscription_tier("basic"))
RequireProTier = Depends(require_subscription_tier("pro"))
RequireEnterpriseTier = Depends(require_subscription_tier("enterprise"))
RequireAdmin = Depends(require_role("admin"))
RequireOwner = Depends(require_role("owner"))

# RLS Service dependency
async def get_rls_service_with_auth(
    auth_client: Client = Depends(get_supabase_client_with_auth)
) -> RLSService:
    """Get RLS service with authenticated client"""
    return get_rls_service(auth_client)


# Backward compatibility aliases
get_supabase_client = get_supabase_admin_client
