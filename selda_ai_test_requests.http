### Selda AI Sales Autopilot - Complete HTTP Test Requests
### Base URL: http://localhost:8000

### Variables
@baseUrl = http://localhost:8000
@token = {{auth_response.response.body.access_token}}
@conversationId = {{goal_start_response.response.body.conversation_id}}
@workflowId = {{workflow_start_response.response.body.workflow_id}}
@campaignId = {{campaign_response.response.body.id}}

### =============================================================================
### 1. HEALTH CHECK
### =============================================================================

### Health Check
GET {{baseUrl}}/health
Content-Type: application/json

### =============================================================================
### 2. AUTHENTICATION
### =============================================================================

### Register New User
# @name register_response
POST {{baseUrl}}/api/v1/auth/register
Content-Type: application/json

{
  "email": "test.user.{{$timestamp}}@example.com",
  "password": "StrongPass123!",
  "first_name": "Test",
  "last_name": "User",
  "organization_name": "Restaurant POS Solutions Ltd"
}

### Login User
# @name auth_response
POST {{baseUrl}}/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Itblast@123"
}

### Get Current User Info
GET {{baseUrl}}/api/v1/auth/me
Authorization: Bearer {{token}}

### =============================================================================
### 3. CHAT INTERFACE - NATURAL LANGUAGE GOAL INPUT (CORRECT FLOW)
### =============================================================================

### Start Goal Conversation with Natural Language Input
# @name goal_start_response
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "I want to get 10 restaurant clients in London within 3 months",
  "context": "We provide modern POS systems for restaurants and want to expand our client base in the UK market"
}

### Handle Follow-up Response (Answer Questions)
# @name goal_followup_response
POST {{baseUrl}}/api/v1/chat/goal/followup
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "conversation_id": "{{conversationId}}",
  "user_response": "My budget is $5000 and I want to measure success by the number of signed contracts. I'm targeting small to medium restaurants with 20-100 seats.",
  "answered_questions": {
    "budget": "$5000",
    "success_metrics": "number of signed contracts",
    "company_size": "20-100 seats",
    "target_type": "small to medium restaurants"
  }
}

### Get Goal Examples
GET {{baseUrl}}/api/v1/chat/goal/examples
Authorization: Bearer {{token}}

### Get Conversation Status
GET {{baseUrl}}/api/v1/conversation/{{conversationId}}/status
Authorization: Bearer {{token}}

### =============================================================================
### 4. INDIVIDUAL AGENT TESTING (SEQUENTIAL ORDER)
### =============================================================================

### Agent 1: Goal Understanding Agent (Manual Test)
# @name goal_understanding_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "goal_understanding",
  "input_data": {
    "goal_description": "I want to get 10 restaurant clients in London within 3 months",
    "context": "We provide POS systems for restaurants",
    "user_id": "user-uuid",
    "organization_id": "org-uuid",
    "is_follow_up": false,
    "answered_questions": {}
  }
}

### Agent 2: Strategic Planning Agent (Uses Goal Understanding Output)
# @name strategic_planning_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "strategic_planning",
  "input_data": {
    "goal": {
      "objective": "Get 10 restaurant clients",
      "target_audience": {
        "industry": "Restaurants",
        "location": "London",
        "company_size": "20-100 seats"
      },
      "timeline": "3 months",
      "budget": "$5000",
      "success_criteria": "number of signed contracts"
    },
    "target_audience": {
      "industry": "Restaurants",
      "location": "London",
      "company_size": "20-100 seats"
    },
    "timeline_constraints": {
      "duration": "3 months"
    },
    "budget_constraints": {
      "total_budget": "$5000"
    }
  }
}

### Agent 3: Lead Sourcing Agent (Uses Strategic Planning Output)
# @name lead_sourcing_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "lead_sourcing",
  "input_data": {
    "campaign_id": "campaign-uuid",
    "target_audience": {
      "industry": "Restaurants",
      "location": "London",
      "company_size": "20-100"
    },
    "location": "London",
    "industry": "Restaurants",
    "lead_count_target": 500,
    "sources": ["apollo"],
    "quality_threshold": 70
  }
}

### Agent 4: Lead Distribution Agent (Uses Lead Sourcing Output)
# @name lead_distribution_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "lead_distribution",
  "input_data": {
    "campaign_id": "campaign-uuid",
    "leads_requested": 100,
    "target_audience": {
      "industry": "Restaurants",
      "location": "London",
      "company_size": "20-100"
    },
    "quality_threshold": 70,
    "exclude_domains": ["example.com"],
    "priority_level": 3
  }
}

### Agent 5: Content Creation Agent (Uses Lead Distribution Output)
# @name content_creation_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "content_creation",
  "input_data": {
    "allocated_leads": ["lead-uuid-1", "lead-uuid-2"],
    "campaign_strategy": {
      "approach": "Multi-channel outreach",
      "messaging_strategy": "Focus on POS efficiency",
      "value_proposition": "Streamline restaurant operations"
    },
    "target_audience": {
      "industry": "Restaurants",
      "location": "London"
    },
    "campaign_goals": {
      "objective": "Get 10 restaurant clients"
    }
  }
}

### Agent 6: Email Automation Agent (Uses Content Creation Output)
# @name email_automation_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "email_automation",
  "input_data": {
    "content_templates": [
      {
        "subject": "Streamline Your Restaurant Operations",
        "body": "Hi {{first_name}}, I noticed {{company}} is in the restaurant business..."
      }
    ],
    "allocated_leads": ["lead-uuid-1", "lead-uuid-2"],
    "campaign_schedule": {
      "start_date": "2024-01-15",
      "frequency": "daily",
      "batch_size": 50
    },
    "personalization_data": {
      "sender_name": "John Doe",
      "company_name": "Restaurant POS Solutions"
    }
  }
}

### Agent 7: Reply Analysis Agent (Uses Email Automation Output)
# @name reply_analysis_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "reply_analysis",
  "input_data": {
    "sent_emails": [
      {
        "email_id": "email-1",
        "lead_id": "lead-uuid-1",
        "sent_at": "2024-01-15T10:00:00Z",
        "status": "delivered"
      }
    ],
    "tracking_data": {
      "opens": 45,
      "clicks": 12,
      "replies": 8
    },
    "campaign_id": "campaign-uuid"
  }
}

### Agent 8: Meeting Booking Agent (Uses Reply Analysis Output)
# @name meeting_booking_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "meeting_booking",
  "input_data": {
    "qualified_leads": [
      {
        "lead_id": "lead-uuid-1",
        "interest_level": "high",
        "response_sentiment": "positive"
      }
    ],
    "meeting_opportunities": [
      {
        "lead_id": "lead-uuid-1",
        "preferred_time": "next week",
        "meeting_type": "demo"
      }
    ],
    "calendar_settings": {
      "available_slots": ["9:00-17:00"],
      "timezone": "Europe/London",
      "meeting_duration": 30
    }
  }
}

### Agent 9: Performance Monitoring Agent (Uses Meeting Booking Output)
# @name performance_monitoring_response
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "performance_monitoring",
  "input_data": {
    "booked_meetings": [
      {
        "meeting_id": "meeting-1",
        "lead_id": "lead-uuid-1",
        "scheduled_at": "2024-01-20T14:00:00Z",
        "status": "confirmed"
      }
    ],
    "campaign_results": {
      "emails_sent": 500,
      "responses_received": 47,
      "meetings_booked": 12,
      "conversion_rate": 2.4
    },
    "original_goal": {
      "target_meetings": 10,
      "timeline": "3 months",
      "budget": "$5000"
    }
  }
}

### =============================================================================
### 5. CAMPAIGN MANAGEMENT
### =============================================================================

### Create Campaign
# @name campaign_response
POST {{baseUrl}}/api/v1/campaigns
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "London Restaurant POS Campaign",
  "description": "Target restaurants in London for our POS system solutions",
  "target_audience": {
    "industry": "Restaurants",
    "location": "London",
    "company_size": "20-100 seats"
  },
  "goals": {
    "meetings_target": 10,
    "contacts_target": 500,
    "conversion_rate_target": 2.0
  },
  "status": "active"
}

### List Campaigns
GET {{baseUrl}}/api/v1/campaigns
Authorization: Bearer {{token}}

### Get Campaign Details
GET {{baseUrl}}/api/v1/campaigns/{{campaignId}}
Authorization: Bearer {{token}}

### =============================================================================
### 6. COMPLETE AUTOMATED WORKFLOW
### =============================================================================

### Start Complete Sequential Workflow
# @name workflow_start_response
POST {{baseUrl}}/api/v1/campaigns/workflow/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "campaign_id": "{{campaignId}}",
  "target_meetings": 10,
  "target_contacts": 500,
  "target_audience": {
    "industry": "Restaurants",
    "location": "London",
    "company_size": "20-100 seats",
    "job_titles": ["Owner", "Manager", "Operations Director"]
  },
  "campaign_goals": {
    "objective": "Generate qualified leads for our restaurant POS system",
    "value_proposition": "Streamline restaurant operations with modern POS technology",
    "pain_points": ["Manual order processing", "Inventory management", "Payment processing delays"]
  },
  "workflow_type": "sales_autopilot"
}

### Monitor Workflow Status
GET {{baseUrl}}/api/v1/campaigns/workflow/{{workflowId}}/status
Authorization: Bearer {{token}}

### Get Detailed Workflow Results
GET {{baseUrl}}/api/v1/campaigns/workflow/{{workflowId}}/details
Authorization: Bearer {{token}}

### List Organization Workflows
GET {{baseUrl}}/api/v1/campaigns/workflow/organization/workflows?limit=10&status_filter=completed
Authorization: Bearer {{token}}

### =============================================================================
### 7. LEADS MANAGEMENT
### =============================================================================

### Get Lead Analytics
GET {{baseUrl}}/api/v1/leads/analytics?campaign_id={{campaignId}}
Authorization: Bearer {{token}}

### List Leads
GET {{baseUrl}}/api/v1/leads?limit=20&status=unused
Authorization: Bearer {{token}}

### =============================================================================
### 8. APOLLO.IO INTEGRATION
### =============================================================================

### Search Leads via Apollo
POST {{baseUrl}}/api/v1/apollo/search
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "filters": {
    "industry": "Restaurants",
    "location": "London",
    "company_size": "20-100"
  },
  "page": 1,
  "per_page": 50
}

### =============================================================================
### 9. ANALYTICS & MONITORING
### =============================================================================

### Get Campaign Performance
GET {{baseUrl}}/api/v1/analytics/campaigns/{{campaignId}}/performance
Authorization: Bearer {{token}}

### Get Lead Quality Analytics
GET {{baseUrl}}/api/v1/analytics/leads/quality
Authorization: Bearer {{token}}

### Get Workflow Performance Metrics
GET {{baseUrl}}/api/v1/analytics/workflows/performance
Authorization: Bearer {{token}}

### =============================================================================
### 10. EXAMPLE COMPLETE FLOW TEST SEQUENCE
### =============================================================================

### STEP 1: Register & Login (Use responses above)

### STEP 2: Start Natural Language Goal Input
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "I need to find 15 new restaurant clients in London for our POS system within 2 months",
  "context": "We're a growing POS company looking to expand in the UK restaurant market"
}

### STEP 3: Answer Follow-up Questions
POST {{baseUrl}}/api/v1/chat/goal/followup
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "conversation_id": "conv_abc123",
  "user_response": "My budget is $8000, timeline is 2 months, and I want to measure success by signed contracts and demo bookings",
  "answered_questions": {
    "budget": "$8000",
    "timeline": "2 months",
    "success_metrics": "signed contracts and demo bookings"
  }
}

### STEP 4: Create Campaign (if needed)
POST {{baseUrl}}/api/v1/campaigns
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "UK Restaurant Expansion Campaign",
  "description": "Expand POS system sales to London restaurants",
  "target_audience": {
    "industry": "Restaurants",
    "location": "London"
  },
  "goals": {
    "meetings_target": 15,
    "contacts_target": 750
  }
}

### STEP 5: Start Automated Workflow
POST {{baseUrl}}/api/v1/campaigns/workflow/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "campaign_id": "campaign-uuid-from-step-4",
  "target_meetings": 15,
  "target_contacts": 750,
  "target_audience": {
    "industry": "Restaurants",
    "location": "London",
    "company_size": "10-200"
  },
  "campaign_goals": {
    "objective": "Find 15 new restaurant clients for POS system",
    "value_proposition": "Modern POS technology for restaurant efficiency"
  }
}

### STEP 6: Monitor Progress
GET {{baseUrl}}/api/v1/campaigns/workflow/workflow-id-from-step-5/status
Authorization: Bearer {{token}}

### =============================================================================
### TESTING SCENARIOS
### =============================================================================

### Scenario A: Technology Company Targeting SaaS
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "I want to get 20 qualified leads for our API management platform targeting SaaS companies in Europe",
  "context": "We help SaaS companies reduce API development time by 60% with our platform"
}

### Scenario B: Healthcare Industry Outreach
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "Find 50 healthcare companies in the US for our HIPAA-compliant communication platform",
  "context": "We provide secure communication solutions for healthcare providers"
}

### Scenario C: E-commerce Logistics
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "Book 30 demo meetings with e-commerce companies for our logistics optimization software",
  "context": "Our software reduces shipping costs by 25% and improves delivery times"
}

### Scenario D: Financial Services
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "Generate leads for our fintech solution targeting small banks and credit unions",
  "context": "We provide digital banking transformation services"
}

### =============================================================================
### ERROR TESTING
### =============================================================================

### Test Invalid Goal Input
POST {{baseUrl}}/api/v1/chat/goal/start
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "goal_description": "test",
  "context": ""
}

### Test Invalid Agent Type
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "invalid_agent",
  "input_data": {}
}

### Test Unauthorized Access
POST {{baseUrl}}/api/v1/chat/goal/start
Content-Type: application/json

{
  "goal_description": "Test without auth token"
}

### =============================================================================
### PERFORMANCE TESTING
### =============================================================================

### Bulk Agent Execution Test
POST {{baseUrl}}/api/v1/agents/execute
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "agent_type": "lead_sourcing",
  "input_data": {
    "campaign_id": "perf-test-campaign",
    "target_audience": {
      "industry": "Technology",
      "location": "Global"
    },
    "lead_count_target": 1000,
    "sources": ["apollo"],
    "quality_threshold": 60
  }
}

### =============================================================================
### Notes:
### - Replace placeholder UUIDs with actual values from responses
### - Update email addresses and timestamps as needed
### - Ensure Apollo API key is configured for lead sourcing tests
### - Some endpoints may require actual data to exist (campaigns, leads, etc.)
### - Use VS Code REST Client extension or similar HTTP client
### - Variables are automatically populated from previous responses
### =============================================================================
