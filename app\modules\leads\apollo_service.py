import httpx
import logging
from typing import Optional, Dict, Any, List
from app.core.config import settings
from app.modules.leads.models import LeadFilter, LeadCreate

logger = logging.getLogger(__name__)

class ApolloService:
    """
    Service for integrating with Apollo.io API for lead generation and enrichment.
    """
    BASE_URL = "https://api.apollo.io/api/v1"  # Updated to match official docs

    def __init__(self, api_key: Optional[str] = None):
        # Load from config/env
        self.api_key = api_key or getattr(settings, "apollo_api_key", None)
        if not self.api_key:
            raise RuntimeError("Apollo.io API key is not set in environment/config (settings.apollo_api_key)")
        self.headers = {
            "accept": "application/json",
            "Cache-Control": "no-cache",
            "Content-Type": "application/json",
            "x-api-key": self.api_key,  # Updated to match official docs
        }

    async def _request(self, method: str, endpoint: str, params: Optional[dict] = None, data: Optional[dict] = None) -> Any:
        url = f"{self.BASE_URL}{endpoint}"
        async with httpx.AsyncClient() as client:
            try:
                response = await client.request(
                    method,
                    url,
                    headers=self.headers,
                    params=params,
                    json=data,
                    timeout=30
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"Apollo.io API error: {e.response.status_code} {e.response.text}")
                raise
            except Exception as e:
                logger.error(f"Apollo.io API request failed: {e}")
                raise

    async def authenticate(self) -> bool:
        """Test authentication with Apollo.io API."""
        try:
            # Test with a simple search to verify API key works
            test_query = {
                "page": 1,
                "per_page": 1,
                "q_organization_locations": "London"
            }
            resp = await self._request("POST", "/mixed_people/search", data=test_query)
            return "people" in resp
        except Exception as e:
            logger.error(f"Apollo.io authentication failed: {e}")
            return False

    async def search_leads(self, filters: LeadFilter, organization_id: str, page: int = 1, per_page: int = 25) -> List[LeadCreate]:
        """Search for leads using Apollo.io API with structured filters."""
        apollo_query = {
            "q_organization_locations": filters.location if filters.location else "",
            "organization_industries": filters.industry if filters.industry else "",
            # Add more specific filter mappings here
            # "person_titles": ...,
            "page": page,
            "per_page": per_page
        }
        
        # Remove empty filters
        apollo_query = {k: v for k, v in apollo_query.items() if v}

        resp = await self._request("POST", "/mixed_people/search", data=apollo_query)
        
        leads = resp.get("people", [])
        
        # Map to internal LeadCreate model
        return [self._map_to_lead_create(lead, organization_id) for lead in leads]

    def _map_to_lead_create(self, apollo_person: Dict[str, Any], organization_id: str) -> LeadCreate:
        """Maps an Apollo person object to a LeadCreate model."""
        return LeadCreate(
            organization_id=organization_id,
            contact_email=apollo_person.get("email"),
            first_name=apollo_person.get("first_name"),
            last_name=apollo_person.get("last_name"),
            company=apollo_person.get("organization", {}).get("name"),
            title=apollo_person.get("title"),
            phone=apollo_person.get("phone_numbers", [{}])[0].get("sanitized_number"),
            linkedin_url=apollo_person.get("linkedin_url"),
            source="apollo.io",
            company_id=apollo_person.get("organization", {}).get("id"),
            industry=apollo_person.get("organization", {}).get("industry"),
            location=f'{apollo_person.get("city", "")}, {apollo_person.get("state", "")}, {apollo_person.get("country", "")}',
            # other fields will use defaults
        )

    async def search_companies(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for companies using Apollo.io API."""
        resp = await self._request("GET", "/companies", params=query)
        return resp.get("companies", [])

    async def enrich_contact(self, email: str) -> Optional[Dict[str, Any]]:
        """Enrich a contact by email using Apollo.io API."""
        resp = await self._request("GET", "/people/match", params={"email": email})
        return resp.get("person")

    async def bulk_enrich_people(self, person_ids: List[str]) -> List[Dict[str, Any]]:
        """Bulk enrich people using Apollo.io API to unlock real emails (max 10 per request)."""
        try:
            logger.info(f"🔄 Starting bulk enrichment for {len(person_ids)} person IDs")
            logger.info(f"🆔 Person IDs to enrich: {person_ids}")

            print(f"📧 Email Enrichment Progress:")
            print(f"   🎯 Total contacts to enrich: {len(person_ids)}")
            print(f"   📦 Processing in batches of 10 (Apollo.io limit)")

            all_enriched_people = []
            batch_size = 10  # Apollo.io limit

            # Process in batches of 10
            for i in range(0, len(person_ids), batch_size):
                batch_ids = person_ids[i:i + batch_size]
                batch_num = i//batch_size + 1
                total_batches = (len(person_ids) + batch_size - 1) // batch_size
                logger.info(f"🔄 Enriching batch {batch_num}: {len(batch_ids)} people")
                logger.info(f"🆔 Batch {batch_num} IDs: {batch_ids}")

                print(f"   📦 Batch {batch_num}/{total_batches}: Enriching {len(batch_ids)} contacts...")

                # Apollo.io bulk enrichment endpoint - correct format from official docs
                url_params = "reveal_personal_emails=true&reveal_phone_number=false"
                endpoint = f"/people/bulk_match?{url_params}"

                # Format data according to official docs
                data = {
                    "details": [{"id": person_id} for person_id in batch_ids]
                }

                logger.info(f"📤 Batch {batch_num}: Sending request to {endpoint}")
                logger.info(f"📤 Batch {batch_num}: Request data: {data}")

                try:
                    resp = await self._request("POST", endpoint, data=data)
                    logger.info(f"📥 Batch {batch_num}: Raw API response: {resp}")

                    # Apollo.io bulk enrichment returns data in 'matches' field, not 'people'
                    batch_enriched = resp.get("matches", [])
                    all_enriched_people.extend(batch_enriched)

                    # Count real emails in this batch
                    batch_real_emails = sum(1 for person in batch_enriched
                                          if person.get("email") and "email_not_unlocked" not in person.get("email", ""))

                    logger.info(f"✅ Batch {batch_num} completed: {len(batch_enriched)} people enriched, {batch_real_emails} with real emails")
                    print(f"   ✅ Batch {batch_num}/{total_batches}: {len(batch_enriched)} enriched, {batch_real_emails} real emails unlocked")

                    # Show detailed email analysis for debugging
                    if batch_enriched:
                        for j, person in enumerate(batch_enriched[:3]):  # Show first 3
                            email = person.get("email", "N/A")
                            is_real = email and "email_not_unlocked" not in email
                            logger.info(f"📧 Batch {batch_num} Person {j+1}: Email='{email}', Real={is_real}")

                    # Small delay between batches to respect rate limits
                    import asyncio
                    await asyncio.sleep(0.2)

                except Exception as batch_error:
                    logger.error(f"❌ Batch {batch_num} failed: {batch_error}")
                    import traceback
                    logger.error(f"❌ Batch {batch_num} traceback: {traceback.format_exc()}")
                    continue

            # Log final enrichment results
            real_emails_count = sum(1 for person in all_enriched_people
                                  if person.get("email") and "email_not_unlocked" not in person.get("email", ""))

            logger.info(f"🎯 Bulk enrichment completed: {len(all_enriched_people)} people returned, {real_emails_count} with real emails")
            print(f"   🎯 Enrichment completed: {len(all_enriched_people)} contacts processed")
            print(f"   📧 Email results: {real_emails_count} real emails unlocked")

            # Show final email breakdown
            if all_enriched_people:
                locked_count = len(all_enriched_people) - real_emails_count
                logger.info(f"📊 Email breakdown: {real_emails_count} real, {locked_count} locked")
                print(f"   📊 Email breakdown: {real_emails_count} real, {locked_count} still locked")

                # Show sample of final emails
                sample_emails = [person.get("email", "N/A") for person in all_enriched_people[:5]]
                logger.info(f"📧 Sample final emails: {sample_emails}")

            return all_enriched_people

        except Exception as e:
            logger.error(f"❌ Bulk enrichment failed: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return []

    async def search_people_with_pagination(
        self,
        filters: Dict[str, Any],
        target_count: int = 100,
        start_page: int = 1
    ) -> List[Dict[str, Any]]:
        """Search for people with pagination to get target count"""
        logger.info(f"🔍 Starting people search with filters: {filters}")
        logger.info(f"🎯 Target count: {target_count}, Starting from page: {start_page}")

        print(f"🔍 Apollo.io Search Progress:")
        print(f"   🎯 Target: {target_count} leads")
        print(f"   📋 Filters: {filters}")
        print(f"   📄 Starting from page: {start_page}")

        all_people = []
        page = start_page
        per_page = 25  # Apollo.io limit

        while len(all_people) < target_count:
            try:
                # Build search query
                search_data = {
                    "page": page,
                    "per_page": per_page,
                    **filters
                }

                # Remove empty filters
                search_data = {k: v for k, v in search_data.items() if v}

                logger.info(f"📄 Page {page}: Searching with data: {search_data}")
                print(f"   📄 Page {page}: Searching...")

                # Use POST request as per Apollo.io official docs
                resp = await self._request("POST", "/mixed_people/search", data=search_data)
                people = resp.get("people", [])

                logger.info(f"📄 Page {page}: Found {len(people)} people")
                print(f"   📄 Page {page}: Found {len(people)} people (Total so far: {len(all_people) + len(people)})")

                # Log sample person data
                if people:
                    sample_person = people[0]
                    logger.info(f"📄 Page {page}: Sample person - ID: {sample_person.get('id')}, "
                              f"Name: {sample_person.get('first_name')} {sample_person.get('last_name')}, "
                              f"Email: {sample_person.get('email', 'N/A')}")

                    print(f"   📄 Page {page}: Sample - {sample_person.get('first_name')} {sample_person.get('last_name')}, "
                          f"{sample_person.get('title', 'Unknown Title')} at {sample_person.get('organization', {}).get('name', 'Unknown Company')}")

                if not people:
                    logger.info(f"📄 Page {page}: No more results, stopping pagination")
                    print(f"   📄 Page {page}: No more results found")
                    break  # No more results

                all_people.extend(people)
                page += 1

                logger.info(f"📊 Total people collected so far: {len(all_people)}")
                print(f"   📊 Progress: {len(all_people)}/{target_count} leads collected")

                # Respect rate limits
                import asyncio
                await asyncio.sleep(0.1)  # Small delay between requests

            except Exception as e:
                logger.error(f"❌ Error in people search pagination page {page}: {e}")
                break

        final_people = all_people[:target_count]
        logger.info(f"✅ People search completed: {len(final_people)} people returned")
        print(f"   ✅ Search completed: {len(final_people)} leads ready for enrichment")

        return final_people

    # Add more methods as needed for rate limiting, error handling, data mapping, and webhooks

# Singleton instance (to be imported elsewhere)
apollo_service = ApolloService() 