"""
Agent router
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import logging
from typing import Optional, List

from app.modules.agents.models import (
    AgentType,
    TaskStatus,
    AgentInfo,
    AgentTask,
    CreateAgentTaskRequest,
    AgentTaskResponse,
    AgentListResponse,
    AgentTaskListResponse,
    AgentMetricsResponse,
    AgentConfigurationUpdate,
    AgentConfigurationResponse
)
from app.modules.agents.service import agent_service
from app.shared.models import BaseResponse, PaginationParams
from app.shared.dependencies import (
    get_current_active_user,
    get_current_organization,
    require_role
)
from app.core.exceptions import (
    NotFoundError,
    ConflictError,
    ValidationError,
    ExternalServiceError
)

logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Security
security = HTTPBearer()

# Router
router = APIRouter()


@router.get("/", response_model=AgentListResponse)
async def get_available_agents():
    """Get list of all available agents"""
    try:
        agents = await agent_service.get_available_agents()
        return AgentListResponse(
            success=True,
            message="Available agents retrieved successfully",
            agents=agents
        )
    except Exception as e:
        logger.error(f"Error getting available agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get available agents"
        )


@router.get("/{agent_type}", response_model=AgentInfo)
async def get_agent_info(
    agent_type: AgentType,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get detailed information about a specific agent"""
    try:
        agent_info = await agent_service.get_agent_info(
            organization_id=organization['id'],
            agent_type=agent_type
        )
        return agent_info
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting agent info for {agent_type}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent information"
        )


@router.post("/tasks", response_model=AgentTaskResponse)
@limiter.limit("10/minute")
async def create_agent_task(
    request: Request,
    task_request: CreateAgentTaskRequest,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Create and execute an agent task"""
    try:
        task = await agent_service.create_agent_task(
            organization_id=organization['id'],
            request=task_request
        )
        
        return AgentTaskResponse(
            success=True,
            message="Agent task created and executed successfully",
            task=task
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ExternalServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating agent task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create agent task"
        )


@router.get("/tasks", response_model=AgentTaskListResponse)
async def get_agent_tasks(
    agent_type: Optional[AgentType] = Query(None, description="Filter by agent type"),
    status: Optional[TaskStatus] = Query(None, description="Filter by task status"),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get agent tasks with optional filtering"""
    try:
        offset = (page - 1) * limit
        
        tasks = await agent_service.get_agent_tasks(
            organization_id=organization['id'],
            agent_type=agent_type,
            status=status,
            limit=limit,
            offset=offset
        )
        
        # Get total count for pagination
        total = len(tasks)  # Simplified - in production, implement proper counting
        
        return AgentTaskListResponse(
            success=True,
            message="Agent tasks retrieved successfully",
            tasks=tasks,
            total=total,
            page=page,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error getting agent tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent tasks"
        )


@router.get("/tasks/{task_id}", response_model=AgentTask)
async def get_agent_task(
    task_id: str,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get specific agent task by ID"""
    try:
        task = await agent_service.get_agent_task(task_id)
        
        # Check if task belongs to user's organization
        if task.organization_id != organization['id']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: Task not in your organization"
            )
        
        return task
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting agent task {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent task"
        )


@router.put("/{agent_type}/configuration", response_model=AgentConfigurationResponse)
async def update_agent_configuration(
    agent_type: AgentType,
    config_update: AgentConfigurationUpdate,
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization),
    _: dict = Depends(require_role("admin"))  # Only admins can update agent configs
):
    """Update agent configuration"""
    try:
        configuration = await agent_service.update_agent_configuration(
            organization_id=organization['id'],
            agent_type=agent_type,
            update_data=config_update
        )
        
        return AgentConfigurationResponse(
            success=True,
            message="Agent configuration updated successfully",
            configuration=configuration
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating agent configuration for {agent_type}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update agent configuration"
        )


@router.post("/crew/execute", response_model=BaseResponse)
@limiter.limit("5/minute")
async def execute_crew_task(
    request: Request,
    crew_request: dict,  # Flexible input for crew tasks
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Execute a multi-agent crew task"""
    try:
        # Validate crew request
        agent_types = crew_request.get("agent_types", [])
        task_description = crew_request.get("task_description", "")
        input_data = crew_request.get("input_data", {})
        
        if not agent_types:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="agent_types is required"
            )
        
        if not task_description:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="task_description is required"
            )
        
        # Convert string agent types to enum
        try:
            agent_type_enums = [AgentType(agent_type) for agent_type in agent_types]
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Invalid agent type: {e}"
            )
        
        result = await agent_service.execute_crew_task(
            organization_id=organization['id'],
            agent_types=agent_type_enums,
            task_description=task_description,
            input_data=input_data
        )
        
        return BaseResponse(
            success=True,
            message="Crew task executed successfully",
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing crew task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute crew task"
        )


@router.get("/metrics", response_model=AgentMetricsResponse)
async def get_agent_metrics(
    current_user: dict = Depends(get_current_active_user),
    organization: dict = Depends(get_current_organization)
):
    """Get metrics for all agents in the organization"""
    try:
        metrics = {}
        
        # Get metrics for each available agent type
        for agent_type in AgentType:
            try:
                agent_metrics = await agent_service._get_agent_metrics(
                    organization['id'], agent_type
                )
                metrics[agent_type] = agent_metrics
            except Exception as e:
                logger.warning(f"Failed to get metrics for {agent_type}: {e}")
                continue
        
        return AgentMetricsResponse(
            success=True,
            message="Agent metrics retrieved successfully",
            metrics=metrics
        )
        
    except Exception as e:
        logger.error(f"Error getting agent metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent metrics"
        )


# Note: Rate limit exception handler is added to the main app, not the router
