#!/usr/bin/env python3
"""
Test script to verify keyword generation improvements
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.campaign_planning import CampaignPlanningAgent

async def test_keyword_generation():
    """Test the improved keyword generation"""
    print("🧪 Testing Keyword Generation Improvements")
    print("="*50)
    
    # Test goal understanding agent
    print("\n1. Testing Goal Understanding Agent Keywords:")
    goal_agent = GoalUnderstandingAgent("test-org")
    
    test_goal = {
        "goal_description": "I need 1 meeting with hospital owner in london",
        "context": "Test context",
        "user_id": "test-user",
        "organization_id": "test-org",
        "conversation_history": [],
        "is_follow_up": False,
        "answered_questions": {}
    }
    
    try:
        result = await goal_agent.execute(test_goal)
        parsed_goal = result.get('parsed_goal', {})
        apollo_keywords = parsed_goal.get('apollo_keywords', {})
        
        print(f"   🎯 Primary keywords: {apollo_keywords.get('primary_keywords', 'N/A')}")
        print(f"   🔗 Related keywords: {apollo_keywords.get('related_keywords', [])}")
        print(f"   📝 Reasoning: {apollo_keywords.get('reasoning', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ Goal understanding failed: {e}")
    
    # Test campaign planning agent
    print("\n2. Testing Campaign Planning Agent Keywords:")
    campaign_agent = CampaignPlanningAgent("test-org")
    
    campaign_input = {
        "campaign_id": "test-campaign",
        "organization_id": "test-org",
        "goal_description": "I need 1 meeting with hospital owner in london",
        "target_meetings": 1,
        "target_industry": "healthcare",
        "target_location": "london",
        "user_preferences": {},
        "parsed_goal": {
            "apollo_keywords": {
                "primary_keywords": "hospital",
                "related_keywords": ["hospital", "healthcare", "medical", "clinic"],
                "reasoning": "Generic keywords for high lead volume"
            }
        }
    }
    
    try:
        # Test the enhanced keyword strategy method directly
        keyword_strategy = await campaign_agent._create_enhanced_keyword_strategy(
            "healthcare", 
            "I need 1 meeting with hospital owner in london",
            campaign_input["parsed_goal"]
        )
        
        print(f"   🎯 Primary keywords: {keyword_strategy.get('primary_keywords', 'N/A')}")
        print(f"   🔗 Related keywords: {keyword_strategy.get('related_keywords', [])}")
        print(f"   📝 Strategy: {keyword_strategy.get('strategy', 'N/A')}")
        print(f"   🔍 Source: {keyword_strategy.get('source', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ Campaign planning failed: {e}")
    
    # Test fallback keyword strategy
    print("\n3. Testing Fallback Keyword Strategy:")
    try:
        fallback_strategy = campaign_agent._create_keyword_strategy("healthcare", "hospital meeting")
        
        print(f"   🎯 Primary keywords: {fallback_strategy.get('primary_keywords', 'N/A')}")
        print(f"   🔗 Related keywords: {fallback_strategy.get('related_keywords', [])}")
        print(f"   📝 Strategy: {fallback_strategy.get('strategy', 'N/A')}")
        print(f"   🔍 Source: {fallback_strategy.get('source', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ Fallback strategy failed: {e}")
    
    print("\n✅ Keyword generation test completed!")

if __name__ == "__main__":
    asyncio.run(test_keyword_generation())
