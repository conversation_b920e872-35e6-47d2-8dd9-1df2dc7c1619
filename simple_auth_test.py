#!/usr/bin/env python3
"""
Simple test for auth endpoints
"""

import requests
import json

def test_auth():
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Authentication...")
    
    # Test 1: Health check
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✅ Health check: {health_response.status_code}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return
    
    # Test 2: Login
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "Itblast@123"
        }
        login_response = requests.post(
            f"{base_url}/api/v1/auth/login", 
            json=login_data,
            timeout=10
        )
        print(f"✅ Login: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print(f"📝 Token received: {access_token[:50]}...")
            
            # Test 3: Auth Me
            try:
                headers = {"Authorization": f"Bearer {access_token}"}
                me_response = requests.get(
                    f"{base_url}/api/v1/auth/me",
                    headers=headers,
                    timeout=10
                )
                print(f"🔍 Auth Me: {me_response.status_code}")
                
                if me_response.status_code == 200:
                    print("✅ SUCCESS: Auth/me endpoint is working!")
                    print(f"📄 Response: {me_response.json()}")
                else:
                    print("❌ FAILED: Auth/me endpoint not working")
                    print(f"📄 Response: {me_response.text}")
                    
            except Exception as e:
                print(f"❌ Auth/me request failed: {e}")
        else:
            print(f"❌ Login failed: {login_response.text}")
            
    except Exception as e:
        print(f"❌ Login request failed: {e}")

if __name__ == "__main__":
    test_auth()
