#!/usr/bin/env python3
"""
Real Interactive Chat Interface for Selda AI
This version works with actual user input and handles the complete workflow
"""

import asyncio
import sys
import os
import uuid
from typing import Dict, Any, Optional, List

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.agents.goal_understanding import GoalUnderstandingAgent
from app.modules.agents.campaign_planning import CampaignPlanningAgent
from app.modules.agents.lead_sourcing import LeadSourcingAgent

def safe_input(prompt: str) -> str:
    """Safe input function that handles interrupts gracefully"""
    try:
        print(prompt, end="", flush=True)
        return input().strip()
    except (KeyboardInterrupt, EOFError):
        print("\n🤖 Session ended. Goodbye! 👋")
        sys.exit(0)

def print_banner():
    """Print welcome banner"""
    print("\n" + "="*70)
    print("🤖 SELDA AI - Interactive Lead Sourcing Assistant")
    print("="*70)
    print("Welcome! I'm your AI assistant for sourcing high-quality sales leads.")
    print("\n💡 How it works:")
    print("  1. Tell me your sales goal in natural language")
    print("  2. I'll ask follow-up questions if needed")
    print("  3. I'll source targeted leads from Apollo.io")
    print("  4. You'll get a complete campaign summary")
    print("\n📝 Example goals:")
    print("  • 'I need 10 meetings with restaurant owners in London'")
    print("  • 'Find me tech startup CEOs in San Francisco for 5 meetings'")
    print("  • 'Source leads for SaaS companies in New York'")
    print("\n⌨️ Commands:")
    print("  • Type 'help' for assistance")
    print("  • Type 'quit' or 'exit' to end session")
    print("  • Just type naturally - I understand!")
    print("-"*70)

def show_help():
    """Show help information"""
    print("\n" + "="*50)
    print("📚 HELP - How to use Selda AI")
    print("="*50)
    print("🎯 Goal Examples:")
    print("  • 'I need 5 meetings with restaurant owners in London'")
    print("  • 'Find 10 tech CEOs in San Francisco'")
    print("  • 'Source leads for healthcare companies in Boston'")
    print("  • 'Get me 3 meetings with SaaS founders in New York'")
    print("\n📝 What I need to know:")
    print("  • Industry (restaurants, technology, healthcare, etc.)")
    print("  • Location (city, country, or region)")
    print("  • Number of meetings you want")
    print("  • Company size (optional: small, medium, large)")
    print("\n⌨️ Commands:")
    print("  • 'help' - Show this help")
    print("  • 'quit' or 'exit' - End session")
    print("  • Just type naturally - I'll understand!")
    print("-"*50)

async def process_goal(goal_description: str, organization_id: str, session_id: str) -> Optional[Dict[str, Any]]:
    """Process user goal and handle follow-up questions"""
    print("\n🤖 Let me understand your goal...")
    
    goal_agent = GoalUnderstandingAgent(organization_id)
    
    # Process initial goal
    goal_input = {
        "goal_description": goal_description,
        "context": "Interactive chat interface for lead sourcing",
        "user_id": f"chat-user-{session_id}",
        "organization_id": organization_id,
        "conversation_history": [],
        "is_follow_up": False,
        "answered_questions": {}
    }
    
    result = await goal_agent.execute(goal_input)
    
    # Check if goal is complete
    if result.get("is_complete", False):
        parsed_goal = result.get('parsed_goal', {})
        target_audience = parsed_goal.get('target_audience', {})
        
        print("\n🤖 Perfect! I have all the information I need:")
        print(f"   🎯 Industry: {target_audience.get('industry', 'N/A')}")
        print(f"   📍 Location: {target_audience.get('geography', 'N/A')}")
        print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 'N/A')} meetings")
        print(f"   ✅ Confidence: {result.get('confidence_score', 0):.1%}")
        
        return {
            "industry": target_audience.get('industry', ''),
            "location": target_audience.get('geography', ''),
            "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 5),
            "goal_description": goal_description,
            "parsed_goal": parsed_goal,
            "complete": True
        }
    
    # Handle follow-up questions with improved logic
    follow_up_questions = result.get("clarification_questions", [])
    if follow_up_questions:
        print(f"\n🤖 I need some additional information to help you better:")
        print(f"   📝 {len(follow_up_questions)} question(s) to clarify your goal")

        answers = {}
        for i, question in enumerate(follow_up_questions, 1):
            while True:
                answer = safe_input(f"\n💬 ({i}/{len(follow_up_questions)}) {question}\n👤 ")

                if answer.lower() == 'help':
                    show_help()
                    continue
                elif answer.lower() in ['quit', 'exit']:
                    print("\n🤖 Thank you for using Selda AI! Goodbye! 👋")
                    return None
                elif answer.strip():
                    break
                else:
                    print("🤖 Please provide an answer to continue.")

            # Improved answer mapping using AI-like logic
            question_lower = question.lower()
            if any(keyword in question_lower for keyword in ["industry", "targeting", "market", "business"]):
                answers["industry"] = answer
            elif any(keyword in question_lower for keyword in ["location", "geographic", "where", "region", "city"]):
                answers["location"] = answer
            elif any(keyword in question_lower for keyword in ["product", "service", "sell", "offer", "solution"]):
                answers["purpose"] = answer
            elif any(keyword in question_lower for keyword in ["value proposition", "benefit", "key benefit"]):
                answers["value_proposition"] = answer
            elif any(keyword in question_lower for keyword in ["pain points", "problems", "challenges", "issues"]):
                answers["pain_points"] = answer
            elif any(keyword in question_lower for keyword in ["company name", "company", "description", "what your company does"]):
                answers["company_info"] = answer
            elif any(keyword in question_lower for keyword in ["meeting", "target", "how many", "number"]):
                answers["target_meetings"] = answer
            elif any(keyword in question_lower for keyword in ["company size", "size", "employees", "business size"]):
                answers["company_size"] = answer
            else:
                # Use a more generic key for other questions
                key = f"question_{i}"
                if "budget" in question_lower:
                    key = "budget"
                elif "timeline" in question_lower:
                    key = "timeline"
                answers[key] = answer
        
        # Process follow-up answers
        print("\n🤖 Thank you! Let me process your answers...")
        
        follow_up_input = {
            "goal_description": goal_description,
            "context": "Interactive chat interface follow-up",
            "user_id": f"chat-user-{session_id}",
            "organization_id": organization_id,
            "conversation_history": [],
            "is_follow_up": True,
            "answered_questions": answers
        }
        
        final_result = await goal_agent.execute(follow_up_input)
        
        if final_result.get("is_complete", False):
            parsed_goal = final_result.get('parsed_goal', {})
            target_audience = parsed_goal.get('target_audience', {})
            
            print("\n🤖 Excellent! Now I have everything I need:")
            print(f"   🎯 Industry: {target_audience.get('industry', 'N/A')}")
            print(f"   📍 Location: {target_audience.get('geography', 'N/A')}")
            print(f"   🔢 Target: {parsed_goal.get('metrics', {}).get('target_value', 'N/A')} meetings")
            print(f"   ✅ Confidence: {final_result.get('confidence_score', 0):.1%}")
            
            return {
                "industry": target_audience.get('industry', ''),
                "location": target_audience.get('geography', ''),
                "target_meetings": parsed_goal.get('metrics', {}).get('target_value', 5),
                "goal_description": goal_description,
                "parsed_goal": parsed_goal,
                "complete": True
            }
        else:
            print("\n🤖 I still need more information. Let me ask a few more questions...")
            return await process_goal(goal_description, organization_id, session_id)  # Recursive
    
    return None

async def plan_campaign(goal_result: Dict[str, Any], organization_id: str, session_id: str) -> Dict[str, Any]:
    """Plan the campaign using the Campaign Planning Agent"""
    print("\n" + "="*20 + " CAMPAIGN PLANNING " + "="*20)
    print("\n🤖 Let me create a comprehensive campaign plan for your goal...")

    # Initialize Campaign Planning Agent
    campaign_agent = CampaignPlanningAgent(organization_id)

    # Prepare input for campaign planning agent
    campaign_input = {
        "campaign_id": f"chat-campaign-{session_id}",
        "organization_id": organization_id,
        "goal_description": goal_result.get("goal_description", ""),
        "target_meetings": goal_result.get("target_meetings", 5),
        "target_industry": goal_result.get("industry", ""),
        "target_location": goal_result.get("location", ""),
        "user_preferences": {},
        "parsed_goal": goal_result.get("parsed_goal", {})
    }

    try:
        # Execute campaign planning
        campaign_plan = await campaign_agent.execute(campaign_input)

        # Display the planning results
        display_campaign_planning_results(campaign_plan)

        return campaign_plan

    except Exception as e:
        print(f"❌ Campaign planning failed: {e}")
        # Fallback to basic planning
        return {
            "lead_volume_plan": {"total_leads_needed": goal_result.get("target_meetings", 5) * 20},
            "source_distribution": {"apollo": goal_result.get("target_meetings", 5) * 20},
            "campaign_timeline": {"total_duration": 14, "email_phases": []},
            "target_meetings": goal_result.get("target_meetings", 5)
        }

def display_campaign_planning_results(campaign_plan: Dict[str, Any]) -> None:
    """Display campaign planning results from the agent"""
    lead_volume_plan = campaign_plan.get("lead_volume_plan", {})
    source_distribution = campaign_plan.get("source_distribution", {})
    campaign_timeline = campaign_plan.get("campaign_timeline", {})
    keyword_strategy = campaign_plan.get("keyword_strategy", {})
    success_metrics = campaign_plan.get("success_metrics", {})

    # Display lead volume calculation
    print(f"\n📊 Lead Volume Requirements:")
    print(f"   🎯 Target meetings: {lead_volume_plan.get('target_meetings', 0)}")
    print(f"   📈 Required leads: {lead_volume_plan.get('total_leads_needed', 0)}")
    print(f"   📊 Conversion assumptions:")
    print(f"      • Email open rate: {lead_volume_plan.get('email_open_rate', 0)}%")
    print(f"      • Response rate: {lead_volume_plan.get('response_rate', 0)}%")
    print(f"      • Meeting booking rate: {lead_volume_plan.get('meeting_rate', 0)}%")

    # Display source distribution
    print(f"\n🔗 Lead Source Distribution:")
    total_leads = lead_volume_plan.get('total_leads_needed', 1)
    for source, count in source_distribution.items():
        if count > 0:
            percentage = (count / total_leads) * 100
            print(f"   📡 {source.title()}: {count} leads ({percentage:.1f}%)")

    # Display enhanced keyword strategy
    print(f"\n🔑 Enhanced Keyword Strategy:")
    print(f"   🎯 Primary keywords: {keyword_strategy.get('primary_keywords', 'N/A')}")
    print(f"   🔗 Related keywords: {len(keyword_strategy.get('related_keywords', []))} variations available")
    print(f"   📝 Strategy: {keyword_strategy.get('strategy', 'N/A')}")

    # Show keyword source if available
    keyword_source = keyword_strategy.get('source', '')
    if keyword_source:
        source_display = {
            'goal_understanding_ai': '🤖 AI-generated from goal analysis',
            'campaign_planning_ai': '🧠 AI-generated from campaign context',
            'fallback_mapping': '📋 Industry mapping fallback'
        }
        print(f"   🔍 Source: {source_display.get(keyword_source, keyword_source)}")

    # Display campaign timeline with real dates
    print(f"\n📅 Campaign Timeline:")
    print(f"   🚀 Campaign starts: {campaign_timeline.get('campaign_start_date', 'N/A')}")
    print(f"   🏁 Campaign ends: {campaign_timeline.get('campaign_end_date', 'N/A')}")
    print(f"   ⏰ Total duration: {campaign_timeline.get('total_duration', 0)} days")

    # Display sourcing phase
    sourcing_phase = campaign_timeline.get('sourcing_phase', {})
    if sourcing_phase:
        print(f"\n📊 Lead Sourcing Phase:")
        print(f"   📅 Start: {sourcing_phase.get('start_date', 'N/A')}")
        print(f"   📅 End: {sourcing_phase.get('end_date', 'N/A')}")
        print(f"   � {sourcing_phase.get('description', 'N/A')}")

    print(f"\n�📧 Email Campaign Schedule:")
    for phase in campaign_timeline.get('email_phases', []):
        print(f"   • {phase.get('name', '')}:")
        print(f"     📅 {phase.get('human_schedule', 'Schedule TBD')}")
        print(f"     📝 {phase.get('description', '')}")
        print(f"     📊 {phase.get('leads_per_day', 0)} leads per day")
        print()

    # Display expected results
    print(f"\n📈 Expected Results:")
    print(f"   📧 Expected opens: {success_metrics.get('expected_opens', 0)}")
    print(f"   💬 Expected responses: {success_metrics.get('expected_responses', 0)}")
    print(f"   🤝 Expected meetings: {success_metrics.get('expected_meetings', 0)}")

def calculate_lead_volume(target_meetings: int) -> Dict[str, Any]:
    """Calculate required lead volume based on industry conversion rates"""
    # More realistic conversion rates for B2B cold outreach
    email_open_rate = 30  # 30% open rate
    response_rate = 8     # 8% response rate from opens
    meeting_rate = 40     # 40% of responses convert to meetings

    # Calculate backwards from target meetings
    responses_needed = target_meetings / (meeting_rate / 100)
    opens_needed = responses_needed / (response_rate / 100)
    total_leads_needed = opens_needed / (email_open_rate / 100)

    # Round up and add buffer, but cap at reasonable maximum
    total_leads_needed = int(total_leads_needed * 1.2)  # 20% buffer
    total_leads_needed = min(total_leads_needed, target_meetings * 100)  # Cap at 100x target meetings

    return {
        "target_meetings": target_meetings,
        "total_leads_needed": total_leads_needed,
        "responses_needed": int(responses_needed),
        "opens_needed": int(opens_needed),
        "email_open_rate": email_open_rate,
        "response_rate": response_rate,
        "meeting_rate": meeting_rate,
        "buffer_applied": "20%"
    }

def plan_lead_sources(total_leads_needed: int) -> Dict[str, int]:
    """Plan distribution across multiple lead sources"""
    # Currently using Apollo.io as primary source (100%)
    # Other sources will be added in future phases
    distribution = {
        "apollo": total_leads_needed,  # 100% from Apollo.io for now
        "linkedin": 0,  # Future implementation
        "local_directories": 0  # Future implementation
    }

    return distribution

def create_campaign_timeline(target_meetings: int, total_leads: int) -> Dict[str, Any]:
    """Create detailed campaign timeline with email sequences"""

    # Calculate campaign duration based on lead volume
    leads_per_day = 100  # Increased daily sending limit for faster execution
    sourcing_days = max(2, (total_leads // 200) + 1)  # Faster sourcing

    # Start outreach immediately after planning, with 2-3 day intervals
    timeline = {
        "total_duration": sourcing_days + 12,  # Sourcing + 12 days outreach (faster cycle)
        "sourcing_phase": {
            "duration": sourcing_days,
            "description": f"Source {total_leads} leads from Apollo.io"
        },
        "email_phases": [
            {
                "name": "Initial Outreach",
                "start_day": 1,  # Start immediately after planning
                "end_day": 2,
                "description": "Send personalized cold emails to all leads",
                "leads_per_day": leads_per_day,
                "frequency": "Immediate start"
            },
            {
                "name": "First Follow-up",
                "start_day": 4,  # 2-3 days after initial
                "end_day": 5,
                "description": "Follow up with non-responders (value-add content)",
                "leads_per_day": leads_per_day,
                "frequency": "2-3 days after initial"
            },
            {
                "name": "Second Follow-up",
                "start_day": 7,  # 3 days after first follow-up
                "end_day": 8,
                "description": "Final follow-up with case study/social proof",
                "leads_per_day": leads_per_day,
                "frequency": "3 days after first follow-up"
            },
            {
                "name": "Break-up Email",
                "start_day": 11,  # 3-4 days after second follow-up
                "end_day": 11,
                "description": "Polite break-up email to remaining non-responders",
                "leads_per_day": leads_per_day,
                "frequency": "3-4 days after second follow-up"
            }
        ]
    }

    return timeline

def generate_related_keywords(industry: str, goal_description: str = "") -> Dict[str, Any]:
    """Generate related keywords for better lead sourcing"""

    # Extract additional context from goal description
    goal_lower = goal_description.lower()

    # Enhanced keyword mapping with related terms
    keyword_mapping = {
        "restaurant": {
            "primary": "restaurant food dining hospitality catering",
            "related": [
                "restaurant owner manager",
                "food service dining",
                "hospitality catering",
                "bar pub bistro",
                "cafe coffee shop",
                "fast food quick service",
                "fine dining upscale restaurant"
            ]
        },
        "restaurants": {
            "primary": "restaurant food dining hospitality catering",
            "related": [
                "restaurant owner manager",
                "food service dining",
                "hospitality catering",
                "bar pub bistro",
                "cafe coffee shop",
                "fast food quick service",
                "fine dining upscale restaurant"
            ]
        },
        "healthcare": {
            "primary": "healthcare medical health clinic hospital",
            "related": [
                "medical practice clinic",
                "healthcare provider",
                "hospital medical center",
                "dental practice dentist",
                "pharmacy pharmaceutical",
                "medical device healthcare",
                "telemedicine health tech"
            ]
        },
        "technology": {
            "primary": "technology software tech IT development",
            "related": [
                "software development tech",
                "IT services technology",
                "SaaS software platform",
                "startup tech company",
                "digital transformation",
                "cloud computing services",
                "cybersecurity technology"
            ]
        },
        "retail": {
            "primary": "retail store shop commerce ecommerce",
            "related": [
                "retail store manager",
                "ecommerce online store",
                "fashion retail clothing",
                "electronics retail",
                "grocery retail supermarket",
                "specialty retail boutique",
                "department store retail"
            ]
        }
    }

    # Get base keywords
    base_industry = industry.lower()
    keywords_data = keyword_mapping.get(base_industry, {
        "primary": industry,
        "related": [industry]
    })

    # Add context-specific keywords based on goal description
    if "pos" in goal_lower or "point of sale" in goal_lower:
        keywords_data["related"].append("point of sale POS system")
        keywords_data["related"].append("payment processing retail")

    if "software" in goal_lower or "system" in goal_lower:
        keywords_data["related"].append("software system technology")
        keywords_data["related"].append("business software solution")

    if "marketing" in goal_lower:
        keywords_data["related"].append("marketing advertising agency")
        keywords_data["related"].append("digital marketing services")

    return {
        "primary_keywords": keywords_data["primary"],
        "related_keywords": keywords_data["related"],
        "total_variations": len(keywords_data["related"]) + 1
    }

def combine_sourcing_results(results_list: List[Dict[str, Any]], keywords_used: List[str]) -> Dict[str, Any]:
    """Combine multiple sourcing results into a single result"""
    if not results_list:
        return {
            "leads_sourced": 0,
            "duplicates_filtered": 0,
            "cooldown_filtered": 0,
            "quality_distribution": {"high": 0, "medium": 0, "low": 0},
            "leads_by_source": {"apollo": 0},
            "keywords_used": keywords_used
        }

    # Combine all metrics
    total_leads = sum(r.get("leads_sourced", 0) for r in results_list)
    total_duplicates = sum(r.get("duplicates_filtered", 0) for r in results_list)
    total_cooldown = sum(r.get("cooldown_filtered", 0) for r in results_list)

    # Combine quality distribution
    combined_quality = {"high": 0, "medium": 0, "low": 0}
    for result in results_list:
        quality_dist = result.get("quality_distribution", {})
        for quality, count in quality_dist.items():
            combined_quality[quality] = combined_quality.get(quality, 0) + count

    # Combine sources
    combined_sources = {"apollo": total_leads}

    return {
        "leads_sourced": total_leads,
        "duplicates_filtered": total_duplicates,
        "cooldown_filtered": total_cooldown,
        "quality_distribution": combined_quality,
        "leads_by_source": combined_sources,
        "keywords_used": keywords_used
    }

async def source_leads(goal_result: Dict[str, Any], campaign_plan: Dict[str, Any], organization_id: str, session_id: str) -> Dict[str, Any]:
    """Source leads using the enhanced Lead Sourcing Agent"""
    print("\n" + "="*20 + " LEAD SOURCING " + "="*20)
    print("\n🤖 Now I'll source leads according to the campaign plan...")

    # Initialize Lead Sourcing Agent
    lead_agent = LeadSourcingAgent(organization_id)

    # Get data from campaign plan
    source_distribution = campaign_plan.get("source_distribution", {})
    keyword_strategy = campaign_plan.get("keyword_strategy", {})
    campaign_plan_id = campaign_plan.get("campaign_plan_id", "")

    lead_count_target = source_distribution.get("apollo", 100)
    target_meetings = goal_result.get("target_meetings", 5)

    print(f"\n🔍 Sourcing {lead_count_target} leads for {target_meetings} meetings...")
    print(f"   🎯 Industry: {goal_result.get('industry', 'N/A')}")
    print(f"   📍 Location: {goal_result.get('location', 'N/A')}")
    print(f"   📊 Quality Threshold: 50%")
    print(f"   📡 Primary source: Apollo.io (100%)")

    # Display enhanced keyword strategy from campaign planning
    if keyword_strategy:
        print(f"\n🔑 Enhanced Keyword Strategy (from Campaign Planning):")
        print(f"   🎯 Primary keywords: {keyword_strategy.get('primary_keywords', 'N/A')}")
        print(f"   🔗 Related keywords: {len(keyword_strategy.get('related_keywords', []))} variations")
        print(f"   📝 Strategy: {keyword_strategy.get('strategy', 'N/A')}")

        # Show keyword source
        keyword_source = keyword_strategy.get('source', '')
        if keyword_source:
            source_display = {
                'goal_understanding_ai': '🤖 AI-generated from goal analysis',
                'campaign_planning_ai': '🧠 AI-generated from campaign context',
                'fallback_mapping': '📋 Industry mapping fallback'
            }
            print(f"   🔍 Source: {source_display.get(keyword_source, keyword_source)}")

    # Show progress
    print(f"\n⏳ Processing...")
    print("   🔍 Searching Apollo.io database with enhanced keywords...")
    print("   📧 Enriching contact emails...")
    print("   🎯 Filtering for quality...")
    print("   📊 Calculating lead scores...")
    print("   💾 Storing leads...")

    # Prepare input for lead sourcing agent
    sourcing_input = {
        "campaign_id": f"chat-campaign-{session_id}",
        "target_audience": {
            "industry": goal_result.get("industry", ""),
            "geography": goal_result.get("location", ""),
            "company_size": "any",
            "titles": ["CEO", "Owner", "Manager", "Director"]
        },
        "industry": goal_result.get("industry", ""),
        "location": goal_result.get("location", ""),
        "lead_count_target": lead_count_target,
        "quality_threshold": 50,
        "sources": ["apollo"],
        "keyword_strategy": keyword_strategy,
        "campaign_plan_id": campaign_plan_id
    }

    try:
        # Execute lead sourcing with enhanced agent
        sourcing_result = await lead_agent.execute(sourcing_input)

        print(f"\n✅ Lead sourcing completed!")

        return sourcing_result

    except Exception as e:
        print(f"❌ Lead sourcing failed: {e}")
        return {
            "leads_sourced": 0,
            "duplicates_filtered": 0,
            "cooldown_filtered": 0,
            "quality_distribution": {"high": 0, "medium": 0, "low": 0},
            "leads_by_source": {"apollo": 0},
            "keywords_used": []
        }

def display_results(sourcing_result: Dict[str, Any]) -> None:
    """Display lead sourcing results with enhanced information"""
    leads_sourced = sourcing_result.get("leads_sourced", 0)
    duplicates = sourcing_result.get("duplicates_filtered", 0)
    cooldown = sourcing_result.get("cooldown_filtered", 0)
    keywords_used = sourcing_result.get("keywords_used", [])

    print("\n🤖 Lead sourcing completed! Here are the results:")
    print(f"   ✅ Leads sourced: {leads_sourced}")
    print(f"   🔄 Duplicates filtered: {duplicates}")
    print(f"   ⏰ Cooldown filtered: {cooldown}")

    # Show quality distribution
    quality_dist = sourcing_result.get("quality_distribution", {})
    if quality_dist:
        print(f"   📊 Quality distribution:")
        print(f"      🟢 High quality (80-100): {quality_dist.get('high', 0)} leads")
        print(f"      🟡 Medium quality (60-79): {quality_dist.get('medium', 0)} leads")
        print(f"      🔴 Low quality (50-59): {quality_dist.get('low', 0)} leads")

    # Show source breakdown
    sources = sourcing_result.get("leads_by_source", {})
    if sources:
        print(f"   🔗 Sources:")
        for source, count in sources.items():
            print(f"      📡 {source.title()}: {count} leads (100%)")

    # Add lead scoring and prioritization display
    if leads_sourced > 0:
        print(f"\n📈 Lead Scoring & Prioritization:")
        print(f"   🎯 Leads prioritized by:")
        print(f"      • Quality score (40% weight)")
        print(f"      • Title relevance (30% weight)")
        print(f"      • Company size match (20% weight)")
        print(f"      • Contact completeness (10% weight)")

        # Calculate priority distribution
        high_priority = quality_dist.get('high', 0)
        medium_priority = quality_dist.get('medium', 0) + int(quality_dist.get('low', 0) * 0.3)
        low_priority = quality_dist.get('low', 0) - int(quality_dist.get('low', 0) * 0.3)

        print(f"   📊 Priority distribution:")
        print(f"      🔥 High priority: {high_priority} leads (contact first)")
        print(f"      ⚡ Medium priority: {medium_priority} leads (contact second)")
        print(f"      📋 Low priority: {low_priority} leads (contact last)")

    # Show keywords used
    if keywords_used:
        print(f"\n🔑 Keywords Strategy Results:")
        for i, keyword in enumerate(keywords_used, 1):
            print(f"   {i}. '{keyword}' - {'✅ Used' if i <= len(keywords_used) else '⏸️ Available'}")



def display_campaign_execution_plan(campaign_plan: Dict[str, Any], sourcing_result: Dict[str, Any]):
    """Display detailed campaign execution plan"""
    print("\n" + "="*20 + " CAMPAIGN EXECUTION PLAN " + "="*20)

    leads_sourced = sourcing_result.get("leads_sourced", 0)
    timeline = campaign_plan.get("campaign_timeline", {})

    print("\n📅 Email Campaign Schedule:")
    print("┌─────────────────────────────────────────────────────────────────┐")
    print("│                    CAMPAIGN TIMELINE                           │")
    print("├─────────────────────────────────────────────────────────────────┤")

    for phase in timeline.get("email_phases", []):
        start_day = phase.get("start_day", 0)
        end_day = phase.get("end_day", 0)
        name = phase.get("name", "")
        description = phase.get("description", "")

        print(f"│ Day {start_day:2d}-{end_day:2d}: {name:<20} │")
        print(f"│         {description:<40} │")
        print("├─────────────────────────────────────────────────────────────────┤")

    print("└─────────────────────────────────────────────────────────────────┘")

    # Email sequence details
    print(f"\n📧 Email Sequence Details:")
    email_sequences = [
        {
            "sequence": 1,
            "day": timeline.get("email_phases", [{}])[0].get("start_day", 1),
            "subject": "Introduction & Value Proposition",
            "content": "Personalized introduction with clear value proposition",
            "cta": "Schedule a brief call to discuss your needs"
        },
        {
            "sequence": 2,
            "day": timeline.get("email_phases", [{}])[1].get("start_day", 7) if len(timeline.get("email_phases", [])) > 1 else 7,
            "subject": "Industry Insights & Case Study",
            "content": "Share relevant industry insights and success story",
            "cta": "Would you like to see how we helped [similar company]?"
        },
        {
            "sequence": 3,
            "day": timeline.get("email_phases", [{}])[2].get("start_day", 14) if len(timeline.get("email_phases", [])) > 2 else 14,
            "subject": "Social Proof & Testimonials",
            "content": "Customer testimonials and social proof",
            "cta": "Quick 15-minute call to explore opportunities"
        },
        {
            "sequence": 4,
            "day": timeline.get("email_phases", [{}])[3].get("start_day", 21) if len(timeline.get("email_phases", [])) > 3 else 21,
            "subject": "Final Follow-up",
            "content": "Polite break-up email with door left open",
            "cta": "Feel free to reach out when timing is better"
        }
    ]

    for seq in email_sequences:
        print(f"   📨 Email {seq['sequence']} (Day {seq['day']}):")
        print(f"      📋 Subject: {seq['subject']}")
        print(f"      📝 Content: {seq['content']}")
        print(f"      🎯 CTA: {seq['cta']}")
        print()

def display_summary(goal_result: Dict[str, Any], campaign_plan: Dict[str, Any], sourcing_result: Dict[str, Any]):
    """Display final campaign summary"""
    print("\n" + "="*20 + " CAMPAIGN SUMMARY " + "="*20)

    # Ensure values are integers to avoid comparison errors
    leads_sourced = int(sourcing_result.get("leads_sourced", 0))
    target_meetings = int(goal_result.get("target_meetings", 0))
    lead_volume_plan = campaign_plan.get("lead_volume_plan", {})

    print("\n🤖 🎉 Your comprehensive sales campaign is ready!")
    print(f"\n📋 Campaign Overview:")
    print(f"   🎯 Goal: {goal_result.get('goal_description', 'N/A')}")
    print(f"   🏭 Industry: {goal_result.get('industry', 'N/A')}")
    print(f"   🌍 Location: {goal_result.get('location', 'N/A')}")
    print(f"   🎯 Target Meetings: {target_meetings}")
    print(f"   📊 Leads Sourced: {leads_sourced}")
    print(f"   📈 Required Leads: {lead_volume_plan.get('total_leads_needed', 'N/A')}")

    if leads_sourced > 0:
        ratio = leads_sourced / target_meetings if target_meetings > 0 else 0
        total_leads_needed = int(lead_volume_plan.get('total_leads_needed', 1))
        coverage = (leads_sourced / total_leads_needed) * 100

        print(f"   📊 Lead-to-Meeting Ratio: {ratio:.1f}:1")
        print(f"   📈 Plan Coverage: {coverage:.1f}%")

        # Expected results
        expected_opens = int(leads_sourced * (lead_volume_plan.get('email_open_rate', 25) / 100))
        expected_responses = int(expected_opens * (lead_volume_plan.get('response_rate', 3) / 100))
        expected_meetings = int(expected_responses * (lead_volume_plan.get('meeting_rate', 30) / 100))

        print(f"\n📊 Expected Results:")
        print(f"   📧 Expected opens: {expected_opens} ({lead_volume_plan.get('email_open_rate', 25)}%)")
        print(f"   💬 Expected responses: {expected_responses} ({lead_volume_plan.get('response_rate', 3)}%)")
        print(f"   🤝 Expected meetings: {expected_meetings} ({lead_volume_plan.get('meeting_rate', 30)}%)")

        print("\n🤖 ✅ Success! Your campaign is ready for execution.")
        print("   💡 Next steps:")
        print("      1. Review the detailed execution plan above")
        print("      2. Set up email sequences in your CRM")
        print("      3. Start with high-priority leads first")
        print("      4. Monitor response rates and adjust messaging")
        print("      5. Track meetings booked and conversion rates")
    else:
        print("\n🤖 ⚠️ No leads were sourced. This could be due to:")
        print("   • Very specific targeting criteria")
        print("   • API limitations or credits")
        print("   • Temporary service issues")
        print("   💡 Try adjusting your criteria or contact support.")

async def main():
    """Main chat interface loop"""
    organization_id = "00000000-0000-4000-8000-000000000001"
    
    print_banner()
    
    while True:
        session_id = str(uuid.uuid4())
        
        # Get user goal
        while True:
            goal = safe_input("\n💬 What's your sales goal? (e.g., 'I need 5 meetings with restaurant owners in London')\n👤 ")
            
            if goal.lower() == 'help':
                show_help()
                continue
            elif goal.lower() in ['quit', 'exit', 'bye']:
                print("\n🤖 Thank you for using Selda AI! Goodbye! 👋")
                return
            elif goal.strip():
                break
            else:
                print("🤖 Please enter a valid goal description.")
        
        try:
            # Process goal
            goal_result = await process_goal(goal, organization_id, session_id)
            
            if not goal_result:
                continue
            
            # Plan campaign
            campaign_plan = await plan_campaign(goal_result, organization_id, session_id)

            # Source leads based on campaign plan
            sourcing_result = await source_leads(goal_result, campaign_plan, organization_id, session_id)

            # Display lead sourcing results
            display_results(sourcing_result)

            # Display campaign execution plan
            display_campaign_execution_plan(campaign_plan, sourcing_result)

            # Display summary
            display_summary(goal_result, campaign_plan, sourcing_result)
            
            # Ask to continue
            while True:
                continue_choice = safe_input("\n💬 Would you like to create another campaign? (yes/no)\n👤 ")
                
                if continue_choice.lower() == 'help':
                    show_help()
                    continue
                elif continue_choice.lower() in ['quit', 'exit']:
                    print("\n🤖 Thank you for using Selda AI! Goodbye! 👋")
                    return
                elif continue_choice.lower() in ['yes', 'y', 'yeah', 'sure', 'ok']:
                    break
                elif continue_choice.lower() in ['no', 'n', 'nope', 'done']:
                    print("\n🤖 Thank you for using Selda AI! Goodbye! 👋")
                    return
                else:
                    print("🤖 Please answer 'yes' or 'no'.")
                    
        except Exception as e:
            print(f"\n🤖 ❌ An error occurred: {str(e)}")
            print("Let's try again with a different goal.")

if __name__ == "__main__":
    print("🚀 Starting Selda AI Interactive Chat...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🤖 Session interrupted. Goodbye! 👋")
    except Exception as e:
        print(f"\n❌ Error: {e}")
