"""
User service layer
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from uuid import uuid4

from app.core.database import SupabaseService, supabase_service
from app.core.exceptions import (
    NotFoundError,
    ConflictError,
    ValidationError,
    AuthorizationError
)
from app.shared.models import UserRole, PaginationParams
from app.modules.users.models import (
    UserProfileUpdate,
    UserProfileResponse,
    UserListItem,
    OrganizationMember,
    OrganizationMembershipUpdate,
    InviteUserRequest,
    UserActivityLog,
    UserStatsResponse,
    UserSearchParams,
    ChangeUserRoleRequest,
    ChangeUserStatusRequest,
    UserPasswordChangeRequest,
    UserDeletionRequest,
    BulkUserAction,
    UserExportRequest,
    UserSettings
)

logger = logging.getLogger(__name__)


class UserService:
    """User service for managing user operations"""

    def __init__(self, db: SupabaseService = None):
        self.db = db or supabase_service
        # Create repository instances for different tables
        from app.core.database import BaseRepository
        self.users_repo = BaseRepository("users", self.db)
        self.organizations_repo = BaseRepository("organizations", self.db)
    
    async def get_user_profile(self, user_id: str) -> UserProfileResponse:
        """Get user profile by ID"""
        try:
            # Get user data
            user_data = await self.users_repo.get_by_id(user_id)
            if not user_data:
                raise NotFoundError("User not found")

            # Parse settings if they exist
            settings = UserSettings()
            if user_data.get('settings'):
                try:
                    settings = UserSettings(**user_data['settings'])
                except Exception as e:
                    logger.warning(f"Failed to parse user settings for {user_id}: {e}")

            user_data['settings'] = settings
            return UserProfileResponse(**user_data)

        except Exception as e:
            logger.error(f"Error getting user profile {user_id}: {e}")
            raise
    
    async def update_user_profile(
        self, 
        user_id: str, 
        update_data: UserProfileUpdate,
        current_user_id: str
    ) -> UserProfileResponse:
        """Update user profile"""
        try:
            # Check if user exists
            existing_user = await self.users_repo.get_by_id(user_id)
            if not existing_user:
                raise NotFoundError("User not found")

            # Check permissions (users can only update their own profile unless admin)
            if user_id != current_user_id:
                current_user = await self.users_repo.get_by_id(current_user_id)
                if current_user.get('role') not in ['admin', 'owner']:
                    raise AuthorizationError("Insufficient permissions to update this user")

            # Prepare update data
            update_dict = {}
            for field, value in update_data.dict(exclude_unset=True).items():
                if value is not None:
                    update_dict[field] = value

            if update_dict:
                update_dict['updated_at'] = datetime.utcnow().isoformat()

                # Update user
                updated_user = await self.users_repo.update(user_id, update_dict)

                # Log activity
                await self._log_user_activity(
                    user_id=current_user_id,
                    action="profile_updated",
                    resource_type="user",
                    resource_id=user_id,
                    details={"updated_fields": list(update_dict.keys())}
                )

                return await self.get_user_profile(user_id)

            return await self.get_user_profile(user_id)
            
        except Exception as e:
            logger.error(f"Error updating user profile {user_id}: {e}")
            raise
    
    async def get_organization_members(
        self, 
        organization_id: str,
        pagination: PaginationParams,
        search_params: Optional[UserSearchParams] = None
    ) -> Dict[str, Any]:
        """Get organization members with pagination and search"""
        try:
            # Build query conditions
            conditions = {"organization_id": organization_id}
            
            if search_params:
                if search_params.role:
                    conditions["role"] = search_params.role.value
                if search_params.is_active is not None:
                    conditions["is_active"] = search_params.is_active
            
            # Get total count
            total = await self.db.count("users", conditions)
            
            # Get users with pagination
            users_data = await self.db.get_many(
                "users",
                conditions=conditions,
                limit=pagination.limit,
                offset=pagination.offset,
                order_by="created_at DESC"
            )
            
            # Convert to response models
            users = [UserListItem(**user) for user in users_data]
            
            return {
                "items": users,
                "total": total,
                "page": pagination.page,
                "limit": pagination.limit,
                "pages": (total + pagination.limit - 1) // pagination.limit if total > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting organization members {organization_id}: {e}")
            raise
    
    async def invite_user_to_organization(
        self,
        organization_id: str,
        invite_data: InviteUserRequest,
        invited_by: str
    ) -> Dict[str, Any]:
        """Invite user to organization"""
        try:
            # Check if user already exists
            existing_user = await self.db.get_by_field("users", "email", invite_data.email)
            
            if existing_user:
                # Check if already in organization
                if existing_user.get('organization_id') == organization_id:
                    raise ConflictError("User is already a member of this organization")
                
                # Add to organization
                await self.db.update("users", existing_user['id'], {
                    "organization_id": organization_id,
                    "role": invite_data.role.value,
                    "updated_at": datetime.utcnow().isoformat()
                })
                
                # Log activity
                await self._log_user_activity(
                    user_id=invited_by,
                    action="user_added_to_organization",
                    resource_type="user",
                    resource_id=existing_user['id'],
                    details={
                        "organization_id": organization_id,
                        "role": invite_data.role.value
                    }
                )
                
                return {
                    "message": "User added to organization successfully",
                    "user_id": existing_user['id']
                }
            else:
                # Create invitation record (this would typically involve email sending)
                invitation_id = str(uuid4())
                expires_at = datetime.utcnow() + timedelta(days=7)
                
                # TODO: Store invitation in database and send email
                # For now, return invitation details
                
                await self._log_user_activity(
                    user_id=invited_by,
                    action="user_invited",
                    resource_type="invitation",
                    resource_id=invitation_id,
                    details={
                        "email": invite_data.email,
                        "organization_id": organization_id,
                        "role": invite_data.role.value
                    }
                )
                
                return {
                    "message": "Invitation sent successfully",
                    "invitation_id": invitation_id,
                    "expires_at": expires_at
                }
                
        except Exception as e:
            logger.error(f"Error inviting user to organization {organization_id}: {e}")
            raise
    
    async def update_user_role(
        self,
        user_id: str,
        role_data: ChangeUserRoleRequest,
        updated_by: str
    ) -> Dict[str, Any]:
        """Update user role"""
        try:
            # Check if user exists
            user = await self.db.get_by_id("users", user_id)
            if not user:
                raise NotFoundError("User not found")
            
            # Update role
            await self.db.update("users", user_id, {
                "role": role_data.role.value,
                "updated_at": datetime.utcnow().isoformat()
            })
            
            # Log activity
            await self._log_user_activity(
                user_id=updated_by,
                action="user_role_changed",
                resource_type="user",
                resource_id=user_id,
                details={
                    "old_role": user.get('role'),
                    "new_role": role_data.role.value
                }
            )
            
            return {
                "message": "User role updated successfully",
                "user_id": user_id,
                "new_role": role_data.role.value
            }
            
        except Exception as e:
            logger.error(f"Error updating user role {user_id}: {e}")
            raise
    
    async def update_user_status(
        self,
        user_id: str,
        status_data: ChangeUserStatusRequest,
        updated_by: str
    ) -> Dict[str, Any]:
        """Update user active status"""
        try:
            # Check if user exists
            user = await self.db.get_by_id("users", user_id)
            if not user:
                raise NotFoundError("User not found")
            
            # Update status
            await self.db.update("users", user_id, {
                "is_active": status_data.is_active,
                "updated_at": datetime.utcnow().isoformat()
            })
            
            # Log activity
            await self._log_user_activity(
                user_id=updated_by,
                action="user_status_changed",
                resource_type="user",
                resource_id=user_id,
                details={
                    "old_status": user.get('is_active'),
                    "new_status": status_data.is_active,
                    "reason": status_data.reason
                }
            )
            
            return {
                "message": f"User {'activated' if status_data.is_active else 'deactivated'} successfully",
                "user_id": user_id,
                "is_active": status_data.is_active
            }
            
        except Exception as e:
            logger.error(f"Error updating user status {user_id}: {e}")
            raise
    
    async def get_user_stats(self, user_id: str) -> UserStatsResponse:
        """Get user statistics"""
        try:
            # TODO: Implement actual statistics gathering from campaigns, leads, etc.
            # For now, return mock data
            stats = UserStatsResponse(
                total_campaigns=0,
                active_campaigns=0,
                total_leads=0,
                emails_sent=0,
                response_rate=0.0,
                last_activity=None
            )
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting user stats {user_id}: {e}")
            raise
    
    async def _log_user_activity(
        self,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log user activity"""
        try:
            activity_data = {
                "id": str(uuid4()),
                "user_id": user_id,
                "action": action,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "details": details or {},
                "ip_address": ip_address,
                "user_agent": user_agent,
                "created_at": datetime.utcnow().isoformat(),
                "organization_id": None,  # Optionally set if available
            }
            # Try to infer organization_id from details if present
            if details and "organization_id" in details:
                activity_data["organization_id"] = details["organization_id"]
            # Store in audit_logs table
            from app.core.database import BaseRepository
            audit_logs_repo = BaseRepository("audit_logs", self.db)
            await audit_logs_repo.create(activity_data, use_admin=True)
            logger.info(f"User activity: {action} by {user_id}")
        except Exception as e:
            logger.warning(f"Failed to log user activity to audit_logs: {e}")
            # Don't raise exception for logging failures


# Create service instance
user_service = UserService()
