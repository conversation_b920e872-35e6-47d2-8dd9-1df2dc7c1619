#!/usr/bin/env python3
"""
Direct test of Apollo.io API calls
"""

import asyncio
import sys
import os
import json

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.modules.leads.apollo_service import ApolloService

async def test_apollo_direct_api():
    """Test Apollo.io API calls directly"""
    print("🧪 Testing Apollo.io Direct API Calls")
    print("=" * 50)
    
    try:
        apollo_service = ApolloService()
        
        # Test 1: People search
        print("\n1️⃣ Testing people search...")
        search_data = {
            "page": 1,
            "per_page": 3,
            "q_organization_locations": "London",
            "organization_industries": "restaurants"
        }
        
        print(f"📤 Search request: {search_data}")
        
        try:
            resp = await apollo_service._request("POST", "/mixed_people/search", data=search_data)
            print(f"📥 Search response keys: {list(resp.keys())}")
            
            people = resp.get("people", [])
            print(f"✅ Search successful: {len(people)} people found")
            
            if people:
                person_ids = [person.get("id") for person in people if person.get("id")]
                print(f"🆔 Person IDs: {person_ids}")
                
                # Test 2: Direct bulk enrichment API call
                print(f"\n2️⃣ Testing bulk enrichment API...")
                
                url_params = "reveal_personal_emails=true&reveal_phone_number=false"
                endpoint = f"/people/bulk_match?{url_params}"
                
                enrich_data = {
                    "details": [{"id": person_id} for person_id in person_ids]
                }
                
                print(f"📤 Enrichment endpoint: {endpoint}")
                print(f"📤 Enrichment request: {enrich_data}")
                
                try:
                    enrich_resp = await apollo_service._request("POST", endpoint, data=enrich_data)
                    print(f"📥 Enrichment response: {json.dumps(enrich_resp, indent=2)}")
                    
                    enriched_people = enrich_resp.get("people", [])
                    matches = enrich_resp.get("matches", [])
                    
                    print(f"✅ Enrichment successful:")
                    print(f"   People: {len(enriched_people)}")
                    print(f"   Matches: {len(matches)}")
                    
                    # Analyze the response structure
                    if matches:
                        print(f"\n📧 Email analysis from matches:")
                        for i, match in enumerate(matches[:3]):
                            email = match.get("email", "N/A")
                            print(f"   Match {i+1}: {email}")
                    
                    if enriched_people:
                        print(f"\n📧 Email analysis from people:")
                        for i, person in enumerate(enriched_people[:3]):
                            email = person.get("email", "N/A")
                            print(f"   Person {i+1}: {email}")
                    
                except Exception as enrich_error:
                    print(f"❌ Enrichment failed: {enrich_error}")
                    import traceback
                    print(f"❌ Enrichment traceback: {traceback.format_exc()}")
            
        except Exception as search_error:
            print(f"❌ Search failed: {search_error}")
            import traceback
            print(f"❌ Search traceback: {traceback.format_exc()}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    await test_apollo_direct_api()

if __name__ == "__main__":
    asyncio.run(main())
