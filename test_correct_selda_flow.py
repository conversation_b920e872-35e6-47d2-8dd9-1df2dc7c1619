#!/usr/bin/env python3
"""
Test Script for Correct Selda AI Sales Autopilot Flow
Tests the chat interface and sequential agent workflow
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional


class SeldaChatTester:
    """Test client for Selda AI Chat Interface and Sequential Workflow"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.conversation_id: Optional[str] = None
        
    def _make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        try:
            response = requests.request(method=method, url=url, json=data, headers=headers, timeout=30)
            print(f"📡 {method} {endpoint} -> {response.status_code}")
            
            if response.status_code >= 400:
                print(f"❌ Error: {response.text}")
                return {"error": response.text, "status_code": response.status_code}
            
            return response.json()
            
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return {"error": str(e)}
    
    def authenticate(self) -> bool:
        """Authenticate and get access token"""
        print("\n🔐 Authenticating...")
        print("=" * 50)
        
        # Register new user
        register_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "TestPass123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Restaurant POS Company"
        }
        
        register_result = self._make_request("POST", "/api/v1/auth/register", register_data)
        if "error" in register_result:
            print("❌ Registration failed")
            return False
        
        # Login
        login_data = {"email": register_data["email"], "password": register_data["password"]}
        login_result = self._make_request("POST", "/api/v1/auth/login", login_data)
        
        if "access_token" in login_result:
            self.access_token = login_result["access_token"]
            print("✅ Authentication successful")
            return True
        else:
            print("❌ Login failed")
            return False
    
    def test_chat_goal_input(self) -> bool:
        """Test natural language goal input through chat interface"""
        print("\n💬 Testing Chat Interface - Goal Input...")
        print("=" * 50)
        
        # Test natural language goal input
        goal_data = {
            "goal_description": "I want to get 10 restaurant clients in London within 3 months",
            "context": "We provide POS systems for restaurants and want to expand our client base"
        }
        
        result = self._make_request("POST", "/api/v1/chat/goal/start", goal_data)
        
        if "conversation_id" in result:
            self.conversation_id = result["conversation_id"]
            print("✅ Goal input successful")
            print(f"   Conversation ID: {self.conversation_id}")
            print(f"   Agent Response: {result.get('agent_response', '')}")
            print(f"   Questions Asked: {len(result.get('questions', []))}")
            print(f"   Is Complete: {result.get('is_complete', False)}")
            print(f"   Confidence Score: {result.get('confidence_score', 0)}")
            
            # Print questions if any
            if result.get('questions'):
                print("\n❓ Follow-up Questions:")
                for i, question in enumerate(result['questions'], 1):
                    print(f"   {i}. {question}")
            
            return True
        else:
            print("❌ Goal input failed")
            print(f"   Error: {result}")
            return False
    
    def test_chat_followup(self) -> bool:
        """Test follow-up responses to clarification questions"""
        print("\n💬 Testing Chat Interface - Follow-up Responses...")
        print("=" * 50)
        
        if not self.conversation_id:
            print("❌ No conversation ID available")
            return False
        
        # Simulate user answering follow-up questions
        followup_data = {
            "conversation_id": self.conversation_id,
            "user_response": "My timeline is 3 months and my budget is $5000. I want to measure success by the number of new clients signed.",
            "answered_questions": {
                "timeline": "3 months",
                "budget": "$5000",
                "success_metrics": "number of new clients signed"
            }
        }
        
        result = self._make_request("POST", "/api/v1/chat/goal/followup", followup_data)
        
        if "conversation_id" in result:
            print("✅ Follow-up response successful")
            print(f"   Agent Response: {result.get('agent_response', '')}")
            print(f"   Additional Questions: {len(result.get('questions', []))}")
            print(f"   Is Complete: {result.get('is_complete', False)}")
            print(f"   Confidence Score: {result.get('confidence_score', 0)}")
            print(f"   Next Action: {result.get('next_action', '')}")
            
            if result.get('parsed_goal'):
                print("\n📋 Parsed Goal:")
                print(json.dumps(result['parsed_goal'], indent=2))
            
            return result.get('is_complete', False)
        else:
            print("❌ Follow-up response failed")
            return False
    
    def test_sequential_agents(self) -> bool:
        """Test sequential agent execution"""
        print("\n🤖 Testing Sequential Agent Execution...")
        print("=" * 50)
        
        # Sample goal data (would come from chat interface in real scenario)
        goal_data = {
            "objective": "Get 10 restaurant clients",
            "target_audience": {
                "industry": "Restaurants",
                "location": "London",
                "company_size": "10-50"
            },
            "timeline": "3 months",
            "budget": "$5000",
            "success_criteria": "10 new clients"
        }
        
        # Test each agent in sequence
        agents_to_test = [
            ("strategic_planning", "Strategic Planning"),
            ("lead_sourcing", "Lead Sourcing"),
            ("lead_distribution", "Lead Distribution"),
            ("content_creation", "Content Creation"),
            ("email_automation", "Email Automation")
        ]
        
        current_input = {"goal": goal_data, "target_audience": goal_data["target_audience"]}
        
        for agent_type, agent_name in agents_to_test:
            print(f"\n🔄 Testing {agent_name} Agent...")
            
            agent_data = {
                "agent_type": agent_type,
                "input_data": current_input
            }
            
            result = self._make_request("POST", "/api/v1/agents/execute", agent_data)
            
            if "error" not in result:
                print(f"✅ {agent_name} Agent executed successfully")
                
                # Print key outputs
                if agent_type == "lead_sourcing" and "leads_sourced" in result:
                    print(f"   Leads Sourced: {result['leads_sourced']}")
                    print(f"   Quality Distribution: {result.get('quality_distribution', {})}")
                elif agent_type == "lead_distribution" and "leads_allocated" in result:
                    print(f"   Leads Allocated: {result['leads_allocated']}")
                    print(f"   Quality Stats: {result.get('quality_stats', {})}")
                elif agent_type == "content_creation" and "content_created" in result:
                    print(f"   Content Created: {result['content_created']}")
                elif agent_type == "email_automation" and "emails_sent" in result:
                    print(f"   Emails Sent: {result['emails_sent']}")
                
                # Use output as input for next agent
                if "next_agent_input" in result:
                    current_input.update(result["next_agent_input"])
                else:
                    current_input.update(result)
                
            else:
                print(f"❌ {agent_name} Agent failed")
                print(f"   Error: {result.get('error', 'Unknown error')}")
                # Continue with next agent even if one fails
        
        return True
    
    def test_complete_workflow(self) -> bool:
        """Test complete automated workflow"""
        print("\n🔄 Testing Complete Automated Workflow...")
        print("=" * 50)
        
        # Create a campaign first
        campaign_data = {
            "name": "Restaurant POS Campaign",
            "description": "Target restaurants in London for POS systems",
            "target_audience": {
                "industry": "Restaurants",
                "location": "London"
            },
            "goals": {
                "meetings_target": 10,
                "contacts_target": 500
            }
        }
        
        campaign_result = self._make_request("POST", "/api/v1/campaigns", campaign_data)
        
        if "id" not in campaign_result:
            print("❌ Failed to create campaign")
            return False
        
        campaign_id = campaign_result["id"]
        print(f"✅ Campaign created: {campaign_id}")
        
        # Start complete workflow
        workflow_data = {
            "campaign_id": campaign_id,
            "target_meetings": 10,
            "target_contacts": 500,
            "target_audience": {
                "industry": "Restaurants",
                "location": "London",
                "company_size": "10-50"
            },
            "campaign_goals": {
                "objective": "Get 10 restaurant clients for POS systems",
                "value_proposition": "Streamline restaurant operations with modern POS",
                "pain_points": ["Manual order processing", "Inventory management", "Payment processing"]
            }
        }
        
        workflow_result = self._make_request("POST", "/api/v1/campaigns/workflow/start", workflow_data)
        
        if "workflow_id" not in workflow_result:
            print("❌ Failed to start workflow")
            return False
        
        workflow_id = workflow_result["workflow_id"]
        print(f"✅ Workflow started: {workflow_id}")
        
        # Monitor workflow progress
        print("\n📊 Monitoring workflow progress...")
        max_wait = 60  # 1 minute for demo
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            status_result = self._make_request("GET", f"/api/v1/campaigns/workflow/{workflow_id}/status")
            
            if "status" in status_result:
                status = status_result["status"]
                progress = status_result.get("progress_percentage", 0)
                stage = status_result.get("current_stage", "unknown")
                
                print(f"   Status: {status} | Progress: {progress:.1f}% | Stage: {stage}")
                
                if status in ["completed", "failed"]:
                    break
            
            time.sleep(5)
        
        print("✅ Workflow monitoring completed")
        return True
    
    def run_all_tests(self) -> bool:
        """Run all tests in the correct Selda AI flow"""
        print("🚀 Starting Selda AI Correct Workflow Tests")
        print("=" * 60)
        
        results = []
        
        # Step 1: Authentication
        results.append(self.authenticate())
        if not self.access_token:
            return False
        
        # Step 2: Chat Interface - Goal Input
        results.append(self.test_chat_goal_input())
        
        # Step 3: Chat Interface - Follow-up
        goal_complete = self.test_chat_followup()
        results.append(goal_complete)
        
        # Step 4: Sequential Agents
        results.append(self.test_sequential_agents())
        
        # Step 5: Complete Workflow
        results.append(self.test_complete_workflow())
        
        # Summary
        print("\n" + "=" * 60)
        if all(results):
            print("🎉 ALL TESTS PASSED!")
            print("\n✨ Selda AI Correct Workflow Summary:")
            print("   ✅ Chat interface for natural language goal input")
            print("   ✅ Follow-up questions for missing information")
            print("   ✅ Sequential agent execution with output chaining")
            print("   ✅ Complete automated workflow")
            print("   ✅ Real-time progress monitoring")
            print("\n🎯 This matches the Selda AI Sales Autopilot proposal!")
            return True
        else:
            print("❌ SOME TESTS FAILED!")
            print(f"   Passed: {sum(results)}/{len(results)} tests")
            return False


def main():
    """Main test execution"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    print(f"Testing Selda AI Correct Workflow at: {base_url}")
    
    tester = SeldaChatTester(base_url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
