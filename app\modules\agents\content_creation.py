import logging
from typing import Dict, Any, List
from app.modules.agents.base import Selda<PERSON>gent, agent_registry
from app.modules.agents.models import (
    AgentType,
    ContentCreationInput,
    ContentCreationOutput
)

logger = logging.getLogger(__name__)

class ContentCreationAgent(SeldaAgent):
    """Agent for dynamic, personalized content/email generation with A/B testing and brand voice enforcement."""

    @property
    def agent_type(self) -> AgentType:
        return AgentType.CONTENT_CREATION

    @property
    def name(self) -> str:
        return "Content Creation Specialist"

    @property
    def description(self) -> str:
        return "Generates personalized, high-converting email and campaign content."

    def _get_agent_goal(self) -> str:
        return "Create dynamic, personalized content for sales campaigns, including A/B tested email variants and brand voice enforcement."

    def _get_agent_backstory(self) -> str:
        return "You are an expert in sales copywriting, email marketing, and brand communication. You use AI to craft compelling, personalized messages that drive engagement."

    def get_capabilities(self) -> List[Dict[str, Any]]:
        return [
            {
                "name": "content_generation",
                "description": "Generate personalized email/campaign content with A/B testing and brand voice.",
                "parameters": {"input": "Campaign context, audience, brand guidelines", "output": "Email content variants"}
            }
        ]

    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate personalized content and A/B variants using LLM."""
        try:
            cc_input = ContentCreationInput(**input_data)
            prompt = self._build_prompt(cc_input)
            response = await self.llm.ainvoke(prompt)
            content, subject, personalization_vars, suggestions, score = self._parse_response(response)
            # Generate A/B variant
            ab_prompt = self._build_prompt(cc_input, ab_variant=True)
            ab_response = await self.llm.ainvoke(ab_prompt)
            ab_content, ab_subject, ab_personalization_vars, ab_suggestions, ab_score = self._parse_response(ab_response)
            return ContentCreationOutput(
                content=content,
                subject_line=subject,
                personalization_variables=personalization_vars,
                content_score=score,
                suggestions=suggestions + ab_suggestions
            ).dict() | {"ab_variant": {
                "content": ab_content,
                "subject_line": ab_subject,
                "personalization_variables": ab_personalization_vars,
                "content_score": ab_score,
                "suggestions": ab_suggestions
            }}
        except Exception as e:
            logger.error(f"Content creation failed: {e}")
            raise

    def _build_prompt(self, cc_input: ContentCreationInput, ab_variant: bool = False) -> str:
        variant = " (A/B variant)" if ab_variant else ""
        brand = f"\nBrand Guidelines: {cc_input.brand_guidelines}" if cc_input.brand_guidelines else ""
        personalization = f"\nPersonalization Data: {cc_input.personalization_data}" if cc_input.personalization_data else ""
        return f"""
        Generate a high-converting, personalized {cc_input.content_type}{variant} for the following campaign:
        Campaign Context: {cc_input.campaign_context}
        Target Audience: {cc_input.target_audience}{brand}{personalization}
        Requirements:
        - Use persuasive, natural language
        - Enforce brand voice and guidelines if provided
        - Include personalization variables
        - Suggest a subject line
        - Suggest improvements and A/B test ideas
        - Score the content for engagement (0-1)
        Return a JSON with: content, subject_line, personalization_variables, suggestions, content_score
        """

    def _parse_response(self, response) -> (str, str, List[str], List[str], float):
        import json
        try:
            data = json.loads(response.content)
            return (
                data.get("content", ""),
                data.get("subject_line", ""),
                data.get("personalization_variables", []),
                data.get("suggestions", []),
                float(data.get("content_score", 0.7))
            )
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            return "", "", [], ["Check content manually"], 0.5

# Register the agent
agent_registry[AgentType.CONTENT_CREATION] = ContentCreationAgent 