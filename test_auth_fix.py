#!/usr/bin/env python3
"""
Test script to verify the auth/me endpoint fix
"""

import requests
from app.core.config import settings

def create_test_jwt():
    """Create a test JWT token with proper Supabase format"""
    # Use the exact same approach as the auth system
    from app.core.supabase_auth import create_supabase_compatible_jwt

    user_data = {
        "id": "test-user-123",
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User"
    }

    token = create_supabase_compatible_jwt(user_data)
    print(f"✅ Token created using auth system")
    print(f"   JWT Secret Key: {settings.jwt_secret_key[:10]}...")
    print(f"   JWT Algorithm: {settings.jwt_algorithm}")

    return token

def test_auth_me_endpoint():
    """Test the /api/v1/auth/me endpoint"""
    base_url = "http://localhost:8000"
    
    # Create a test JWT token
    token = create_test_jwt()
    print(f"Created test JWT token: {token[:50]}...")
    
    # Test the /auth/me endpoint
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/api/v1/auth/me", headers=headers)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Auth endpoint working!")
            print(f"Response body: {response.json()}")
        else:
            print("❌ FAILED: Auth endpoint not working")
            print(f"Response body: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Could not connect to server: {e}")
        return False
    
    return response.status_code == 200

if __name__ == "__main__":
    print("🧪 Testing auth/me endpoint fix...")
    success = test_auth_me_endpoint()
    if success:
        print("🎉 All tests passed!")
    else:
        print("💥 Tests failed!")
