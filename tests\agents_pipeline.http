### 0. (Optional) Register a new user
# POST http://localhost:8000/api/v1/auth/register
# Content-Type: application/json
#
# {
#   "email": "<EMAIL>",
#   "password": "YourPassword123!",
#   "first_name": "<PERSON>",
#   "last_name": "<PERSON><PERSON>",
#   "organization_name": "TestOrg"
# }

### 0.1. <PERSON><PERSON> to get tokens
POST http://localhost:8000/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

> {% set @access_token = response.body.access_token %}
> {% set @refresh_token = response.body.refresh_token %}

### 0.2. Refresh access token (if needed)
# Use the @refresh_token from the login response
POST http://localhost:8000/api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "{{@refresh_token}}"
}

> {% set @refreshed_access_token = response.body.access_token %}

### 1. Goal Understanding Agent
POST http://localhost:8000/api/v1/agents/goal-understanding
Content-Type: application/json
Authorization: Bearer {{@access_token}}

{
  "goal": "Book 10 meetings with London restaurants"
}

### 2. Strategic Planning Agent
# Copy the parsed_goal output from the previous response as input here
POST http://localhost:8000/api/v1/agents/strategic-planning
Content-Type: application/json
Authorization: Bearer {{@access_token}}

{
  "goal": {
    "target": "meetings",
    "quantity": 10,
    "location": "London",
    "industry": "Restaurants"
  }
}

### 3. Content Creation Agent
# Copy the plan and filters output from the previous response as input here
POST http://localhost:8000/api/v1/agents/content-creation
Content-Type: application/json
Authorization: Bearer {{@access_token}}

{
  "plan": "Source 1000 restaurant leads in London from Apollo.io",
  "filters": {
    "location": "London",
    "industry": "Restaurants"
  }
}

### 4. Lead Sourcing Agent (Apollo)
# Use the filters from the planning agent output
POST http://localhost:8000/api/v1/leads/source/apollo
Content-Type: application/json
Authorization: Bearer {{@access_token}}
Apollo-Api-Key: <YOUR_APOLLO_API_KEY>

{
  "location": "London",
  "industry": "Restaurants"
}

# If Apollo API key is returned from a response, you can automate extraction like:
# > {% set @apollo_api_key = response.body.apollo_api_key %}
# And use Apollo-Api-Key: {{@apollo_api_key}} 