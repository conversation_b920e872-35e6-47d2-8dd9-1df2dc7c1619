#!/usr/bin/env python3
"""
Simple Chat Interface for Selda AI Sales Autopilot
Provides a command-line interface to interact with the goal understanding agent
and start the sequential workflow.
"""

import sys
import asyncio

# Add the app directory to the path
sys.path.append('.')

from app.modules.agents.simple_workflow import simple_workflow_manager


class SimpleChatInterface:
    """Simple chat interface for Selda AI"""
    
    def __init__(self):
        # Use fixed demo organization and user IDs that exist in the database
        self.organization_id = "00000000-0000-4000-8000-000000000001"  # Demo organization
        self.user_id = "00000000-0000-4000-8000-000000000002"  # Demo user
        self.conversation_history = []

        print(f"🔧 Using Demo Organization: Demo Organization - Simple Chat")
        print(f"🔧 Using Demo User: <EMAIL>")
        
    async def start_chat(self):
        """Start the chat interface"""
        print("🤖 Welcome to Selda AI Sales Autopilot!")
        print("=" * 50)
        print("I'll help you create and execute your sales campaign.")
        print("Just tell me your sales goal in natural language.")
        print("Type 'quit' to exit.\n")
        
        while True:
            try:
                # Get user input
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("\n👋 Goodbye! Thanks for using Selda AI!")
                    break
                
                if not user_input:
                    print("Please enter your sales goal.")
                    continue
                
                # Process the goal
                await self.process_goal(user_input)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using Selda AI!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                print("Please try again.\n")
    
    async def process_goal(self, goal: str):
        """Process the user's goal through the simplified workflow"""
        print(f"\n🤖 Processing your goal: '{goal}'")
        print("=" * 50)

        try:
            # Set organization ID for the workflow manager
            simple_workflow_manager.organization_id = self.organization_id

            # Start the complete workflow from just the goal
            print("🚀 Starting complete workflow...")
            print("This will run all agents sequentially: goal understanding → planning → sourcing → content → email → analysis → booking → monitoring")

            result = await simple_workflow_manager.start_from_goal(goal, self.user_id)

            if result["success"]:
                # Display results
                goal_result = result.get("goal_understanding", {})
                parsed_goal = goal_result.get('parsed_goal', {})

                print(f"✅ Goal understood with {goal_result.get('confidence_score', 0):.1%} confidence")

                # Display parsed goal
                print(f"\n📊 Parsed Goal:")
                print(f"   Objective: {parsed_goal.get('objective', 'Not specified')}")
                if parsed_goal.get('target_audience'):
                    ta = parsed_goal['target_audience']
                    print(f"   Target Industry: {ta.get('industry', 'Not specified')}")
                    print(f"   Geography: {ta.get('geography', 'Not specified')}")

                # Display workflow info
                workflow_info = result.get("workflow", {})
                workflow_id = workflow_info.get("workflow_id")
                campaign_id = workflow_info.get("campaign_id")

                print(f"\n🎯 Strategic plan created")

                lead_result = result.get("lead_sourcing", {})
                leads_count = lead_result.get('leads_sourced', 0)
                print(f"🔍 Found {leads_count} potential leads from Apollo.io")

                print(f"\n✅ Complete workflow started!")
                print(f"📊 Workflow ID: {workflow_id}")
                print(f"🎯 Campaign ID: {campaign_id}")
                print(f"📈 You can monitor progress and results in the dashboard.")

                # Monitor workflow progress
                if workflow_id:
                    await self.monitor_workflow_progress(workflow_id)

            else:
                print(f"❌ Failed to start workflow: {result.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"❌ Error processing goal: {e}")
            raise
    

    
    async def monitor_workflow_progress(self, workflow_id: str):
        """Monitor workflow progress"""
        print(f"\n📊 Monitoring workflow progress...")

        try:
            # Check workflow status using simple workflow manager
            status = await simple_workflow_manager.get_workflow_status(workflow_id)

            if status:
                print(f"Status: {status.get('status', 'unknown')}")
                print(f"Current Stage: {status.get('current_stage', 'unknown')}")
                print(f"Completed Stages: {len(status.get('stages_completed', []))}")

                if status.get('status') == "completed":
                    print("🎉 Workflow completed successfully!")

                    # Get stage results
                    results = await simple_workflow_manager.get_workflow_results(workflow_id)
                    stage_results = results.get('stage_results', [])
                    print(f"📋 Total stages executed: {len(stage_results)}")

                elif status.get('status') == "failed":
                    print("❌ Workflow failed. Check logs for details.")
                else:
                    print("⏳ Workflow is still running...")
            else:
                print("❓ Could not retrieve workflow status")

        except Exception as e:
            print(f"❌ Error monitoring workflow: {e}")


async def main():
    """Main function"""
    chat = SimpleChatInterface()
    await chat.start_chat()


if __name__ == "__main__":
    # Run the chat interface
    asyncio.run(main())
