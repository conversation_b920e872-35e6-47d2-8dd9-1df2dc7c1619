# PowerShell script to test auth endpoints
$baseUrl = "http://localhost:8000"

Write-Host "🧪 Testing Selda AI Authentication Endpoints" -ForegroundColor Green
Write-Host ""

# Test 1: Login
Write-Host "1. Testing Login Endpoint..." -ForegroundColor Yellow
try {
    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/api/v1/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"email": "<EMAIL>", "password": "Itblast@123"}'
    
    if ($loginResponse.StatusCode -eq 200) {
        Write-Host "   ✅ Login successful!" -ForegroundColor Green
        
        # Parse the JSON response
        $loginData = $loginResponse.Content | ConvertFrom-Json
        $accessToken = $loginData.access_token
        
        Write-Host "   📝 Access token received: $($accessToken.Substring(0, 50))..." -ForegroundColor Cyan
        
        # Test 2: Auth Me Endpoint
        Write-Host ""
        Write-Host "2. Testing /auth/me Endpoint..." -ForegroundColor Yellow
        
        try {
            $authMeResponse = Invoke-WebRequest -Uri "$baseUrl/api/v1/auth/me" -Method GET -Headers @{"Authorization"="Bearer $accessToken"}
            
            if ($authMeResponse.StatusCode -eq 200) {
                Write-Host "   ✅ /auth/me endpoint working!" -ForegroundColor Green
                Write-Host "   📄 Response: $($authMeResponse.Content)" -ForegroundColor Cyan
            } else {
                Write-Host "   ❌ /auth/me failed with status: $($authMeResponse.StatusCode)" -ForegroundColor Red
                Write-Host "   📄 Response: $($authMeResponse.Content)" -ForegroundColor Red
            }
        } catch {
            Write-Host "   ❌ /auth/me endpoint failed!" -ForegroundColor Red
            Write-Host "   📄 Error: $($_.Exception.Message)" -ForegroundColor Red
            
            # Try to get more details from the response
            if ($_.Exception.Response) {
                $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                $responseBody = $reader.ReadToEnd()
                Write-Host "   📄 Response Body: $responseBody" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "   ❌ Login failed with status: $($loginResponse.StatusCode)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "   ❌ Login endpoint failed!" -ForegroundColor Red
    Write-Host "   📄 Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🏁 Test completed!" -ForegroundColor Green
