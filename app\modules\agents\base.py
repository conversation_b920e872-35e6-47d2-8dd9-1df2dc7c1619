"""
Base agent classes and interfaces
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Type
from datetime import datetime
from uuid import uuid4

from crewai import Agent, Task, Crew
from crewai.tools import BaseTool
from langchain_openai import ChatOpenAI

from app.core.config import settings
from app.core.database import SupabaseService, supabase_service
from app.modules.agents.models import (
    AgentType,
    AgentStatus,
    TaskStatus,
    AgentTask,
    AgentExecution,
    AgentConfiguration
)

logger = logging.getLogger(__name__)


class BaseAgentInterface(ABC):
    """Base interface for all agents"""
    
    @property
    @abstractmethod
    def agent_type(self) -> AgentType:
        """Return the agent type"""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Return the agent name"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Return the agent description"""
        pass
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent with given input data"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[Dict[str, Any]]:
        """Return list of agent capabilities"""
        pass


class SeldaAgent(BaseAgentInterface):
    """Base Selda AI agent implementation"""
    
    def __init__(self, organization_id: str, db: SupabaseService = None):
        self.organization_id = organization_id
        self.db = db or supabase_service
        self.status = AgentStatus.IDLE
        self.current_task_id: Optional[str] = None
        
        # Initialize OpenAI LLM
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            temperature=0.7,
            max_tokens=settings.openai_max_tokens,
            openai_api_key=settings.openai_api_key
        )
        
        # Initialize CrewAI agent
        self._crew_agent: Optional[Agent] = None
        self._initialize_crew_agent()
    
    def _initialize_crew_agent(self):
        """Initialize the CrewAI agent"""
        try:
            self._crew_agent = Agent(
                role=self.name,
                goal=self._get_agent_goal(),
                backstory=self._get_agent_backstory(),
                verbose=True,
                allow_delegation=False,
                llm=self.llm,
                tools=self._get_tools()
            )
            logger.info(f"Initialized CrewAI agent for {self.agent_type}")
        except Exception as e:
            logger.error(f"Failed to initialize CrewAI agent for {self.agent_type}: {e}")
            raise
    
    @abstractmethod
    def _get_agent_goal(self) -> str:
        """Return the agent's goal for CrewAI"""
        pass
    
    @abstractmethod
    def _get_agent_backstory(self) -> str:
        """Return the agent's backstory for CrewAI"""
        pass
    
    def _get_tools(self) -> List[BaseTool]:
        """Return list of tools for the agent"""
        return []
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent with given input data"""
        task_id = str(uuid4())
        self.current_task_id = task_id

        try:
            # Update status
            self.status = AgentStatus.RUNNING

            # Create task record
            task_data = {
                'id': task_id,
                'organization_id': self.organization_id,
                'agent_type': self.agent_type.value,
                'task_type': 'execute',
                'status': TaskStatus.IN_PROGRESS.value,
                'input_data': input_data,
                'started_at': datetime.utcnow().isoformat(),
                'created_at': datetime.utcnow().isoformat()
            }

            # Store task in database
            from app.core.database import BaseRepository
            tasks_repo = BaseRepository("agent_tasks", self.db)
            await tasks_repo.create(task_data, use_admin=True)

            # Execute the agent logic
            result = await self._execute_logic(input_data)

            # Update task with results
            await tasks_repo.update(task_id, {
                'status': TaskStatus.COMPLETED.value,
                'output_data': result,
                'completed_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }, use_admin=True)

            self.status = AgentStatus.COMPLETED
            logger.info(f"Agent {self.agent_type} completed task {task_id}")

            return result

        except Exception as e:
            logger.error(f"Agent {self.agent_type} failed task {task_id}: {e}")

            # Update task with error
            try:
                from app.core.database import BaseRepository
                tasks_repo = BaseRepository("agent_tasks", self.db)
                await tasks_repo.update(task_id, {
                    'status': TaskStatus.FAILED.value,
                    'error_message': str(e),
                    'completed_at': datetime.utcnow().isoformat(),
                    'updated_at': datetime.utcnow().isoformat()
                }, use_admin=True)
            except Exception as update_error:
                logger.error(f"Failed to update task status: {update_error}")

            self.status = AgentStatus.ERROR
            raise
        finally:
            self.current_task_id = None
    
    @abstractmethod
    async def _execute_logic(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the specific agent logic"""
        pass
    
    async def get_configuration(self) -> Optional[AgentConfiguration]:
        """Get agent configuration from database"""
        try:
            from app.core.database import BaseRepository
            config_repo = BaseRepository("agent_configurations", self.db)
            
            # Try to find existing configuration
            configs = await config_repo.list(
                filters={
                    'organization_id': self.organization_id,
                    'agent_type': self.agent_type.value
                },
                limit=1,
                use_admin=True
            )
            
            if configs:
                return AgentConfiguration(**configs[0])
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get agent configuration: {e}")
            return None
    
    async def update_configuration(self, settings: Dict[str, Any]) -> AgentConfiguration:
        """Update agent configuration"""
        try:
            from app.core.database import BaseRepository
            config_repo = BaseRepository("agent_configurations", self.db)
            
            # Check if configuration exists
            existing_config = await self.get_configuration()
            
            if existing_config:
                # Update existing configuration
                updated_config = await config_repo.update(
                    existing_config.id,
                    {
                        'settings': settings,
                        'updated_at': datetime.utcnow().isoformat()
                    },
                    use_admin=True
                )
                return AgentConfiguration(**updated_config)
            else:
                # Create new configuration
                config_data = {
                    'id': str(uuid4()),
                    'organization_id': self.organization_id,
                    'agent_type': self.agent_type.value,
                    'name': self.name,
                    'description': self.description,
                    'settings': settings,
                    'created_at': datetime.utcnow().isoformat()
                }
                
                created_config = await config_repo.create(config_data, use_admin=True)
                return AgentConfiguration(**created_config)
                
        except Exception as e:
            logger.error(f"Failed to update agent configuration: {e}")
            raise


class CrewManager:
    """Manager for CrewAI crews and multi-agent workflows"""
    
    def __init__(self, organization_id: str, db: SupabaseService = None):
        self.organization_id = organization_id
        self.db = db or supabase_service
        self.agents: Dict[AgentType, SeldaAgent] = {}
    
    def register_agent(self, agent: SeldaAgent):
        """Register an agent with the crew manager"""
        self.agents[agent.agent_type] = agent
        logger.info(f"Registered agent {agent.agent_type} with crew manager")
    
    async def execute_crew_task(
        self,
        agent_types: List[AgentType],
        task_description: str,
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a multi-agent crew task"""
        try:
            # Get agents for the crew
            crew_agents = []
            for agent_type in agent_types:
                if agent_type not in self.agents:
                    raise ValueError(f"Agent {agent_type} not registered")
                crew_agents.append(self.agents[agent_type]._crew_agent)
            
            # Create CrewAI task
            crew_task = Task(
                description=task_description,
                agent=crew_agents[0],  # Primary agent
                expected_output="Detailed analysis and recommendations"
            )
            
            # Create and execute crew
            crew = Crew(
                agents=crew_agents,
                tasks=[crew_task],
                verbose=True
            )
            
            result = crew.kickoff()
            
            logger.info(f"Crew task completed successfully")
            return {
                'result': str(result),
                'agents_used': [agent_type.value for agent_type in agent_types],
                'task_description': task_description
            }
            
        except Exception as e:
            logger.error(f"Crew task execution failed: {e}")
            raise


class AgentRegistry:
    """Registry for managing agent classes"""

    def __init__(self):
        self._registry: Dict[AgentType, Type[SeldaAgent]] = {}

    def register(self, agent_type: AgentType):
        """Decorator to register an agent class"""
        def decorator(agent_class: Type[SeldaAgent]):
            self._registry[agent_type] = agent_class
            logger.info(f"Registered agent {agent_type} -> {agent_class.__name__}")
            return agent_class
        return decorator

    def get(self, agent_type: AgentType) -> Optional[Type[SeldaAgent]]:
        """Get agent class by type"""
        return self._registry.get(agent_type)

    def __getitem__(self, agent_type: AgentType) -> Type[SeldaAgent]:
        """Get agent class by type (dict-like access)"""
        return self._registry[agent_type]

    def __setitem__(self, agent_type: AgentType, agent_class: Type[SeldaAgent]):
        """Set agent class by type (dict-like access)"""
        self._registry[agent_type] = agent_class


# Global agent registry
agent_registry = AgentRegistry()
