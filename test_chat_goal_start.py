#!/usr/bin/env python3
"""
Test the chat/goal/start endpoint
"""

import requests
import json

def test_chat_goal_start():
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Chat Goal Start Endpoint...")
    
    # Step 1: Login to get token
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "Itblast@123"
        }
        login_response = requests.post(
            f"{base_url}/api/v1/auth/login", 
            json=login_data,
            timeout=10
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print(f"✅ Login successful: {access_token[:50]}...")
            
            # Step 2: Test chat/goal/start endpoint
            try:
                headers = {"Authorization": f"Bearer {access_token}"}
                goal_data = {
                    "goal_description": "I want to generate 10 qualified leads for my SaaS product targeting small businesses",
                    "context": "We provide AI-powered sales automation tools for small businesses"
                }
                
                goal_response = requests.post(
                    f"{base_url}/api/v1/chat/goal/start",
                    headers=headers,
                    json=goal_data,
                    timeout=30
                )
                
                print(f"🔍 Chat Goal Start: {goal_response.status_code}")
                
                if goal_response.status_code == 200:
                    print("✅ SUCCESS: Chat goal start endpoint is working!")
                    response_data = goal_response.json()
                    print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                else:
                    print("❌ FAILED: Chat goal start endpoint not working")
                    print(f"📄 Status: {goal_response.status_code}")
                    print(f"📄 Response: {goal_response.text}")
                    
            except Exception as e:
                print(f"❌ Chat goal start request failed: {e}")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"📄 Response: {login_response.text}")
            
    except Exception as e:
        print(f"❌ Login request failed: {e}")

if __name__ == "__main__":
    test_chat_goal_start()
