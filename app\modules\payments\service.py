"""
Payment service layer
"""

import logging
import stripe
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from uuid import uuid4

from app.core.config import settings
from app.core.database import SupabaseService, supabase_service
from app.core.exceptions import (
    NotFoundError,
    ConflictError,
    ValidationError,
    ExternalServiceError
)
from app.modules.payments.models import (
    SubscriptionPlan,
    CreateCheckoutSessionRequest,
    CheckoutSessionResponse,
    SubscriptionResponse,
    PaymentMethodResponse,
    InvoiceResponse,
    PaymentResponse,
    BillingPortalSessionRequest,
    BillingPortalSessionResponse,
    CancelSubscriptionRequest,
    UpdateSubscriptionRequest,
    WebhookEvent,
    SubscriptionUsageResponse,
    BillingHistoryResponse,
    SubscriptionPlansResponse,
    PaymentMethodsResponse,
    CreateCustomerRequest,
    CustomerResponse,
    PaymentStatus,
    SubscriptionStatus,
    InvoiceStatus
)

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.stripe_secret_key


class PaymentService:
    """Payment service for managing Stripe operations"""

    def __init__(self, db: SupabaseService = None):
        self.db = db or supabase_service
        self.stripe = stripe
        # Create repository instances
        from app.core.database import BaseRepository
        self.subscriptions_repo = BaseRepository("subscriptions", self.db)
        self.customers_repo = BaseRepository("customers", self.db)
        self.organizations_repo = BaseRepository("organizations", self.db)
        self.users_repo = BaseRepository("users", self.db)
    
    async def get_subscription_plans(self) -> List[SubscriptionPlan]:
        """Get all available subscription plans"""
        try:
            # Define subscription plans (these would typically be stored in database)
            plans = [
                SubscriptionPlan(
                    id="free",
                    name="Free",
                    description="Get started with basic features",
                    tier="free",
                    price=0.0,
                    interval="month",
                    features=[
                        "Up to 100 leads",
                        "Basic email templates",
                        "Manual campaign management",
                        "Email support"
                    ],
                    limits={
                        "leads": 100,
                        "campaigns": 1,
                        "emails_per_month": 500
                    },
                    trial_period_days=None
                ),
                SubscriptionPlan(
                    id="basic",
                    name="Basic",
                    description="Perfect for small teams",
                    tier="basic",
                    price=29.0,
                    interval="month",
                    features=[
                        "Up to 1,000 leads",
                        "AI-powered email generation",
                        "Automated campaigns",
                        "Basic analytics",
                        "Priority support"
                    ],
                    limits={
                        "leads": 1000,
                        "campaigns": 5,
                        "emails_per_month": 5000
                    },
                    stripe_price_id=settings.stripe_basic_price_id,
                    trial_period_days=14
                ),
                SubscriptionPlan(
                    id="pro",
                    name="Pro",
                    description="Advanced features for growing businesses",
                    tier="pro",
                    price=99.0,
                    interval="month",
                    features=[
                        "Up to 10,000 leads",
                        "Advanced AI agents",
                        "Multi-channel outreach",
                        "Advanced analytics",
                        "API access",
                        "Phone support"
                    ],
                    limits={
                        "leads": 10000,
                        "campaigns": 25,
                        "emails_per_month": 25000
                    },
                    stripe_price_id=settings.stripe_pro_price_id,
                    trial_period_days=14
                ),
                SubscriptionPlan(
                    id="enterprise",
                    name="Enterprise",
                    description="Custom solutions for large organizations",
                    tier="enterprise",
                    price=299.0,
                    interval="month",
                    features=[
                        "Unlimited leads",
                        "Custom AI training",
                        "White-label solution",
                        "Advanced integrations",
                        "Dedicated support",
                        "Custom SLA"
                    ],
                    limits={
                        "leads": -1,  # Unlimited
                        "campaigns": -1,
                        "emails_per_month": -1
                    },
                    stripe_price_id=settings.stripe_enterprise_price_id,
                    trial_period_days=30
                )
            ]
            
            return plans
            
        except Exception as e:
            logger.error(f"Error getting subscription plans: {e}")
            raise ExternalServiceError("Failed to get subscription plans")
    
    async def create_checkout_session(
        self,
        organization_id: str,
        request: CreateCheckoutSessionRequest
    ) -> CheckoutSessionResponse:
        """Create Stripe checkout session"""
        try:
            # Get or create customer
            customer = await self._get_or_create_customer(organization_id)
            
            # Create checkout session
            session = self.stripe.checkout.Session.create(
                customer=customer['stripe_customer_id'],
                payment_method_types=['card'],
                line_items=[{
                    'price': request.price_id,
                    'quantity': 1,
                }],
                mode='subscription',
                success_url=request.success_url,
                cancel_url=request.cancel_url,
                metadata={
                    'organization_id': organization_id,
                    **request.metadata
                },
                subscription_data={
                    'trial_period_days': request.trial_period_days,
                    'metadata': {
                        'organization_id': organization_id
                    }
                } if request.trial_period_days else {
                    'metadata': {
                        'organization_id': organization_id
                    }
                },
                discounts=[{
                    'coupon': request.coupon_code
                }] if request.coupon_code else None
            )
            
            return CheckoutSessionResponse(
                success=True,
                message="Checkout session created successfully",
                session_id=session.id,
                session_url=session.url,
                expires_at=datetime.fromtimestamp(session.expires_at)
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating checkout session: {e}")
            raise ExternalServiceError(f"Payment processing error: {str(e)}")
        except Exception as e:
            logger.error(f"Error creating checkout session: {e}")
            raise
    
    async def create_billing_portal_session(
        self,
        organization_id: str,
        request: BillingPortalSessionRequest
    ) -> BillingPortalSessionResponse:
        """Create Stripe billing portal session"""
        try:
            # Get customer
            customer = await self._get_customer(organization_id)
            if not customer:
                raise NotFoundError("Customer not found")
            
            # Create billing portal session
            session = self.stripe.billing_portal.Session.create(
                customer=customer['stripe_customer_id'],
                return_url=request.return_url
            )
            
            return BillingPortalSessionResponse(
                success=True,
                message="Billing portal session created successfully",
                session_url=session.url
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating billing portal session: {e}")
            raise ExternalServiceError(f"Payment processing error: {str(e)}")
        except Exception as e:
            logger.error(f"Error creating billing portal session: {e}")
            raise
    
    async def get_organization_subscription(
        self,
        organization_id: str
    ) -> Optional[SubscriptionResponse]:
        """Get organization's current subscription"""
        try:
            # Get subscription from database
            subscription_data = await self.db.get_by_field(
                "subscriptions", 
                "organization_id", 
                organization_id
            )
            
            if not subscription_data:
                return None
            
            # Get plan details
            plans = await self.get_subscription_plans()
            plan = next((p for p in plans if p.stripe_price_id == subscription_data.get('stripe_price_id')), None)
            
            if not plan:
                logger.warning(f"Plan not found for subscription {subscription_data['id']}")
                return None
            
            return SubscriptionResponse(
                id=subscription_data['id'],
                organization_id=subscription_data['organization_id'],
                stripe_subscription_id=subscription_data['stripe_subscription_id'],
                stripe_customer_id=subscription_data['stripe_customer_id'],
                plan=plan,
                status=SubscriptionStatus(subscription_data['status']),
                current_period_start=subscription_data['current_period_start'],
                current_period_end=subscription_data['current_period_end'],
                trial_start=subscription_data.get('trial_start'),
                trial_end=subscription_data.get('trial_end'),
                canceled_at=subscription_data.get('canceled_at'),
                ended_at=subscription_data.get('ended_at'),
                created_at=subscription_data['created_at'],
                updated_at=subscription_data.get('updated_at')
            )
            
        except Exception as e:
            logger.error(f"Error getting organization subscription: {e}")
            raise
    
    async def _get_or_create_customer(self, organization_id: str) -> Dict[str, Any]:
        """Get or create Stripe customer for organization"""
        try:
            # Check if customer exists
            customer = await self.db.get_by_field("customers", "organization_id", organization_id)
            
            if customer:
                return customer
            
            # Get organization details
            organization = await self.db.get_by_id("organizations", organization_id)
            if not organization:
                raise NotFoundError("Organization not found")
            
            # Get organization owner
            owner = await self.db.get_by_id("users", organization['owner_id'])
            if not owner:
                raise NotFoundError("Organization owner not found")
            
            # Create Stripe customer
            stripe_customer = self.stripe.Customer.create(
                email=owner['email'],
                name=organization['name'],
                metadata={
                    'organization_id': organization_id,
                    'owner_id': organization['owner_id']
                }
            )
            
            # Store customer in database
            customer_data = {
                'id': str(uuid4()),
                'organization_id': organization_id,
                'stripe_customer_id': stripe_customer.id,
                'email': owner['email'],
                'name': organization['name'],
                'created_at': datetime.utcnow().isoformat()
            }
            
            await self.db.create("customers", customer_data)
            
            return customer_data
            
        except Exception as e:
            logger.error(f"Error getting or creating customer: {e}")
            raise
    
    async def _get_customer(self, organization_id: str) -> Optional[Dict[str, Any]]:
        """Get customer for organization"""
        try:
            return await self.db.get_by_field("customers", "organization_id", organization_id)
        except Exception as e:
            logger.error(f"Error getting customer: {e}")
            raise

    async def handle_checkout_completed(self, session_data: dict):
        """Handle successful checkout completion: create subscription, update org, log, send email."""
        try:
            organization_id = session_data['metadata'].get('organization_id')
            subscription_id = session_data.get('subscription')
            customer_id = session_data.get('customer')
            # Fetch subscription from Stripe
            subscription = self.stripe.Subscription.retrieve(subscription_id)
            # Create or update subscription record in DB
            sub_data = {
                'id': str(uuid4()),
                'organization_id': organization_id,
                'stripe_subscription_id': subscription_id,
                'stripe_customer_id': customer_id,
                'stripe_price_id': subscription['items']['data'][0]['price']['id'],
                'status': subscription['status'],
                'current_period_start': datetime.fromtimestamp(subscription['current_period_start']).isoformat(),
                'current_period_end': datetime.fromtimestamp(subscription['current_period_end']).isoformat(),
                'trial_start': datetime.fromtimestamp(subscription['trial_start']).isoformat() if subscription.get('trial_start') else None,
                'trial_end': datetime.fromtimestamp(subscription['trial_end']).isoformat() if subscription.get('trial_end') else None,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat(),
            }
            await self.db.create('subscriptions', sub_data)
            # Update organization subscription tier (optional: fetch plan info)
            await self.db.update('organizations', organization_id, {
                'subscription_tier': subscription['items']['data'][0]['price']['nickname'] or 'basic',
                'updated_at': datetime.utcnow().isoformat()
            })
            # Log the event (optional: use audit log)
            from app.core.database import BaseRepository
            audit_logs_repo = BaseRepository('audit_logs', self.db)
            await audit_logs_repo.create({
                'id': str(uuid4()),
                'organization_id': organization_id,
                'user_id': None,
                'action': 'checkout_completed',
                'resource_type': 'subscription',
                'resource_id': sub_data['id'],
                'details': {'stripe_session_id': session_data['id']},
                'created_at': datetime.utcnow().isoformat()
            }, use_admin=True)
            # Send confirmation email (placeholder)
            # TODO: Integrate with email service
        except Exception as e:
            logger.error(f"Error in handle_checkout_completed: {e}")
            raise

    async def handle_payment_succeeded(self, invoice_data: dict):
        """Handle successful payment: update payment record, send receipt, update subscription status."""
        try:
            # Update payment record in DB (if exists, else create)
            payment_intent_id = invoice_data.get('payment_intent')
            organization_id = invoice_data['metadata'].get('organization_id') if 'metadata' in invoice_data else None
            payment_data = {
                'id': str(uuid4()),
                'organization_id': organization_id,
                'stripe_payment_intent_id': payment_intent_id,
                'amount': invoice_data['amount_paid'] / 100.0,
                'currency': invoice_data['currency'],
                'status': 'succeeded',
                'invoice_id': invoice_data['id'],
                'created_at': datetime.utcnow().isoformat()
            }
            await self.db.create('payments', payment_data)
            # Update subscription status if needed
            if 'subscription' in invoice_data:
                await self.db.update('subscriptions', invoice_data['subscription'], {
                    'status': 'active',
                    'updated_at': datetime.utcnow().isoformat()
                })
            # Log the event
            from app.core.database import BaseRepository
            audit_logs_repo = BaseRepository('audit_logs', self.db)
            await audit_logs_repo.create({
                'id': str(uuid4()),
                'organization_id': organization_id,
                'user_id': None,
                'action': 'payment_succeeded',
                'resource_type': 'payment',
                'resource_id': payment_data['id'],
                'details': {'stripe_invoice_id': invoice_data['id']},
                'created_at': datetime.utcnow().isoformat()
            }, use_admin=True)
            # Send receipt email (placeholder)
            # TODO: Integrate with email service
        except Exception as e:
            logger.error(f"Error in handle_payment_succeeded: {e}")
            raise

    async def handle_subscription_updated(self, subscription_data: dict):
        """Handle subscription update: update DB, update org limits, notify user."""
        try:
            organization_id = subscription_data['metadata'].get('organization_id') if 'metadata' in subscription_data else None
            # Update subscription record in DB
            await self.db.update('subscriptions', subscription_data['id'], {
                'status': subscription_data['status'],
                'current_period_start': datetime.fromtimestamp(subscription_data['current_period_start']).isoformat(),
                'current_period_end': datetime.fromtimestamp(subscription_data['current_period_end']).isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            })
            # Optionally update organization limits (not implemented)
            # Log the event
            from app.core.database import BaseRepository
            audit_logs_repo = BaseRepository('audit_logs', self.db)
            await audit_logs_repo.create({
                'id': str(uuid4()),
                'organization_id': organization_id,
                'user_id': None,
                'action': 'subscription_updated',
                'resource_type': 'subscription',
                'resource_id': subscription_data['id'],
                'details': {},
                'created_at': datetime.utcnow().isoformat()
            }, use_admin=True)
            # Notify user (placeholder)
            # TODO: Integrate with email service
        except Exception as e:
            logger.error(f"Error in handle_subscription_updated: {e}")
            raise

    async def handle_subscription_deleted(self, subscription_data: dict):
        """Handle subscription cancellation: update status, downgrade org, send cancellation."""
        try:
            organization_id = subscription_data['metadata'].get('organization_id') if 'metadata' in subscription_data else None
            # Update subscription status
            await self.db.update('subscriptions', subscription_data['id'], {
                'status': 'canceled',
                'ended_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            })
            # Downgrade organization to free tier
            if organization_id:
                await self.db.update('organizations', organization_id, {
                    'subscription_tier': 'free',
                    'updated_at': datetime.utcnow().isoformat()
                })
            # Log the event
            from app.core.database import BaseRepository
            audit_logs_repo = BaseRepository('audit_logs', self.db)
            await audit_logs_repo.create({
                'id': str(uuid4()),
                'organization_id': organization_id,
                'user_id': None,
                'action': 'subscription_deleted',
                'resource_type': 'subscription',
                'resource_id': subscription_data['id'],
                'details': {},
                'created_at': datetime.utcnow().isoformat()
            }, use_admin=True)
            # Send cancellation confirmation (placeholder)
            # TODO: Integrate with email service
        except Exception as e:
            logger.error(f"Error in handle_subscription_deleted: {e}")
            raise

    async def handle_payment_failed(self, invoice_data: dict):
        """Handle failed payment: update payment record, send notification, handle dunning."""
        try:
            payment_intent_id = invoice_data.get('payment_intent')
            organization_id = invoice_data['metadata'].get('organization_id') if 'metadata' in invoice_data else None
            payment_data = {
                'id': str(uuid4()),
                'organization_id': organization_id,
                'stripe_payment_intent_id': payment_intent_id,
                'amount': invoice_data['amount_due'] / 100.0,
                'currency': invoice_data['currency'],
                'status': 'failed',
                'invoice_id': invoice_data['id'],
                'failure_reason': invoice_data.get('failure_reason'),
                'created_at': datetime.utcnow().isoformat()
            }
            await self.db.create('payments', payment_data)
            # Log the event
            from app.core.database import BaseRepository
            audit_logs_repo = BaseRepository('audit_logs', self.db)
            await audit_logs_repo.create({
                'id': str(uuid4()),
                'organization_id': organization_id,
                'user_id': None,
                'action': 'payment_failed',
                'resource_type': 'payment',
                'resource_id': payment_data['id'],
                'details': {'stripe_invoice_id': invoice_data['id']},
                'created_at': datetime.utcnow().isoformat()
            }, use_admin=True)
            # Send payment failure notification (placeholder)
            # TODO: Integrate with email service
        except Exception as e:
            logger.error(f"Error in handle_payment_failed: {e}")
            raise


# Create service instance
payment_service = PaymentService()
