-- Agent-related tables for Selda AI Sales Autopilot
-- Migration 003: Agents Tables

-- Agent configurations table
CREATE TABLE IF NOT EXISTS agent_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    agent_type VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(organization_id, agent_type)
);

-- Agent tasks table
CREATE TABLE IF NOT EXISTS agent_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    agent_type VARCHAR(50) NOT NULL,
    task_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    input_data JSONB DEFAULT '{}',
    output_data JSONB,
    error_message TEXT,
    progress DECIMAL(5,2) DEFAULT 0.0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
    CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    CHECK (progress >= 0.0 AND progress <= 100.0)
);

-- Agent executions table
CREATE TABLE IF NOT EXISTS agent_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    agent_type VARCHAR(50) NOT NULL,
    task_id UUID NOT NULL REFERENCES agent_tasks(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'idle',
    execution_data JSONB DEFAULT '{}',
    logs TEXT[],
    metrics JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CHECK (status IN ('idle', 'running', 'paused', 'error', 'completed'))
);

-- Crew configurations table (for multi-agent workflows)
CREATE TABLE IF NOT EXISTS crew_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    agents VARCHAR(50)[] NOT NULL, -- Array of agent types
    workflow JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(organization_id, name)
);

-- Crew executions table
CREATE TABLE IF NOT EXISTS crew_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    crew_id UUID NOT NULL REFERENCES crew_configurations(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'idle',
    current_agent VARCHAR(50),
    execution_data JSONB DEFAULT '{}',
    agent_results JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CHECK (status IN ('idle', 'running', 'paused', 'error', 'completed'))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_agent_configurations_org_type ON agent_configurations(organization_id, agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_org_agent ON agent_tasks(organization_id, agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_status ON agent_tasks(status);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_created_at ON agent_tasks(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_executions_org_agent ON agent_executions(organization_id, agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_executions_task_id ON agent_executions(task_id);
CREATE INDEX IF NOT EXISTS idx_crew_configurations_org ON crew_configurations(organization_id);
CREATE INDEX IF NOT EXISTS idx_crew_executions_org ON crew_executions(organization_id);
CREATE INDEX IF NOT EXISTS idx_crew_executions_crew_id ON crew_executions(crew_id);

-- Row Level Security (RLS) policies
ALTER TABLE agent_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE crew_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE crew_executions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agent_configurations
CREATE POLICY "Users can view their organization's agent configurations" ON agent_configurations
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage their organization's agent configurations" ON agent_configurations
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid() AND role IN ('admin', 'owner')
        )
    );

-- RLS Policies for agent_tasks
CREATE POLICY "Users can view their organization's agent tasks" ON agent_tasks
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create agent tasks in their organization" ON agent_tasks
    FOR INSERT WITH CHECK (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "System can update agent tasks" ON agent_tasks
    FOR UPDATE USING (true);

-- RLS Policies for agent_executions
CREATE POLICY "Users can view their organization's agent executions" ON agent_executions
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "System can manage agent executions" ON agent_executions
    FOR ALL USING (true);

-- RLS Policies for crew_configurations
CREATE POLICY "Users can view their organization's crew configurations" ON crew_configurations
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage their organization's crew configurations" ON crew_configurations
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid() AND role IN ('admin', 'owner')
        )
    );

-- RLS Policies for crew_executions
CREATE POLICY "Users can view their organization's crew executions" ON crew_executions
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM user_organizations 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "System can manage crew executions" ON crew_executions
    FOR ALL USING (true);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_agent_configurations_updated_at 
    BEFORE UPDATE ON agent_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_tasks_updated_at 
    BEFORE UPDATE ON agent_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crew_configurations_updated_at 
    BEFORE UPDATE ON crew_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE agent_configurations IS 'Configuration settings for AI agents per organization';
COMMENT ON TABLE agent_tasks IS 'Individual tasks executed by AI agents';
COMMENT ON TABLE agent_executions IS 'Execution logs and metrics for agent tasks';
COMMENT ON TABLE crew_configurations IS 'Multi-agent workflow configurations';
COMMENT ON TABLE crew_executions IS 'Execution logs for multi-agent workflows';

COMMENT ON COLUMN agent_configurations.agent_type IS 'Type of agent (goal_understanding, strategic_planning, etc.)';
COMMENT ON COLUMN agent_configurations.settings IS 'JSON configuration specific to each agent type';
COMMENT ON COLUMN agent_tasks.task_type IS 'Specific task type within the agent category';
COMMENT ON COLUMN agent_tasks.input_data IS 'JSON input data for the task';
COMMENT ON COLUMN agent_tasks.output_data IS 'JSON output data from the task execution';
COMMENT ON COLUMN agent_executions.execution_data IS 'Runtime execution data and context';
COMMENT ON COLUMN agent_executions.logs IS 'Array of log messages from execution';
COMMENT ON COLUMN agent_executions.metrics IS 'Performance metrics and statistics';
COMMENT ON COLUMN crew_configurations.agents IS 'Array of agent types participating in the crew';
COMMENT ON COLUMN crew_configurations.workflow IS 'JSON workflow definition for agent coordination';
COMMENT ON COLUMN crew_executions.agent_results IS 'Results from each agent in the crew workflow';
