#!/usr/bin/env python3
"""
Test script for the simple workflow implementation
Tests the goal understanding agent and simple workflow manager
"""

import sys
import asyncio
import logging

# Add the app directory to the path
sys.path.append('.')

from app.modules.agents.simple_workflow import simple_workflow_manager
from app.modules.agents.goal_understanding import GoalUnderstandingAgent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_goal_understanding():
    """Test the goal understanding agent with simple input"""
    print("🧪 Testing Goal Understanding Agent...")
    
    try:
        # Test with simple string input
        goal = "I want to get 10 new restaurant clients in London within 3 months"
        
        agent = GoalUnderstandingAgent("test-org-001")
        result = await agent.execute(goal)
        
        print(f"✅ Goal Understanding Result:")
        print(f"   Confidence Score: {result.get('confidence_score', 0):.1%}")
        print(f"   Is Complete: {result.get('is_complete', False)}")
        print(f"   Parsed Goal: {result.get('parsed_goal', {}).get('objective', 'Not found')}")
        
        return result
        
    except Exception as e:
        print(f"❌ Goal Understanding Test Failed: {e}")
        return None


async def test_simple_workflow():
    """Test the simple workflow manager"""
    print("\n🧪 Testing Simple Workflow Manager...")
    
    try:
        goal = "I want to get 15 new SaaS clients in the US within 6 months"
        
        result = await simple_workflow_manager.start_from_goal(goal, "test-user-001")
        
        if result["success"]:
            print(f"✅ Simple Workflow Result:")
            print(f"   Success: {result['success']}")
            print(f"   Message: {result['message']}")
            
            workflow_info = result.get("workflow", {})
            if workflow_info:
                print(f"   Workflow ID: {workflow_info.get('workflow_id', 'Not found')}")
                print(f"   Campaign ID: {workflow_info.get('campaign_id', 'Not found')}")
            
            goal_result = result.get("goal_understanding", {})
            if goal_result:
                print(f"   Goal Confidence: {goal_result.get('confidence_score', 0):.1%}")
            
            return result
        else:
            print(f"❌ Simple Workflow Failed: {result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ Simple Workflow Test Failed: {e}")
        return None


async def test_goal_variations():
    """Test different goal variations"""
    print("\n🧪 Testing Goal Variations...")
    
    goals = [
        "Get 5 meetings with tech companies",
        "I need 20 leads for my consulting business in Europe",
        "Book 10 demos for our new software product",
        "Find customers for restaurant equipment in New York"
    ]
    
    for i, goal in enumerate(goals, 1):
        print(f"\n--- Test {i}: {goal} ---")
        
        try:
            agent = GoalUnderstandingAgent("test-org-001")
            result = await agent.execute(goal)
            
            confidence = result.get('confidence_score', 0)
            is_complete = result.get('is_complete', False)
            objective = result.get('parsed_goal', {}).get('objective', 'Not parsed')
            
            print(f"Confidence: {confidence:.1%} | Complete: {is_complete} | Objective: {objective}")
            
        except Exception as e:
            print(f"❌ Failed: {e}")


async def main():
    """Main test function"""
    print("🚀 Starting Simple Workflow Tests")
    print("=" * 50)
    
    # Test 1: Goal Understanding
    goal_result = await test_goal_understanding()
    
    # Test 2: Simple Workflow (only if goal understanding works)
    if goal_result:
        workflow_result = await test_simple_workflow()
    
    # Test 3: Goal Variations
    await test_goal_variations()
    
    print("\n" + "=" * 50)
    print("🏁 Tests Complete!")


if __name__ == "__main__":
    asyncio.run(main())
