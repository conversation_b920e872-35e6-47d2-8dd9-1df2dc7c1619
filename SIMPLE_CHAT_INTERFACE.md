# Simple Chat Interface for Selda AI

This document explains how to use the new simplified chat interface for Selda AI Sales Autopilot.

## Overview

The simple chat interface allows you to:
1. **Provide a sales goal in natural language**
2. **Automatically run the complete agent workflow**
3. **Get leads from Apollo.io and store them in the database**
4. **Execute all necessary sales automation actions**

## Usage Options

### 1. Command Line Interface (Python Script)

Run the interactive chat interface:

```bash
python simple_chat_interface.py
```

**Example interaction:**
```
🤖 Welcome to Selda AI Sales Autopilot!
==================================================
I'll help you create and execute your sales campaign.
Just tell me your sales goal in natural language.
Type 'quit' to exit.

You: I want to get 10 new restaurant clients in London within 3 months

🤖 Processing your goal: 'I want to get 10 new restaurant clients in London within 3 months'
==================================================
🚀 Starting complete workflow...
This will run all agents sequentially: goal understanding → planning → sourcing → content → email → analysis → booking → monitoring
✅ Goal understood with 85% confidence

📊 Parsed Goal:
   Objective: Generate 10 new restaurant clients in London
   Target Industry: Restaurant/Food Service
   Geography: London, UK

🎯 Strategic plan created
🔍 Found 87 potential leads from Apollo.io

✅ Complete workflow started!
📊 Workflow ID: workflow_campaign_abc123_1234567890
🎯 Campaign ID: campaign_abc123
📈 You can monitor progress and results in the dashboard.
```

### 2. API Endpoints

#### Start Workflow from Goal
```bash
POST /api/v1/agents/chat/simple/start
Authorization: Bearer {your_token}
Content-Type: application/json

{
  "goal": "I want to get 10 new restaurant clients in London within 3 months"
}
```

**Response:**
```json
{
  "success": true,
  "workflow_id": "workflow_campaign_abc123_1234567890",
  "campaign_id": "campaign_abc123",
  "message": "Workflow started successfully! Your sales campaign is now running.",
  "goal_understanding": {
    "parsed_goal": {
      "objective": "Generate 10 new restaurant clients in London",
      "target_audience": {
        "industry": "Restaurant/Food Service",
        "geography": "London, UK"
      }
    },
    "confidence_score": 0.85
  }
}
```

#### Check Workflow Status
```bash
GET /api/v1/agents/chat/workflow/{workflow_id}/status
Authorization: Bearer {your_token}
```

#### Get Goal Examples
```bash
GET /api/v1/agents/chat/goal/examples
```

## How It Works

### 1. Goal Understanding Agent
- Takes **only the goal string** as input
- Uses AI to parse and understand the sales objective
- Extracts target audience, timeline, and success metrics
- **More lenient completion criteria** for smoother workflow

### 2. Sequential Agent Workflow
The system automatically runs these agents in sequence:

1. **Goal Understanding** - Parse and understand the goal
2. **Strategic Planning** - Create campaign strategy
3. **Lead Sourcing** - Get leads from Apollo.io
4. **Lead Distribution** - Manage and distribute leads
5. **Content Creation** - Generate sales content
6. **Email Automation** - Set up email campaigns
7. **Reply Analysis** - Analyze responses
8. **Meeting Booking** - Schedule meetings
9. **Performance Monitoring** - Track results

### 3. Data Storage
- Leads are automatically stored in Supabase database
- 90-day domain contact cooldown is enforced
- Lead status tracking (unused/reserved/contacted/blacklisted)
- Campaign and workflow data is persisted

## Example Goals

Here are some example goals you can try:

- "I want to get 10 new restaurant clients in London within 3 months"
- "Generate 50 qualified leads for our SaaS platform targeting tech companies"
- "Book 15 sales meetings with healthcare companies in the US"
- "Find potential customers for our marketing agency in Europe"
- "Get 20 demo bookings for our new software product"
- "Generate leads for our consulting services targeting startups"
- "Find enterprise clients for our cybersecurity solution"
- "Book meetings with e-commerce companies for our logistics service"

## Testing

Run the test script to verify everything works:

```bash
python test_simple_workflow.py
```

This will test:
- Goal understanding with different inputs
- Simple workflow execution
- Various goal formats and structures

## Key Improvements

### ✅ What's New
- **Simple goal input** - Just provide a string, no complex JSON
- **Automatic workflow execution** - All agents run sequentially
- **Apollo.io integration** - Leads are sourced and stored automatically
- **Streamlined API** - Fewer endpoints, simpler usage
- **Better error handling** - More robust and user-friendly

### ❌ What's Removed
- Complex individual agent endpoints
- Manual agent chaining requirements
- Complicated input structures
- Confusing documentation

## Architecture

```
User Goal (String)
       ↓
Goal Understanding Agent
       ↓
Strategic Planning Agent
       ↓
Lead Sourcing Agent (Apollo.io)
       ↓
[Store in Supabase Database]
       ↓
Complete Sequential Workflow
       ↓
Results & Monitoring
```

## Error Handling

The system includes comprehensive error handling:
- Invalid goals are handled gracefully
- API failures are caught and reported
- Database errors don't crash the workflow
- Users get clear error messages

## Next Steps

After starting a workflow:
1. **Monitor progress** using the workflow status endpoint
2. **Check results** in the Supabase dashboard
3. **Review leads** that were sourced from Apollo.io
4. **Track campaign performance** through the monitoring agent

## Support

If you encounter issues:
1. Check the logs for detailed error messages
2. Verify your Apollo.io API credentials
3. Ensure Supabase connection is working
4. Run the test script to diagnose problems
