# Selda AI Sales Autopilot - API Quick Reference

## 🚀 Base URL

```
http://localhost:8000/api/v1
```

## 🔐 Authentication

### Register

```bash
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "StrongPass123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "organization_name": "My Company"
}
```

### Login

```bash
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "StrongPass123!"
}
# Returns: { "access_token": "...", "user": {...} }
```

## 🔄 Sequential Workflow (Main Feature)

### Start Complete Workflow

```bash
POST /campaigns/workflow/start
Authorization: Bearer {token}
{
  "campaign_id": "campaign-uuid",
  "target_meetings": 10,
  "target_contacts": 500,
  "target_audience": {
    "industry": "Technology",
    "location": "London",
    "company_size": "50-200"
  },
  "campaign_goals": {
    "objective": "Generate qualified leads",
    "value_proposition": "AI-powered automation"
  }
}
# Returns: { "workflow_id": "workflow_...", "status": "started" }
```

### Monitor Progress

```bash
GET /campaigns/workflow/{workflow_id}/status
Authorization: Bearer {token}
# Returns: { "status": "running", "progress_percentage": 45.5, "current_stage": "lead_sourcing" }
```

### Get Detailed Results

```bash
GET /campaigns/workflow/{workflow_id}/details
Authorization: Bearer {token}
# Returns: Complete workflow results with all stage outputs
```

## 💬 Simple Chat Interface (Recommended)

### Start Workflow from Goal

```bash
POST /agents/chat/simple/start
Authorization: Bearer {token}
{
  "goal": "I want to get 10 new restaurant clients in London within 3 months"
}
# Returns: { "success": true, "workflow_id": "...", "campaign_id": "...", "message": "..." }
```

### Check Workflow Status

```bash
GET /agents/chat/workflow/{workflow_id}/status
Authorization: Bearer {token}
# Returns: { "success": true, "status": {...} }
```

### Get Goal Examples

```bash
GET /agents/chat/goal/examples
# Returns: ["I want to get 10 new restaurant clients...", ...]
```

## 📋 Campaign Management

### Create Campaign

```bash
POST /campaigns
Authorization: Bearer {token}
{
  "name": "Q1 SaaS Outreach",
  "description": "Target SaaS companies",
  "target_audience": {
    "industry": "Technology",
    "location": "London"
  },
  "goals": {
    "meetings_target": 10,
    "contacts_target": 500
  }
}
# Returns: { "id": "campaign-uuid", "name": "...", "status": "active" }
```

### List Campaigns

```bash
GET /campaigns
Authorization: Bearer {token}
```

## 📊 Monitoring & Analytics

### List Organization Workflows

```bash
GET /campaigns/workflow/organization/workflows?limit=10&status_filter=completed
Authorization: Bearer {token}
```

### Lead Analytics

```bash
GET /leads/analytics?campaign_id=campaign-uuid
Authorization: Bearer {token}
```

## 🧪 Quick Test Commands

### Health Check

```bash
curl -X GET "http://localhost:8000/health"
```

### Complete Test Flow

```bash
# 1. Register & Login
TOKEN=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"StrongPass123!"}' \
  | jq -r '.access_token')

# 2. Create Campaign
CAMPAIGN_ID=$(curl -s -X POST "http://localhost:8000/api/v1/campaigns" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Campaign","target_audience":{"industry":"Technology"}}' \
  | jq -r '.id')

# 3. Start Workflow
WORKFLOW_ID=$(curl -s -X POST "http://localhost:8000/api/v1/campaigns/workflow/start" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{\"campaign_id\":\"$CAMPAIGN_ID\",\"target_meetings\":5,\"target_contacts\":100,\"target_audience\":{\"industry\":\"Technology\"},\"campaign_goals\":{\"objective\":\"Test\"}}" \
  | jq -r '.workflow_id')

# 4. Monitor Progress
curl -X GET "http://localhost:8000/api/v1/campaigns/workflow/$WORKFLOW_ID/status" \
  -H "Authorization: Bearer $TOKEN"
```

## 📈 Expected Outputs

### Workflow Status Response

```json
{
  "workflow_id": "workflow_campaign-uuid_1705320000",
  "status": "running",
  "current_stage": "lead_sourcing",
  "stages_completed": 3,
  "total_stages": 9,
  "execution_time_ms": 45000,
  "progress_percentage": 33.3
}
```

### Lead Sourcing Output

```json
{
  "leads_sourced": 87,
  "leads_by_source": { "apollo": 87 },
  "quality_distribution": { "high": 28, "medium": 41, "low": 18 },
  "duplicates_found": 13,
  "cooldown_filtered": 5,
  "sourcing_summary": {
    "execution_time_ms": 15000,
    "target_met": true
  }
}
```

### Lead Distribution Output

```json
{
  "leads_allocated": 50,
  "lead_ids": ["lead-uuid-1", "lead-uuid-2"],
  "quality_stats": {
    "average_score": 78.5,
    "score_distribution": { "high": 18, "medium": 25, "low": 7 }
  },
  "distribution_summary": {
    "fulfillment_rate": 100.0,
    "average_quality": 78.5
  }
}
```

## 🔧 Configuration

### Required Environment Variables

```env
APOLLO_API_KEY=your_apollo_api_key
SUPABASE_URL=https://aqjctgvrvtogryxlvwor.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_KEY=your_service_key
JWT_SECRET_KEY=your_jwt_secret
```

## 🚨 Common HTTP Status Codes

- **200**: Success
- **201**: Created (registration, campaign creation)
- **400**: Bad Request (invalid input)
- **401**: Unauthorized (invalid/missing token)
- **403**: Forbidden (access denied)
- **404**: Not Found (campaign/workflow not found)
- **422**: Validation Error (invalid data format)
- **500**: Internal Server Error

## 📱 Automated Testing

### Run Complete Test Suite

```bash
pip install requests
python test_selda_api.py
```

### Test Individual Components

```bash
# Test authentication only
python test_selda_api.py --auth-only

# Test specific agent
python test_selda_api.py --agent lead_sourcing

# Test workflow only
python test_selda_api.py --workflow-only
```

## 🎯 Key Features Summary

- ✅ **9-Stage Sequential Workflow**: Complete sales automation pipeline
- ✅ **Apollo.io Integration**: Primary lead sourcing from B2B database
- ✅ **Lead Quality Scoring**: 0-100 scoring algorithm
- ✅ **90-Day Cooldown**: Prevents domain over-contact
- ✅ **Real-time Monitoring**: Track workflow progress live
- ✅ **Agent Output Chaining**: Each agent uses previous output
- ✅ **Organization Isolation**: Secure multi-tenant architecture
- ✅ **Campaign Management**: Complete campaign lifecycle
- ✅ **Duplicate Prevention**: Smart filtering across campaigns
- ✅ **Quality Distribution**: Fair lead allocation system

This quick reference covers all essential endpoints and usage patterns for the Selda AI Sales Autopilot system!
