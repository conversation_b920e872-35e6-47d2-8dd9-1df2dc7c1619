"""
Sequential Agent Workflow Manager - Orchestrates agents according to Selda AI proposal
"""

import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from app.modules.agents.models import AgentType
from app.modules.agents.service import agent_service
from app.core.database import BaseRepository, supabase_service

logger = logging.getLogger(__name__)


class WorkflowStage(str, Enum):
    """Workflow stages according to Selda AI proposal"""
    GOAL_UNDERSTANDING = "goal_understanding"
    STRATEGIC_PLANNING = "strategic_planning"
    LEAD_SOURCING = "lead_sourcing"
    LEAD_DISTRIBUTION = "lead_distribution"
    CONTENT_CREATION = "content_creation"
    EMAIL_AUTOMATION = "email_automation"
    REPLY_ANALYSIS = "reply_analysis"
    MEETING_BOOKING = "meeting_booking"
    PERFORMANCE_MONITORING = "performance_monitoring"


class WorkflowInput(BaseModel):
    """Input for starting a workflow"""
    campaign_id: str = Field(..., description="Campaign ID")
    organization_id: str = Field(..., description="Organization ID")
    initial_input: Dict[str, Any] = Field(..., description="Initial workflow input")
    workflow_type: str = Field(default="sales_autopilot", description="Type of workflow")
    target_meetings: int = Field(default=10, description="Target number of meetings")
    target_contacts: int = Field(default=500, description="Target number of contacts")


class WorkflowStageResult(BaseModel):
    """Result from a workflow stage"""
    stage: WorkflowStage
    agent_type: AgentType
    status: str  # "success", "failed", "skipped"
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    execution_time_ms: int
    error_message: Optional[str] = None
    created_at: datetime


class WorkflowExecution(BaseModel):
    """Complete workflow execution tracking"""
    workflow_id: str
    campaign_id: str
    organization_id: str
    workflow_type: str
    status: str  # "running", "completed", "failed", "paused"
    current_stage: WorkflowStage
    stages_completed: List[WorkflowStageResult]
    total_execution_time_ms: int
    started_at: datetime
    completed_at: Optional[datetime] = None
    final_output: Optional[Dict[str, Any]] = None


class SequentialWorkflowManager:
    """Manages sequential execution of agents according to Selda AI proposal"""
    
    def __init__(self, db: Optional[Any] = None):
        self.db = db or supabase_service
        self.workflow_repo = BaseRepository("workflow_executions", self.db)
        self.stage_repo = BaseRepository("workflow_stage_results", self.db)
        
        # Define the sequential workflow according to proposal
        self.workflow_sequence = [
            (WorkflowStage.GOAL_UNDERSTANDING, AgentType.GOAL_UNDERSTANDING),
            (WorkflowStage.STRATEGIC_PLANNING, AgentType.STRATEGIC_PLANNING),
            (WorkflowStage.LEAD_SOURCING, AgentType.LEAD_SOURCING),
            (WorkflowStage.LEAD_DISTRIBUTION, AgentType.LEAD_DISTRIBUTION),
            (WorkflowStage.CONTENT_CREATION, AgentType.CONTENT_CREATION),
            (WorkflowStage.EMAIL_AUTOMATION, AgentType.EMAIL_AUTOMATION),
            (WorkflowStage.REPLY_ANALYSIS, AgentType.REPLY_ANALYSIS),
            (WorkflowStage.MEETING_BOOKING, AgentType.MEETING_BOOKING),
            (WorkflowStage.PERFORMANCE_MONITORING, AgentType.PERFORMANCE_MONITORING)
        ]
    
    async def start_workflow(self, workflow_input: WorkflowInput) -> str:
        """Start a new sequential workflow execution"""
        try:
            workflow_id = f"workflow_{workflow_input.campaign_id}_{int(datetime.utcnow().timestamp())}"
            
            # Create workflow execution record
            workflow_execution = WorkflowExecution(
                workflow_id=workflow_id,
                campaign_id=workflow_input.campaign_id,
                organization_id=workflow_input.organization_id,
                workflow_type=workflow_input.workflow_type,
                status="running",
                current_stage=WorkflowStage.GOAL_UNDERSTANDING,
                stages_completed=[],
                total_execution_time_ms=0,
                started_at=datetime.utcnow()
            )
            
            # Save to database
            await self.workflow_repo.create(workflow_execution.dict(), use_admin=True)
            
            # Execute workflow stages sequentially
            await self._execute_workflow_stages(workflow_execution, workflow_input)
            
            return workflow_id
            
        except Exception as e:
            logger.error(f"Failed to start workflow: {e}")
            raise
    
    async def _execute_workflow_stages(
        self, 
        workflow_execution: WorkflowExecution, 
        workflow_input: WorkflowInput
    ) -> None:
        """Execute all workflow stages sequentially"""
        
        current_input = workflow_input.initial_input
        total_start_time = datetime.utcnow()
        
        try:
            for stage, agent_type in self.workflow_sequence:
                stage_start_time = datetime.utcnow()
                
                logger.info(f"Executing stage {stage.value} with agent {agent_type.value}")
                
                try:
                    # Execute the agent
                    agent_output = await agent_service.execute_agent(
                        organization_id=workflow_execution.organization_id,
                        agent_type=agent_type,
                        input_data=current_input
                    )
                    
                    stage_execution_time = int((datetime.utcnow() - stage_start_time).total_seconds() * 1000)
                    
                    # Create stage result
                    stage_result = WorkflowStageResult(
                        stage=stage,
                        agent_type=agent_type,
                        status="success",
                        input_data=current_input,
                        output_data=agent_output,
                        execution_time_ms=stage_execution_time,
                        created_at=datetime.utcnow()
                    )
                    
                    # Save stage result
                    await self.stage_repo.create({
                        "workflow_id": workflow_execution.workflow_id,
                        "stage": stage.value,
                        "agent_type": agent_type.value,
                        "status": "success",
                        "input_data": current_input,
                        "output_data": agent_output,
                        "execution_time_ms": stage_execution_time,
                        "organization_id": workflow_execution.organization_id
                    }, use_admin=True)
                    
                    # Update workflow execution
                    workflow_execution.stages_completed.append(stage_result)
                    workflow_execution.current_stage = stage
                    
                    # Prepare input for next stage
                    if "next_agent_input" in agent_output:
                        current_input = agent_output["next_agent_input"]
                    else:
                        # Use the full output as input for next stage
                        current_input.update(agent_output)
                    
                    # Add campaign context to next input
                    current_input["campaign_id"] = workflow_execution.campaign_id
                    current_input["organization_id"] = workflow_execution.organization_id
                    
                    logger.info(f"Stage {stage.value} completed successfully")
                    
                except Exception as stage_error:
                    logger.error(f"Stage {stage.value} failed: {stage_error}")
                    
                    stage_execution_time = int((datetime.utcnow() - stage_start_time).total_seconds() * 1000)
                    
                    # Create failed stage result
                    stage_result = WorkflowStageResult(
                        stage=stage,
                        agent_type=agent_type,
                        status="failed",
                        input_data=current_input,
                        output_data={},
                        execution_time_ms=stage_execution_time,
                        error_message=str(stage_error),
                        created_at=datetime.utcnow()
                    )
                    
                    # Save failed stage result
                    await self.stage_repo.create({
                        "workflow_id": workflow_execution.workflow_id,
                        "stage": stage.value,
                        "agent_type": agent_type.value,
                        "status": "failed",
                        "input_data": current_input,
                        "output_data": {},
                        "execution_time_ms": stage_execution_time,
                        "error_message": str(stage_error),
                        "organization_id": workflow_execution.organization_id
                    }, use_admin=True)
                    
                    workflow_execution.stages_completed.append(stage_result)
                    
                    # Decide whether to continue or stop
                    if self._is_critical_stage(stage):
                        # Stop workflow for critical stage failures
                        workflow_execution.status = "failed"
                        break
                    else:
                        # Continue with next stage for non-critical failures
                        logger.warning(f"Non-critical stage {stage.value} failed, continuing workflow")
                        continue
            
            # Mark workflow as completed if we got through all stages
            if workflow_execution.status == "running":
                workflow_execution.status = "completed"
                workflow_execution.completed_at = datetime.utcnow()
                workflow_execution.final_output = current_input
            
            # Calculate total execution time
            total_execution_time = int((datetime.utcnow() - total_start_time).total_seconds() * 1000)
            workflow_execution.total_execution_time_ms = total_execution_time
            
            # Update workflow execution record
            await self.workflow_repo.update(
                workflow_execution.workflow_id,
                {
                    "status": workflow_execution.status,
                    "completed_at": workflow_execution.completed_at.isoformat() if workflow_execution.completed_at else None,
                    "total_execution_time_ms": total_execution_time,
                    "final_output": workflow_execution.final_output
                },
                use_admin=True
            )
            
            logger.info(f"Workflow {workflow_execution.workflow_id} completed with status: {workflow_execution.status}")
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            
            # Mark workflow as failed
            await self.workflow_repo.update(
                workflow_execution.workflow_id,
                {
                    "status": "failed",
                    "completed_at": datetime.utcnow().isoformat(),
                    "total_execution_time_ms": int((datetime.utcnow() - total_start_time).total_seconds() * 1000)
                },
                use_admin=True
            )
            raise
    
    def _is_critical_stage(self, stage: WorkflowStage) -> bool:
        """Determine if a stage is critical for workflow continuation"""
        critical_stages = {
            WorkflowStage.GOAL_UNDERSTANDING,
            WorkflowStage.STRATEGIC_PLANNING,
            WorkflowStage.LEAD_SOURCING
        }
        return stage in critical_stages
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[WorkflowExecution]:
        """Get current status of a workflow"""
        try:
            workflow_data = await self.workflow_repo.get_by_id(workflow_id, use_admin=True)
            if workflow_data:
                return WorkflowExecution(**workflow_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get workflow status: {e}")
            return None
    
    async def get_stage_results(self, workflow_id: str) -> List[WorkflowStageResult]:
        """Get all stage results for a workflow"""
        try:
            stage_data = await self.stage_repo.list(
                filters={"workflow_id": workflow_id},
                order_by="created_at",
                ascending=True,
                use_admin=True
            )
            
            return [WorkflowStageResult(**stage) for stage in stage_data]
        except Exception as e:
            logger.error(f"Failed to get stage results: {e}")
            return []


# Global instance
workflow_manager = SequentialWorkflowManager()
