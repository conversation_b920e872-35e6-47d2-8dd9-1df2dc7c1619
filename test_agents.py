#!/usr/bin/env python3
"""Test script to check agent imports and basic functionality"""

import sys
import traceback

def test_imports():
    """Test importing all agents"""
    print("🧪 Testing agent imports...")
    
    try:
        print("1. Testing base imports...")
        from app.modules.agents.base import SeldaAgent
        from app.modules.agents.models import AgentType
        print("   ✅ Base imports successful")
        
        print("2. Testing goal understanding agent...")
        from app.modules.agents.goal_understanding import GoalUnderstandingAgent
        print("   ✅ Goal Understanding Agent imported")
        
        print("3. Testing campaign planning agent...")
        from app.modules.agents.campaign_planning import CampaignPlanningAgent
        print("   ✅ Campaign Planning Agent imported")
        
        print("4. Testing lead sourcing agent...")
        from app.modules.agents.lead_sourcing import LeadSourcingAgent
        print("   ✅ Lead Sourcing Agent imported")
        
        print("5. Testing agent instantiation...")
        org_id = "test-org-123"
        
        # Test goal understanding agent
        goal_agent = GoalUnderstandingAgent(org_id)
        print(f"   ✅ Goal agent created: {goal_agent.name}")
        
        # Test campaign planning agent
        campaign_agent = CampaignPlanningAgent(org_id)
        print(f"   ✅ Campaign agent created: {campaign_agent.name}")
        
        # Test lead sourcing agent
        lead_agent = LeadSourcingAgent(org_id)
        print(f"   ✅ Lead agent created: {lead_agent.name}")
        
        print("\n🎉 All agent tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Agent test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection"""
    print("\n🧪 Testing database connection...")
    
    try:
        from app.core.database import BaseRepository
        
        # Test creating a repository
        repo = BaseRepository("organizations", None)
        print("   ✅ Repository created successfully")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Database test failed: {e}")
        traceback.print_exc()
        return False

def test_config():
    """Test configuration loading"""
    print("\n🧪 Testing configuration...")
    
    try:
        from app.core.config import settings
        
        print(f"   Database URL: {settings.database_url[:20]}...")
        print(f"   Apollo API Key: {settings.apollo_api_key[:10]}...")
        print("   ✅ Configuration loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Agent System Tests")
    print("="*50)
    
    # Run tests
    config_ok = test_config()
    db_ok = test_database_connection()
    agents_ok = test_imports()
    
    print("\n" + "="*50)
    print("📊 Test Results:")
    print(f"   Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"   Database: {'✅ PASS' if db_ok else '❌ FAIL'}")
    print(f"   Agents: {'✅ PASS' if agents_ok else '❌ FAIL'}")
    
    if all([config_ok, db_ok, agents_ok]):
        print("\n🎉 All tests passed! System is ready.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)
