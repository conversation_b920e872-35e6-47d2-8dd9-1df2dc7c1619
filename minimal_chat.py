#!/usr/bin/env python3
"""
Minimal chat interface for testing
"""

import sys
import asyncio
import uuid

# Add the app directory to the path
sys.path.append('.')

async def test_goal_processing():
    """Test goal processing with proper UUIDs"""
    try:
        print("🤖 Minimal Selda AI Test")
        print("=" * 30)
        
        # Generate proper UUIDs
        org_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        print(f"Organization ID: {org_id}")
        print(f"User ID: {user_id}")
        
        # Import goal understanding agent
        from app.modules.agents.goal_understanding import GoalUnderstandingAgent
        
        # Create agent
        agent = GoalUnderstandingAgent(org_id)
        print(f"✅ Agent created: {agent.name}")
        
        # Test goal
        goal = "I want 1 restaurant client in London"
        print(f"\n🎯 Processing goal: '{goal}'")
        
        # Execute agent
        result = await agent.execute({
            "goal_description": goal,
            "user_id": user_id,
            "organization_id": org_id,
            "conversation_history": [],
            "is_follow_up": False,
            "context": None,
            "target_audience": None,
            "timeline": None,
            "budget": None,
            "answered_questions": {}
        })
        
        print(f"\n✅ Goal processing successful!")
        print(f"   Confidence: {result.get('confidence_score', 0):.1%}")
        print(f"   Complete: {result.get('is_complete', False)}")
        
        parsed_goal = result.get('parsed_goal', {})
        print(f"   Objective: {parsed_goal.get('objective', 'Not specified')}")
        
        if parsed_goal.get('target_audience'):
            ta = parsed_goal['target_audience']
            print(f"   Industry: {ta.get('industry', 'Not specified')}")
            print(f"   Geography: {ta.get('geography', 'Not specified')}")
        
        # Check if we have next agent input
        next_input = result.get('next_agent_input')
        if next_input:
            print(f"✅ Ready for next agent!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    success = await test_goal_processing()
    
    if success:
        print(f"\n🎉 Test completed successfully!")
        print(f"The simple chat interface should work now.")
    else:
        print(f"\n❌ Test failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
